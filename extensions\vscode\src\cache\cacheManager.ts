import * as vscode from 'vscode';
import { ConfigurationManager } from '../config/configManager';

interface CachedCompletion {
    text: string;
    timestamp: number;
    model: string;
    language: string;
    usage: any;
}

export class CacheManager {
    private static readonly CACHE_KEY = 'tdsCoder.completionCache';
    private cache: Map<string, CachedCompletion> = new Map();
    private readonly maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours

    constructor(
        private context: vscode.ExtensionContext,
        private configManager: ConfigurationManager
    ) {
        this.loadCache();
    }

    async getCachedCompletion(prompt: string): Promise<CachedCompletion | null> {
        const cacheKey = this.generateCacheKey(prompt);
        const cached = this.cache.get(cacheKey);

        if (!cached) {
            return null;
        }

        // Check if cache entry is expired
        if (Date.now() - cached.timestamp > this.maxCacheAge) {
            this.cache.delete(cacheKey);
            await this.saveCache();
            return null;
        }

        return cached;
    }

    async cacheCompletion(prompt: string, completion: any): Promise<void> {
        const cacheKey = this.generateCacheKey(prompt);
        const maxSize = this.configManager.getCacheSize();

        // Remove oldest entries if cache is full
        if (this.cache.size >= maxSize) {
            const oldestKey = this.getOldestCacheKey();
            if (oldestKey) {
                this.cache.delete(oldestKey);
            }
        }

        const cachedCompletion: CachedCompletion = {
            text: completion.text,
            timestamp: Date.now(),
            model: completion.model,
            language: completion.language || 'unknown',
            usage: completion.usage
        };

        this.cache.set(cacheKey, cachedCompletion);
        await this.saveCache();
    }

    async clearCache(): Promise<void> {
        this.cache.clear();
        await this.saveCache();
    }

    getCacheStats(): { size: number; maxSize: number; oldestEntry: number | null } {
        let oldestTimestamp: number | null = null;
        
        for (const entry of this.cache.values()) {
            if (oldestTimestamp === null || entry.timestamp < oldestTimestamp) {
                oldestTimestamp = entry.timestamp;
            }
        }

        return {
            size: this.cache.size,
            maxSize: this.configManager.getCacheSize(),
            oldestEntry: oldestTimestamp
        };
    }

    async cleanExpiredEntries(): Promise<number> {
        const now = Date.now();
        let removedCount = 0;

        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.maxCacheAge) {
                this.cache.delete(key);
                removedCount++;
            }
        }

        if (removedCount > 0) {
            await this.saveCache();
        }

        return removedCount;
    }

    private generateCacheKey(prompt: string): string {
        // Create a hash of the prompt for cache key
        let hash = 0;
        for (let i = 0; i < prompt.length; i++) {
            const char = prompt.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }

    private getOldestCacheKey(): string | null {
        let oldestKey: string | null = null;
        let oldestTimestamp = Date.now();

        for (const [key, entry] of this.cache.entries()) {
            if (entry.timestamp < oldestTimestamp) {
                oldestTimestamp = entry.timestamp;
                oldestKey = key;
            }
        }

        return oldestKey;
    }

    private async loadCache(): Promise<void> {
        try {
            const cachedData = this.context.globalState.get<any>(CacheManager.CACHE_KEY);
            
            if (cachedData && Array.isArray(cachedData)) {
                this.cache = new Map(cachedData);
                
                // Clean expired entries on load
                await this.cleanExpiredEntries();
            }
        } catch (error) {
            console.error('Failed to load cache:', error);
            this.cache = new Map();
        }
    }

    private async saveCache(): Promise<void> {
        try {
            // Convert Map to array for storage
            const cacheArray = Array.from(this.cache.entries());
            await this.context.globalState.update(CacheManager.CACHE_KEY, cacheArray);
        } catch (error) {
            console.error('Failed to save cache:', error);
        }
    }

    dispose(): void {
        // Save cache before disposal
        this.saveCache().catch(error => {
            console.error('Failed to save cache on disposal:', error);
        });
    }
}
