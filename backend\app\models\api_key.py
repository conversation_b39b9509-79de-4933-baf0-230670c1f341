"""
API Key model for authentication and usage tracking.
"""

from sqlalchemy import <PERSON>um<PERSON>, String, Bo<PERSON><PERSON>, DateTime, ForeignKey, Text, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, timedelta
import secrets
import hashlib

from app.db.base import BaseModel


class APIKey(BaseModel):
    """API Key model for authentication and usage tracking."""
    
    __tablename__ = "api_keys"
    
    # Primary Key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # User Relationship
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Key Information
    name = Column(String(255), nullable=False)  # User-friendly name
    key_hash = Column(String(64), nullable=False, unique=True, index=True)  # SHA-256 hash
    key_prefix = Column(String(8), nullable=False, index=True)  # First 8 chars for display
    key_suffix = Column(String(4), nullable=False)  # Last 4 chars for display
    
    # Status and Permissions
    is_active = Column(Boolean, default=True, nullable=False)
    is_revoked = Column(Boolean, default=False, nullable=False)
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    revoked_reason = Column(String(255), nullable=True)
    
    # Expiration
    expires_at = Column(DateTime(timezone=True), nullable=True)
    never_expires = Column(Boolean, default=False, nullable=False)
    
    # Usage Tracking
    last_used = Column(DateTime(timezone=True), nullable=True)
    usage_count = Column(Integer, default=0, nullable=False)
    
    # Permissions and Scopes
    scopes = Column(Text, nullable=True)  # JSON array of allowed scopes
    allowed_ips = Column(Text, nullable=True)  # JSON array of allowed IP addresses
    
    # Rate Limiting (per key overrides)
    custom_daily_limit = Column(Integer, nullable=True)
    custom_burst_limit = Column(Integer, nullable=True)
    
    # Metadata
    description = Column(Text, nullable=True)
    created_by_ip = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="api_keys")
    usage_records = relationship("Usage", back_populates="api_key", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, name={self.name}, user_id={self.user_id})>"
    
    @classmethod
    def generate_key(cls) -> tuple[str, str]:
        """
        Generate a new API key and return both the raw key and its hash.
        Returns: (raw_key, key_hash)
        """
        # Generate a secure random key
        raw_key = f"tds_{secrets.token_urlsafe(32)}"
        
        # Create hash for storage
        key_hash = hashlib.sha256(raw_key.encode()).hexdigest()
        
        return raw_key, key_hash
    
    @classmethod
    def hash_key(cls, raw_key: str) -> str:
        """Hash a raw API key for storage."""
        return hashlib.sha256(raw_key.encode()).hexdigest()
    
    @property
    def is_expired(self) -> bool:
        """Check if the API key has expired."""
        if self.never_expires or not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if the API key is valid for use."""
        return (
            self.is_active
            and not self.is_revoked
            and not self.is_expired
            and not self.is_deleted
        )
    
    @property
    def masked_key(self) -> str:
        """Return a masked version of the key for display."""
        return f"{self.key_prefix}...{self.key_suffix}"
    
    @property
    def days_until_expiry(self) -> int:
        """Get number of days until key expires."""
        if self.never_expires or not self.expires_at:
            return -1  # Never expires
        
        delta = self.expires_at - datetime.utcnow()
        return max(0, delta.days)
    
    def revoke(self, reason: str = None):
        """Revoke the API key."""
        self.is_revoked = True
        self.is_active = False
        self.revoked_at = datetime.utcnow()
        self.revoked_reason = reason
    
    def update_last_used(self):
        """Update the last used timestamp and increment usage count."""
        self.last_used = datetime.utcnow()
        self.usage_count += 1
    
    def has_scope(self, scope: str) -> bool:
        """Check if the API key has a specific scope."""
        if not self.scopes:
            return True  # No scopes means full access
        
        import json
        try:
            scope_list = json.loads(self.scopes)
            return scope in scope_list or "*" in scope_list
        except (json.JSONDecodeError, TypeError):
            return True  # Default to full access if scopes are malformed
    
    def is_ip_allowed(self, ip_address: str) -> bool:
        """Check if an IP address is allowed to use this key."""
        if not self.allowed_ips:
            return True  # No IP restrictions
        
        import json
        try:
            ip_list = json.loads(self.allowed_ips)
            return ip_address in ip_list or "*" in ip_list
        except (json.JSONDecodeError, TypeError):
            return True  # Default to allow if IP list is malformed
    
    def get_rate_limits(self) -> dict:
        """Get rate limits for this API key."""
        return {
            "daily_limit": self.custom_daily_limit,
            "burst_limit": self.custom_burst_limit,
        }
