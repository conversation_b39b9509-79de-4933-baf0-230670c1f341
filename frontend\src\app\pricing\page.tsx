'use client'

import Link from 'next/link'
import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline'

const plans = [
  {
    name: 'Free',
    price: 0,
    description: 'Perfect for trying out TDS Coder',
    features: [
      '100 completions per day',
      'Basic code suggestions',
      'Community support',
      'VS Code extension',
      'Basic language support',
    ],
    limitations: [
      'No advanced AI models',
      'No priority support',
      'No team features',
      'Limited integrations',
    ],
    cta: 'Get Started',
    href: '/auth/register',
    popular: false,
  },
  {
    name: 'Solo',
    price: 19,
    description: 'For individual developers who want more',
    features: [
      '10,000 completions per day',
      'Advanced AI models',
      'All language support',
      'All IDE integrations',
      'Priority support',
      'Advanced code analysis',
      'Custom snippets',
      'Offline mode',
    ],
    limitations: [],
    cta: 'Start Free Trial',
    href: '/auth/register?plan=solo',
    popular: true,
  },
  {
    name: 'Team',
    price: 49,
    description: 'For teams that build together',
    features: [
      '50,000 completions per day',
      'Everything in Solo',
      'Team management',
      'Shared code snippets',
      'Usage analytics',
      'Admin controls',
      'SSO integration',
      'Dedicated support',
    ],
    limitations: [],
    cta: 'Start Free Trial',
    href: '/auth/register?plan=team',
    popular: false,
  },
  {
    name: 'Enterprise',
    price: null,
    description: 'For large organizations with custom needs',
    features: [
      'Unlimited completions',
      'Everything in Team',
      'On-premise deployment',
      'Custom AI models',
      'SLA guarantees',
      'Dedicated account manager',
      'Custom integrations',
      'Advanced security',
    ],
    limitations: [],
    cta: 'Contact Sales',
    href: '/contact',
    popular: false,
  },
]

const faqs = [
  {
    question: 'What happens when I exceed my daily limit?',
    answer: 'When you reach your daily completion limit, the service will pause until the next day. You can upgrade your plan at any time to get higher limits.',
  },
  {
    question: 'Can I cancel my subscription anytime?',
    answer: 'Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period.',
  },
  {
    question: 'Do you offer refunds?',
    answer: 'We offer a 30-day money-back guarantee for all paid plans. If you\'re not satisfied, contact us for a full refund.',
  },
  {
    question: 'Is my code data secure?',
    answer: 'Absolutely. We use end-to-end encryption and never store or train on your proprietary code. We\'re SOC 2 Type II and GDPR compliant.',
  },
  {
    question: 'Can I switch plans later?',
    answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately with prorated billing.',
  },
]

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Link href="/" className="text-xl font-bold text-gray-900 dark:text-white">
              TDS Coder
            </Link>
            <div className="space-x-4">
              <Link href="/auth/login" className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                Sign In
              </Link>
              <Link href="/auth/register" className="btn-primary px-4 py-2 rounded-md">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
              Simple, transparent pricing
            </h1>
            <p className="mt-4 text-xl text-gray-600 dark:text-gray-300">
              Choose the plan that's right for you. Start free, upgrade when you need more.
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="mt-16 grid grid-cols-1 gap-8 lg:grid-cols-4">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`relative rounded-lg border ${
                  plan.popular
                    ? 'border-indigo-500 shadow-lg'
                    : 'border-gray-200 dark:border-gray-700'
                } bg-white dark:bg-gray-800 p-8`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="inline-flex rounded-full bg-indigo-500 px-4 py-1 text-sm font-semibold text-white">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {plan.name}
                  </h3>
                  <div className="mt-4">
                    {plan.price === null ? (
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">
                        Custom
                      </span>
                    ) : (
                      <>
                        <span className="text-4xl font-bold text-gray-900 dark:text-white">
                          ${plan.price}
                        </span>
                        <span className="text-base font-medium text-gray-500 dark:text-gray-400">
                          /month
                        </span>
                      </>
                    )}
                  </div>
                  <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                    {plan.description}
                  </p>
                </div>

                <ul className="mt-8 space-y-3">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {feature}
                      </span>
                    </li>
                  ))}
                  {plan.limitations.map((limitation) => (
                    <li key={limitation} className="flex items-start">
                      <XMarkIcon className="h-5 w-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {limitation}
                      </span>
                    </li>
                  ))}
                </ul>

                <div className="mt-8">
                  <Link
                    href={plan.href}
                    className={`block w-full text-center px-4 py-2 rounded-md text-sm font-medium ${
                      plan.popular
                        ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600'
                    }`}
                  >
                    {plan.cta}
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* FAQ Section */}
          <div className="mt-24">
            <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">
              Frequently Asked Questions
            </h2>
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
              {faqs.map((faq, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="mt-24 text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Ready to supercharge your coding?
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
              Start with our free plan and upgrade when you need more power.
            </p>
            <div className="mt-8 space-x-4">
              <Link href="/auth/register" className="btn-primary px-6 py-3 rounded-md">
                Get Started Free
              </Link>
              <Link href="/contact" className="btn-secondary px-6 py-3 rounded-md">
                Contact Sales
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
