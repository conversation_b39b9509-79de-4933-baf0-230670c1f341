"""
Main API router that includes all endpoint routers.
"""

from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, users, subscriptions, api_keys, usage, webhooks, completions, analytics, teams, enterprise

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["subscriptions"])
api_router.include_router(api_keys.router, prefix="/api-keys", tags=["api-keys"])
api_router.include_router(usage.router, prefix="/usage", tags=["usage"])
api_router.include_router(webhooks.router, prefix="/webhooks", tags=["webhooks"])
api_router.include_router(completions.router, prefix="/v1", tags=["completions"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
api_router.include_router(teams.router, prefix="/teams", tags=["teams"])
api_router.include_router(enterprise.router, prefix="/enterprise", tags=["enterprise"])
