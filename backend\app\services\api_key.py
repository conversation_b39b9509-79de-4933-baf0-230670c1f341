"""
API key service for managing API keys and authentication.
"""

from typing import Optional, List, Tuple, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
import secrets
import hashlib
import json

from app.models.user import User
from app.models.api_key import APIKey
from app.models.subscription import Subscription
from app.models.usage import Usage
from app.core.logging import get_logger

logger = get_logger(__name__)


class APIKeyService:
    """Service for API key management and authentication."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_api_key(
        self,
        user: User,
        name: str,
        description: Optional[str] = None,
        expires_days: Optional[int] = None,
        scopes: Optional[List[str]] = None,
        allowed_ips: Optional[List[str]] = None,
        custom_daily_limit: Optional[int] = None,
        custom_burst_limit: Optional[int] = None,
    ) -> Tuple[APIKey, str]:
        """
        Create a new API key for the user.
        
        Args:
            user: User object
            name: API key name
            description: Optional description
            expires_days: Days until expiration (None for no expiration)
            scopes: List of allowed scopes
            allowed_ips: List of allowed IP addresses
            custom_daily_limit: Custom daily request limit
            custom_burst_limit: Custom burst limit
            
        Returns:
            Tuple of (APIKey object, raw key string)
        """
        try:
            # Generate API key
            raw_key = self._generate_api_key()
            key_hash = self._hash_api_key(raw_key)
            
            # Extract prefix and suffix for display
            key_prefix = raw_key[:8]
            key_suffix = raw_key[-4:]
            
            # Calculate expiration
            expires_at = None
            never_expires = True
            if expires_days:
                expires_at = datetime.utcnow() + timedelta(days=expires_days)
                never_expires = False
            
            # Create API key record
            api_key = APIKey(
                user_id=user.id,
                name=name,
                key_hash=key_hash,
                key_prefix=key_prefix,
                key_suffix=key_suffix,
                description=description,
                expires_at=expires_at,
                never_expires=never_expires,
                scopes=json.dumps(scopes) if scopes else None,
                allowed_ips=json.dumps(allowed_ips) if allowed_ips else None,
                custom_daily_limit=custom_daily_limit,
                custom_burst_limit=custom_burst_limit,
            )
            
            self.db.add(api_key)
            await self.db.commit()
            await self.db.refresh(api_key)
            
            logger.info("API key created", 
                       api_key_id=str(api_key.id),
                       user_id=str(user.id),
                       name=name)
            
            return api_key, raw_key
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create API key", error=str(e), user_id=str(user.id))
            raise
    
    async def get_user_api_keys(self, user: User) -> List[APIKey]:
        """Get all API keys for a user."""
        try:
            stmt = select(APIKey).where(
                APIKey.user_id == user.id,
                APIKey.is_deleted == False
            ).order_by(desc(APIKey.created_at))
            
            result = await self.db.execute(stmt)
            return result.scalars().all()
            
        except Exception as e:
            logger.error("Failed to get user API keys", error=str(e), user_id=str(user.id))
            raise
    
    async def get_api_key_by_id(self, key_id: str, user: User) -> Optional[APIKey]:
        """Get API key by ID for a specific user."""
        try:
            stmt = select(APIKey).where(
                APIKey.id == key_id,
                APIKey.user_id == user.id,
                APIKey.is_deleted == False
            )
            
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error("Failed to get API key by ID", error=str(e), key_id=key_id)
            raise
    
    async def get_api_key_by_hash(self, key_hash: str) -> Optional[APIKey]:
        """Get API key by hash for authentication."""
        try:
            stmt = select(APIKey).where(
                APIKey.key_hash == key_hash,
                APIKey.is_active == True,
                APIKey.is_revoked == False,
                APIKey.is_deleted == False
            )
            
            result = await self.db.execute(stmt)
            api_key = result.scalar_one_or_none()
            
            # Check if key is expired
            if api_key and not api_key.is_valid:
                return None
            
            return api_key
            
        except Exception as e:
            logger.error("Failed to get API key by hash", error=str(e))
            raise
    
    async def authenticate_api_key(self, raw_key: str) -> Optional[APIKey]:
        """
        Authenticate an API key and update last used timestamp.
        
        Args:
            raw_key: Raw API key string
            
        Returns:
            APIKey object if valid, None otherwise
        """
        try:
            key_hash = self._hash_api_key(raw_key)
            api_key = await self.get_api_key_by_hash(key_hash)
            
            if api_key:
                # Update last used timestamp
                api_key.last_used = datetime.utcnow()
                api_key.usage_count += 1
                await self.db.commit()
                
                logger.info("API key authenticated", 
                           api_key_id=str(api_key.id),
                           user_id=str(api_key.user_id))
            
            return api_key
            
        except Exception as e:
            logger.error("Failed to authenticate API key", error=str(e))
            return None
    
    async def update_api_key(self, api_key: APIKey, update_data: Any) -> APIKey:
        """Update API key settings."""
        try:
            # Update fields
            for field, value in update_data.dict(exclude_unset=True).items():
                if field in ['scopes', 'allowed_ips'] and value is not None:
                    setattr(api_key, field, json.dumps(value))
                else:
                    setattr(api_key, field, value)
            
            await self.db.commit()
            await self.db.refresh(api_key)
            
            logger.info("API key updated", api_key_id=str(api_key.id))
            return api_key
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to update API key", error=str(e), api_key_id=str(api_key.id))
            raise
    
    async def revoke_api_key(self, api_key: APIKey, reason: str = "Revoked by user") -> None:
        """Revoke an API key."""
        try:
            api_key.is_revoked = True
            api_key.is_active = False
            api_key.revoked_at = datetime.utcnow()
            api_key.revoked_reason = reason
            
            await self.db.commit()
            
            logger.info("API key revoked", 
                       api_key_id=str(api_key.id),
                       reason=reason)
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to revoke API key", error=str(e), api_key_id=str(api_key.id))
            raise
    
    async def get_user_subscription(self, user: User) -> Subscription:
        """Get user's subscription for quota checking."""
        stmt = select(Subscription).where(
            Subscription.user_id == user.id,
            Subscription.is_deleted == False
        )
        result = await self.db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            # Create default free subscription
            from app.models.subscription import SubscriptionTier, SubscriptionStatus
            subscription = Subscription(
                user_id=user.id,
                tier=SubscriptionTier.FREE,
                status=SubscriptionStatus.ACTIVE,
                daily_request_limit=100,
                burst_limit=1,
                seats_limit=1,
                currency="USD"
            )
            self.db.add(subscription)
            await self.db.commit()
            await self.db.refresh(subscription)
        
        return subscription
    
    async def check_rate_limit(self, api_key: APIKey, client_ip: str) -> Dict[str, Any]:
        """
        Check if API key has exceeded rate limits.
        
        Args:
            api_key: APIKey object
            client_ip: Client IP address
            
        Returns:
            Dictionary with rate limit status
        """
        try:
            # Get user's subscription
            user = await self.db.get(User, api_key.user_id)
            subscription = await self.get_user_subscription(user)
            
            # Determine limits (custom limits override subscription limits)
            daily_limit = api_key.custom_daily_limit or subscription.daily_request_limit
            burst_limit = api_key.custom_burst_limit or subscription.burst_limit
            
            # Check daily quota
            today = datetime.utcnow().date()
            daily_usage_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.api_key_id == api_key.id,
                    func.date(Usage.timestamp) == today,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            daily_result = await self.db.execute(daily_usage_stmt)
            daily_usage = daily_result.scalar() or 0
            
            # Check burst limit (requests in last minute)
            one_minute_ago = datetime.utcnow() - timedelta(minutes=1)
            burst_usage_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.api_key_id == api_key.id,
                    Usage.timestamp >= one_minute_ago,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            burst_result = await self.db.execute(burst_usage_stmt)
            burst_usage = burst_result.scalar() or 0
            
            # Check limits
            daily_exceeded = daily_limit != -1 and daily_usage >= daily_limit
            burst_exceeded = burst_limit != -1 and burst_usage >= burst_limit
            
            return {
                "allowed": not (daily_exceeded or burst_exceeded),
                "daily_limit": daily_limit,
                "daily_usage": daily_usage,
                "daily_remaining": max(0, daily_limit - daily_usage) if daily_limit != -1 else -1,
                "burst_limit": burst_limit,
                "burst_usage": burst_usage,
                "burst_remaining": max(0, burst_limit - burst_usage) if burst_limit != -1 else -1,
                "daily_exceeded": daily_exceeded,
                "burst_exceeded": burst_exceeded,
                "reset_time": datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1),
            }
            
        except Exception as e:
            logger.error("Failed to check rate limit", error=str(e), api_key_id=str(api_key.id))
            # Allow request on error to avoid blocking legitimate traffic
            return {"allowed": True, "error": str(e)}
    
    async def get_api_key_usage_stats(self, api_key: APIKey, days: int = 30) -> Dict[str, Any]:
        """Get usage statistics for an API key."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Total requests
            total_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.api_key_id == api_key.id,
                    Usage.timestamp >= start_date,
                    Usage.is_deleted == False
                )
            )
            total_result = await self.db.execute(total_stmt)
            total_requests = total_result.scalar() or 0
            
            # Successful requests
            success_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.api_key_id == api_key.id,
                    Usage.timestamp >= start_date,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            success_result = await self.db.execute(success_stmt)
            successful_requests = success_result.scalar() or 0
            
            # Error requests
            error_requests = total_requests - successful_requests
            
            # Average response time
            avg_time_stmt = select(func.avg(Usage.response_time_ms)).where(
                and_(
                    Usage.api_key_id == api_key.id,
                    Usage.timestamp >= start_date,
                    Usage.response_time_ms.isnot(None),
                    Usage.is_deleted == False
                )
            )
            avg_time_result = await self.db.execute(avg_time_stmt)
            avg_response_time = avg_time_result.scalar() or 0
            
            return {
                "period_days": days,
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "error_requests": error_requests,
                "success_rate": (successful_requests / total_requests * 100) if total_requests > 0 else 0,
                "average_response_time_ms": round(float(avg_response_time), 2) if avg_response_time else 0,
            }
            
        except Exception as e:
            logger.error("Failed to get usage stats", error=str(e), api_key_id=str(api_key.id))
            raise
    
    @staticmethod
    def _generate_api_key() -> str:
        """Generate a secure API key."""
        return f"tds_{secrets.token_urlsafe(32)}"
    
    @staticmethod
    def _hash_api_key(raw_key: str) -> str:
        """Hash an API key for secure storage."""
        return hashlib.sha256(raw_key.encode()).hexdigest()
