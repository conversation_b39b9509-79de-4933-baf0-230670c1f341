"""
Intelligent model selection service with fallback mechanisms and cost optimization.
"""

import asyncio
import random
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from dataclasses import dataclass
from enum import Enum

from app.models.user import User
from app.models.usage import Usage
from app.models.subscription import SubscriptionTier
from app.services.ai_models import AIModelService, ModelProvider
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class SelectionStrategy(str, Enum):
    """Model selection strategies."""
    COST_OPTIMIZED = "cost_optimized"
    PERFORMANCE_OPTIMIZED = "performance_optimized"
    BALANCED = "balanced"
    FASTEST = "fastest"
    MOST_CAPABLE = "most_capable"


@dataclass
class ModelScore:
    """Model scoring for selection."""
    model_id: str
    cost_score: float  # 0-1, lower is better
    performance_score: float  # 0-1, higher is better
    availability_score: float  # 0-1, higher is better
    context_score: float  # 0-1, higher is better for large contexts
    total_score: float
    reasoning: str


class IntelligentModelSelector:
    """Service for intelligent model selection with fallbacks and optimization."""
    
    def __init__(self, db: AsyncSession, ai_service: AIModelService):
        self.db = db
        self.ai_service = ai_service
        
        # Model performance data (would be updated from real usage analytics)
        self.performance_data = {
            "gpt-3.5-turbo": {"avg_response_time": 1.2, "success_rate": 0.98, "quality_score": 0.85},
            "gpt-4o": {"avg_response_time": 2.1, "success_rate": 0.99, "quality_score": 0.92},
            "gpt-4-turbo": {"avg_response_time": 3.5, "success_rate": 0.99, "quality_score": 0.95},
            "claude-3-haiku": {"avg_response_time": 0.8, "success_rate": 0.97, "quality_score": 0.82},
            "claude-3-sonnet": {"avg_response_time": 1.5, "success_rate": 0.98, "quality_score": 0.88},
            "claude-3.5-sonnet": {"avg_response_time": 1.8, "success_rate": 0.99, "quality_score": 0.94},
            "codestral": {"avg_response_time": 1.0, "success_rate": 0.98, "quality_score": 0.90},
            "deepseek-coder-v3": {"avg_response_time": 0.9, "success_rate": 0.97, "quality_score": 0.89},
            "gemini-1.5-flash": {"avg_response_time": 0.6, "success_rate": 0.96, "quality_score": 0.83},
            "gemini-1.5-pro": {"avg_response_time": 1.4, "success_rate": 0.98, "quality_score": 0.91},
        }
        
        # Provider health status (would be updated from monitoring)
        self.provider_health = {
            ModelProvider.OPENAI: {"status": "healthy", "latency": 1.2, "error_rate": 0.02},
            ModelProvider.ANTHROPIC: {"status": "healthy", "latency": 1.1, "error_rate": 0.01},
            ModelProvider.MISTRAL: {"status": "healthy", "latency": 1.0, "error_rate": 0.03},
            ModelProvider.DEEPSEEK: {"status": "healthy", "latency": 0.9, "error_rate": 0.02},
            ModelProvider.GOOGLE: {"status": "healthy", "latency": 0.8, "error_rate": 0.04},
        }
    
    async def select_optimal_model(
        self,
        user: User,
        task_type: str = "general",
        context_length: int = 0,
        strategy: SelectionStrategy = SelectionStrategy.BALANCED,
        preferred_models: Optional[List[str]] = None,
        budget_limit: Optional[float] = None
    ) -> Tuple[str, str]:
        """
        Select the optimal model for a given task.
        
        Args:
            user: User making the request
            task_type: Type of task (general, code_completion, chat, etc.)
            context_length: Length of context in tokens
            strategy: Selection strategy
            preferred_models: User's preferred models (if any)
            budget_limit: Maximum cost per request
            
        Returns:
            Tuple of (selected_model_id, reasoning)
        """
        try:
            # Get available models for user
            available_models = await self.ai_service.get_available_models(user)
            
            if not available_models:
                raise ValueError("No models available for user")
            
            # Filter by preferences if provided
            if preferred_models:
                available_models = [m for m in available_models if m in preferred_models]
            
            # Score all available models
            model_scores = []
            for model_id in available_models:
                score = await self._score_model(
                    model_id, task_type, context_length, strategy, budget_limit
                )
                if score:
                    model_scores.append(score)
            
            if not model_scores:
                # Fallback to first available model
                fallback_model = available_models[0]
                logger.warning("No suitable models found, using fallback", 
                             user_id=str(user.id), 
                             fallback_model=fallback_model)
                return fallback_model, "Fallback selection - no models met criteria"
            
            # Sort by total score (descending)
            model_scores.sort(key=lambda x: x.total_score, reverse=True)
            
            # Select top model with some randomization for load balancing
            if len(model_scores) > 1 and strategy == SelectionStrategy.BALANCED:
                # 70% chance for best model, 30% for second best
                if random.random() < 0.7:
                    selected = model_scores[0]
                else:
                    selected = model_scores[1] if len(model_scores) > 1 else model_scores[0]
            else:
                selected = model_scores[0]
            
            logger.info("Model selected", 
                       user_id=str(user.id),
                       selected_model=selected.model_id,
                       strategy=strategy.value,
                       score=selected.total_score,
                       reasoning=selected.reasoning)
            
            return selected.model_id, selected.reasoning
            
        except Exception as e:
            logger.error("Model selection failed", 
                        error=str(e), 
                        user_id=str(user.id))
            
            # Ultimate fallback
            fallback_model = settings.DEFAULT_MODEL
            return fallback_model, f"Error in selection, using default: {str(e)}"
    
    async def _score_model(
        self,
        model_id: str,
        task_type: str,
        context_length: int,
        strategy: SelectionStrategy,
        budget_limit: Optional[float]
    ) -> Optional[ModelScore]:
        """Score a model for selection."""
        try:
            model_config = self.ai_service.model_configs.get(model_id)
            if not model_config:
                return None
            
            # Cost scoring (0-1, lower is better)
            cost_per_1k = model_config.get("cost_per_1k_input_tokens", 0.01)
            estimated_cost = (context_length / 1000) * cost_per_1k
            
            if budget_limit and estimated_cost > budget_limit:
                return None  # Exceeds budget
            
            # Normalize cost score (assuming max cost of $0.1 per 1k tokens)
            cost_score = min(cost_per_1k / 0.1, 1.0)
            
            # Performance scoring (0-1, higher is better)
            perf_data = self.performance_data.get(model_id, {})
            quality_score = perf_data.get("quality_score", 0.5)
            success_rate = perf_data.get("success_rate", 0.9)
            response_time = perf_data.get("avg_response_time", 2.0)
            
            # Normalize response time (assuming max acceptable is 5 seconds)
            speed_score = max(0, 1 - (response_time / 5.0))
            performance_score = (quality_score * 0.5) + (success_rate * 0.3) + (speed_score * 0.2)
            
            # Availability scoring based on provider health
            provider = model_config.get("provider")
            provider_health = self.provider_health.get(provider, {})
            availability_score = 1.0 if provider_health.get("status") == "healthy" else 0.5
            availability_score *= (1 - provider_health.get("error_rate", 0.1))
            
            # Context scoring (0-1, higher is better for large contexts)
            max_context = model_config.get("context_window", 4096)
            if context_length > max_context:
                return None  # Context too large
            
            context_score = min(max_context / 200000, 1.0)  # Normalize to 200k tokens
            
            # Task-specific scoring
            best_for = model_config.get("best_for", [])
            task_bonus = 0.1 if task_type in best_for else 0.0
            
            # Calculate total score based on strategy
            if strategy == SelectionStrategy.COST_OPTIMIZED:
                total_score = (1 - cost_score) * 0.6 + performance_score * 0.3 + availability_score * 0.1
            elif strategy == SelectionStrategy.PERFORMANCE_OPTIMIZED:
                total_score = performance_score * 0.6 + availability_score * 0.3 + (1 - cost_score) * 0.1
            elif strategy == SelectionStrategy.FASTEST:
                total_score = speed_score * 0.7 + availability_score * 0.2 + (1 - cost_score) * 0.1
            elif strategy == SelectionStrategy.MOST_CAPABLE:
                total_score = quality_score * 0.5 + context_score * 0.3 + availability_score * 0.2
            else:  # BALANCED
                total_score = (performance_score * 0.4 + (1 - cost_score) * 0.3 + 
                             availability_score * 0.2 + context_score * 0.1)
            
            # Add task bonus
            total_score += task_bonus
            
            # Generate reasoning
            reasoning_parts = []
            if cost_score < 0.3:
                reasoning_parts.append("cost-effective")
            if performance_score > 0.8:
                reasoning_parts.append("high-performance")
            if context_score > 0.8:
                reasoning_parts.append("large-context")
            if task_bonus > 0:
                reasoning_parts.append(f"optimized-for-{task_type}")
            
            reasoning = f"Selected for: {', '.join(reasoning_parts) or 'balanced-performance'}"
            
            return ModelScore(
                model_id=model_id,
                cost_score=cost_score,
                performance_score=performance_score,
                availability_score=availability_score,
                context_score=context_score,
                total_score=total_score,
                reasoning=reasoning
            )
            
        except Exception as e:
            logger.error("Model scoring failed", error=str(e), model_id=model_id)
            return None
    
    async def get_fallback_models(
        self,
        user: User,
        primary_model: str,
        task_type: str = "general"
    ) -> List[str]:
        """Get fallback models for a primary model."""
        try:
            available_models = await self.ai_service.get_available_models(user)
            
            # Remove primary model from available models
            fallback_candidates = [m for m in available_models if m != primary_model]
            
            if not fallback_candidates:
                return []
            
            # Score fallback candidates
            fallback_scores = []
            for model_id in fallback_candidates:
                score = await self._score_model(
                    model_id, task_type, 0, SelectionStrategy.BALANCED, None
                )
                if score:
                    fallback_scores.append((model_id, score.total_score))
            
            # Sort by score and return top 3
            fallback_scores.sort(key=lambda x: x[1], reverse=True)
            return [model_id for model_id, _ in fallback_scores[:3]]
            
        except Exception as e:
            logger.error("Fallback model selection failed", 
                        error=str(e), 
                        user_id=str(user.id),
                        primary_model=primary_model)
            return []
    
    async def update_performance_data(
        self,
        model_id: str,
        response_time: float,
        success: bool,
        quality_rating: Optional[float] = None
    ):
        """Update performance data for a model."""
        try:
            if model_id not in self.performance_data:
                self.performance_data[model_id] = {
                    "avg_response_time": response_time,
                    "success_rate": 1.0 if success else 0.0,
                    "quality_score": quality_rating or 0.8
                }
            else:
                # Exponential moving average
                alpha = 0.1
                current = self.performance_data[model_id]
                
                current["avg_response_time"] = (
                    alpha * response_time + (1 - alpha) * current["avg_response_time"]
                )
                
                current["success_rate"] = (
                    alpha * (1.0 if success else 0.0) + (1 - alpha) * current["success_rate"]
                )
                
                if quality_rating:
                    current["quality_score"] = (
                        alpha * quality_rating + (1 - alpha) * current["quality_score"]
                    )
            
            logger.debug("Performance data updated", 
                        model_id=model_id,
                        response_time=response_time,
                        success=success)
            
        except Exception as e:
            logger.error("Failed to update performance data", 
                        error=str(e), 
                        model_id=model_id)
    
    async def get_cost_optimization_suggestions(
        self,
        user: User,
        usage_period_days: int = 30
    ) -> Dict[str, Any]:
        """Get cost optimization suggestions for a user."""
        try:
            # Get user's usage data
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=usage_period_days)
            
            stmt = select(Usage).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.timestamp <= end_date
                )
            )
            result = await self.db.execute(stmt)
            usage_records = result.scalars().all()
            
            if not usage_records:
                return {"suggestions": [], "potential_savings": 0}
            
            # Analyze usage patterns
            model_usage = {}
            total_cost = 0
            
            for usage in usage_records:
                model_id = usage.model_used
                if model_id not in model_usage:
                    model_usage[model_id] = {"count": 0, "total_tokens": 0, "total_cost": 0}
                
                model_usage[model_id]["count"] += 1
                model_usage[model_id]["total_tokens"] += usage.tokens_used
                model_usage[model_id]["total_cost"] += usage.cost_cents / 100
                total_cost += usage.cost_cents / 100
            
            suggestions = []
            potential_savings = 0
            
            # Suggest cheaper alternatives for high-usage expensive models
            for model_id, usage_data in model_usage.items():
                if usage_data["total_cost"] > total_cost * 0.3:  # Model accounts for >30% of costs
                    model_config = self.ai_service.model_configs.get(model_id)
                    if model_config:
                        # Find cheaper alternatives
                        available_models = await self.ai_service.get_available_models(user)
                        cheaper_alternatives = []
                        
                        current_cost = model_config.get("cost_per_1k_input_tokens", 0.01)
                        
                        for alt_model in available_models:
                            alt_config = self.ai_service.model_configs.get(alt_model)
                            if alt_config:
                                alt_cost = alt_config.get("cost_per_1k_input_tokens", 0.01)
                                if alt_cost < current_cost * 0.8:  # At least 20% cheaper
                                    savings = (current_cost - alt_cost) * (usage_data["total_tokens"] / 1000)
                                    cheaper_alternatives.append({
                                        "model": alt_model,
                                        "cost_per_1k": alt_cost,
                                        "potential_savings": savings
                                    })
                        
                        if cheaper_alternatives:
                            best_alternative = max(cheaper_alternatives, key=lambda x: x["potential_savings"])
                            suggestions.append({
                                "type": "model_substitution",
                                "current_model": model_id,
                                "suggested_model": best_alternative["model"],
                                "potential_savings": best_alternative["potential_savings"],
                                "reason": f"Switch from {model_id} to {best_alternative['model']} for similar tasks"
                            })
                            potential_savings += best_alternative["potential_savings"]
            
            return {
                "suggestions": suggestions,
                "potential_savings": potential_savings,
                "current_monthly_cost": total_cost,
                "analysis_period_days": usage_period_days
            }
            
        except Exception as e:
            logger.error("Cost optimization analysis failed", 
                        error=str(e), 
                        user_id=str(user.id))
            return {"suggestions": [], "potential_savings": 0}
