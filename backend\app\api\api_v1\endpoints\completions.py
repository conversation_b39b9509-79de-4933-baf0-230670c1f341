"""
AI code completion endpoints.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
import json
import asyncio

from app.db.session import get_db
from app.middleware.api_key_auth import require_api_key
from app.models.api_key import APIKey
from app.models.user import User
from app.services.ai_models import AIModelService, CompletionType
from app.services.cache import get_cache_service
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class CompletionRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=8000, description="Code prompt for completion")
    language: str = Field(default="python", description="Programming language")
    model: Optional[str] = Field(None, description="AI model to use (auto-select if not specified)")
    max_tokens: Optional[int] = Field(None, ge=1, le=4000, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="Sampling temperature")
    completion_type: CompletionType = Field(default=CompletionType.INLINE, description="Type of completion")
    stream: bool = Field(default=False, description="Whether to stream the response")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    task_type: Optional[str] = Field("general", description="Type of task for optimal model selection")
    fallback_enabled: Optional[bool] = Field(True, description="Enable fallback to alternative models")


class ChatMessage(BaseModel):
    role: str = Field(..., description="Message role (user, assistant, system)")
    content: str = Field(..., description="Message content")


class ChatRequest(BaseModel):
    messages: list[ChatMessage] = Field(..., min_items=1, description="Chat messages")
    model: Optional[str] = Field(None, description="AI model to use")
    max_tokens: Optional[int] = Field(None, ge=1, le=4000, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="Sampling temperature")
    stream: bool = Field(default=False, description="Whether to stream the response")


class CompletionResponse(BaseModel):
    id: str
    object: str = "text_completion"
    created: int
    model: str
    choices: list[Dict[str, Any]]
    usage: Dict[str, int]
    metadata: Dict[str, Any]


@router.post("/completions", response_model=CompletionResponse)
async def create_completion(
    request: CompletionRequest,
    api_key_user: tuple[APIKey, User] = Depends(require_api_key(["completions"])),
    db: AsyncSession = Depends(get_db),
    http_request: Request = None,
):
    """
    Create a code completion using AI models.
    
    This endpoint generates intelligent code completions based on the provided prompt.
    Supports multiple programming languages and AI models based on subscription tier.
    """
    api_key, user = api_key_user
    
    try:
        # Get cache service
        cache_service = await get_cache_service()
        
        # Initialize AI model service
        ai_service = AIModelService(db, cache_service)
        
        # Handle streaming response
        if request.stream:
            return StreamingResponse(
                _stream_completion_response(
                    ai_service, user, request
                ),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",  # Disable nginx buffering
                }
            )
        
        # Select optimal model if not specified
        selected_model = request.model
        if not selected_model:
            context_length = len(request.prompt.split())  # Rough token estimate
            selected_model = await ai_service.select_optimal_model_for_task(
                user=user,
                task_type=request.task_type,
                context_length=context_length,
                preferred_model=request.model
            )

        # Create completion with fallback handling
        result = None
        models_to_try = [selected_model]

        if request.fallback_enabled:
            # Get fallback models
            selector = ai_service._get_model_selector()
            fallback_models = await selector.get_fallback_models(
                user=user,
                primary_model=selected_model,
                task_type=request.task_type
            )
            models_to_try.extend(fallback_models)

        last_error = None
        for model_to_try in models_to_try:
            try:
                result = await ai_service.create_completion(
                    user=user,
                    prompt=request.prompt,
                    language=request.language,
                    model=model_to_try,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    completion_type=request.completion_type,
                    context=request.context,
                    stream=False,
                )
                break  # Success, exit loop
            except Exception as e:
                last_error = e
                logger.warning("Model failed, trying fallback",
                             model=model_to_try,
                             error=str(e),
                             user_id=str(user.id))
                continue

        if not result:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"All models failed. Last error: {str(last_error)}"
            )
        
        # Format response
        import time
        import uuid
        
        response = CompletionResponse(
            id=f"cmpl-{uuid.uuid4().hex[:8]}",
            created=int(time.time()),
            model=result["model"],
            choices=[{
                "text": result["text"],
                "index": 0,
                "finish_reason": result.get("finish_reason", "stop"),
                "logprobs": None,
            }],
            usage={
                "prompt_tokens": result["input_tokens"],
                "completion_tokens": result["output_tokens"],
                "total_tokens": result["total_tokens"],
            },
            metadata={
                "language": result["language"],
                "completion_type": result["completion_type"],
                "response_time_ms": result["response_time_ms"],
                "cost_cents": result["cost_cents"],
                "provider": result.get("provider"),
            }
        )
        
        # Add rate limit headers if available
        if hasattr(http_request.state, 'rate_limit_headers'):
            for header, value in http_request.state.rate_limit_headers.items():
                response.headers[header] = value
        
        logger.info("Completion created successfully", 
                   user_id=str(user.id),
                   api_key_id=str(api_key.id),
                   model=result["model"],
                   tokens=result["total_tokens"])
        
        return response
        
    except Exception as e:
        logger.error("Failed to create completion", 
                    error=str(e), 
                    user_id=str(user.id),
                    api_key_id=str(api_key.id))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate completion"
        )


@router.post("/chat/completions")
async def create_chat_completion(
    request: ChatRequest,
    api_key_user: tuple[APIKey, User] = Depends(require_api_key(["chat"])),
    db: AsyncSession = Depends(get_db),
    http_request: Request = None,
):
    """
    Create a chat completion for conversational AI assistance.
    
    This endpoint provides conversational AI assistance for coding questions,
    code explanations, debugging help, and general programming guidance.
    """
    api_key, user = api_key_user
    
    try:
        # Get cache service
        cache_service = await get_cache_service()
        
        # Initialize AI model service
        ai_service = AIModelService(db, cache_service)
        
        # Convert chat messages to prompt
        prompt = _format_chat_messages(request.messages)
        
        # Handle streaming response
        if request.stream:
            return StreamingResponse(
                _stream_chat_response(
                    ai_service, user, request, prompt
                ),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",
                }
            )
        
        # Create chat completion
        result = await ai_service.create_completion(
            user=user,
            prompt=prompt,
            language="text",  # Chat is language-agnostic
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            completion_type=CompletionType.CHAT,
            context={"chat_history": [msg.dict() for msg in request.messages]},
            stream=False,
        )
        
        # Format response
        import time
        import uuid
        
        response = {
            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": result["model"],
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": result["text"],
                },
                "finish_reason": result.get("finish_reason", "stop"),
            }],
            "usage": {
                "prompt_tokens": result["input_tokens"],
                "completion_tokens": result["output_tokens"],
                "total_tokens": result["total_tokens"],
            },
            "metadata": {
                "response_time_ms": result["response_time_ms"],
                "cost_cents": result["cost_cents"],
                "provider": result.get("provider"),
            }
        }
        
        logger.info("Chat completion created successfully", 
                   user_id=str(user.id),
                   api_key_id=str(api_key.id),
                   model=result["model"],
                   tokens=result["total_tokens"])
        
        return response
        
    except Exception as e:
        logger.error("Failed to create chat completion", 
                    error=str(e), 
                    user_id=str(user.id),
                    api_key_id=str(api_key.id))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate chat completion"
        )


@router.get("/models")
async def list_models(
    api_key_user: tuple[APIKey, User] = Depends(require_api_key()),
    db: AsyncSession = Depends(get_db),
):
    """
    List available AI models for the authenticated user.
    
    Returns models available based on the user's subscription tier.
    """
    api_key, user = api_key_user
    
    try:
        # Get cache service
        cache_service = await get_cache_service()
        
        # Initialize AI model service
        ai_service = AIModelService(db, cache_service)
        
        # Get available models
        available_models = await ai_service.get_available_models(user)
        
        # Format response
        models = []
        for model_name in available_models:
            model_config = ai_service.model_configs.get(model_name, {})
            models.append({
                "id": model_name,
                "object": "model",
                "created": int(time.time()),
                "owned_by": model_config.get("provider", "unknown"),
                "permission": [],
                "root": model_name,
                "parent": None,
                "max_tokens": model_config.get("max_tokens", 4096),
                "context_window": model_config.get("context_window", 4096),
                "cost_per_1k_tokens": model_config.get("cost_per_1k_tokens", 0.002),
            })
        
        return {
            "object": "list",
            "data": models
        }
        
    except Exception as e:
        logger.error("Failed to list models", 
                    error=str(e), 
                    user_id=str(user.id),
                    api_key_id=str(api_key.id))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available models"
        )


async def _stream_completion_response(
    ai_service: AIModelService,
    user: User,
    request: CompletionRequest,
):
    """Stream completion response in Server-Sent Events format."""
    import time
    import uuid

    completion_id = f"cmpl-{uuid.uuid4().hex[:8]}"
    created = int(time.time())

    try:
        async for chunk in ai_service.create_streaming_completion(
            user=user,
            prompt=request.prompt,
            language=request.language,
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            completion_type=request.completion_type,
            context=request.context,
        ):
            if "error" in chunk:
                # Send error and close stream
                error_data = {
                    "id": completion_id,
                    "object": "text_completion",
                    "created": created,
                    "model": request.model or "unknown",
                    "choices": [{
                        "text": "",
                        "index": 0,
                        "finish_reason": "error",
                        "error": chunk["error"],
                    }]
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "data: [DONE]\n\n"
                break

            # Send chunk
            chunk_data = {
                "id": completion_id,
                "object": "text_completion",
                "created": created,
                "model": chunk.get("model", request.model or "unknown"),
                "choices": [{
                    "text": chunk.get("text", ""),
                    "index": 0,
                    "finish_reason": chunk.get("finish_reason"),
                }]
            }

            yield f"data: {json.dumps(chunk_data)}\n\n"

            # End stream if completion is finished
            if chunk.get("finish_reason"):
                yield "data: [DONE]\n\n"
                break

    except Exception as e:
        logger.error("Streaming completion failed", error=str(e), user_id=str(user.id))
        error_data = {
            "id": completion_id,
            "object": "text_completion",
            "created": created,
            "model": request.model or "unknown",
            "choices": [{
                "text": "",
                "index": 0,
                "finish_reason": "error",
                "error": str(e),
            }]
        }
        yield f"data: {json.dumps(error_data)}\n\n"
        yield "data: [DONE]\n\n"


async def _stream_chat_response(
    ai_service: AIModelService,
    user: User,
    request: ChatRequest,
    prompt: str,
):
    """Stream chat completion response in Server-Sent Events format."""
    import time
    import uuid

    completion_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
    created = int(time.time())

    try:
        async for chunk in ai_service.create_streaming_completion(
            user=user,
            prompt=prompt,
            language="text",
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            completion_type=CompletionType.CHAT,
            context={"chat_history": [msg.dict() for msg in request.messages]},
        ):
            if "error" in chunk:
                # Send error and close stream
                error_data = {
                    "id": completion_id,
                    "object": "chat.completion.chunk",
                    "created": created,
                    "model": request.model or "unknown",
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": "error",
                        "error": chunk["error"],
                    }]
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "data: [DONE]\n\n"
                break

            # Send chunk
            chunk_data = {
                "id": completion_id,
                "object": "chat.completion.chunk",
                "created": created,
                "model": chunk.get("model", request.model or "unknown"),
                "choices": [{
                    "index": 0,
                    "delta": {
                        "content": chunk.get("text", "")
                    } if chunk.get("text") else {},
                    "finish_reason": chunk.get("finish_reason"),
                }]
            }

            yield f"data: {json.dumps(chunk_data)}\n\n"

            # End stream if completion is finished
            if chunk.get("finish_reason"):
                yield "data: [DONE]\n\n"
                break

    except Exception as e:
        logger.error("Streaming chat completion failed", error=str(e), user_id=str(user.id))
        error_data = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created,
            "model": request.model or "unknown",
            "choices": [{
                "index": 0,
                "delta": {},
                "finish_reason": "error",
                "error": str(e),
            }]
        }
        yield f"data: {json.dumps(error_data)}\n\n"
        yield "data: [DONE]\n\n"


def _format_chat_messages(messages: list[ChatMessage]) -> str:
    """Format chat messages into a single prompt."""
    formatted_messages = []

    for message in messages:
        role = message.role.upper()
        content = message.content.strip()
        formatted_messages.append(f"{role}: {content}")

    # Add assistant prompt
    formatted_messages.append("ASSISTANT:")

    return "\n\n".join(formatted_messages)


@router.get("/models")
async def get_available_models(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get available AI models for the current user.

    Returns models available based on the user's subscription tier,
    along with model information and capabilities.
    """
    try:
        # Initialize AI model service
        ai_service = AIModelService(db)

        # Get available models for user
        available_models = await ai_service.get_available_models(current_user)

        # Get detailed model information
        model_details = []
        for model_id in available_models:
            config = ai_service.model_configs.get(model_id)
            if config:
                model_details.append({
                    "id": model_id,
                    "name": model_id.replace("-", " ").title(),
                    "provider": config["provider"].value,
                    "max_tokens": config["max_tokens"],
                    "context_window": config["context_window"],
                    "cost_per_1k_input_tokens": config.get("cost_per_1k_input_tokens", 0),
                    "cost_per_1k_output_tokens": config.get("cost_per_1k_output_tokens", 0),
                    "supports_streaming": config.get("supports_streaming", False),
                    "supports_function_calling": config.get("supports_function_calling", False),
                    "best_for": config.get("best_for", []),
                })

        return {
            "models": model_details,
            "default_model": settings.DEFAULT_MODEL,
            "user_tier": current_user.subscription.tier.value if current_user.subscription else "free"
        }

    except Exception as e:
        logger.error("Failed to get available models",
                    error=str(e),
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available models"
        )


@router.get("/models/recommendations")
async def get_model_recommendations(
    task_type: str = Query("general", description="Type of task"),
    context_length: int = Query(0, description="Estimated context length"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get model recommendations for a specific task.

    Returns recommended models based on task type, context length,
    user's subscription tier, and current model performance.
    """
    try:
        # Initialize AI model service
        ai_service = AIModelService(db)

        # Get optimal model recommendation
        recommended_model = await ai_service.select_optimal_model_for_task(
            user=current_user,
            task_type=task_type,
            context_length=context_length
        )

        # Get fallback models
        selector = ai_service._get_model_selector()
        fallback_models = await selector.get_fallback_models(
            user=current_user,
            primary_model=recommended_model,
            task_type=task_type
        )

        # Get cost optimization suggestions
        cost_suggestions = await selector.get_cost_optimization_suggestions(
            user=current_user,
            usage_period_days=30
        )

        return {
            "recommended_model": recommended_model,
            "fallback_models": fallback_models,
            "cost_optimization": cost_suggestions,
            "task_type": task_type,
            "context_length": context_length
        }

    except Exception as e:
        logger.error("Failed to get model recommendations",
                    error=str(e),
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get model recommendations"
        )
