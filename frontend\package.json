{"name": "tds-coder-frontend", "version": "1.0.0", "description": "TDS Coder Frontend - AI-Powered Code Completion SaaS Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "swr": "^2.2.4", "react-query": "^3.39.3", "@stripe/stripe-js": "^2.2.2", "@stripe/react-stripe-js": "^2.4.0", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-datepicker": "^4.25.0", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "prismjs": "^1.29.0", "react-copy-to-clipboard": "^5.1.0", "@types/react-copy-to-clipboard": "^5.0.7", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "react-use": "^17.4.2", "use-debounce": "^10.0.0", "react-intersection-observer": "^9.5.3", "next-themes": "^0.2.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-qr-code": "^2.0.12"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.11", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}