-- TDS Coder Database Initialization Script
-- This script sets up the initial database structure and extensions

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- <PERSON><PERSON> custom types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('user', 'admin', 'super_admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE subscription_status AS ENUM ('active', 'inactive', 'canceled', 'past_due', 'unpaid', 'trialing', 'paused');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE subscription_tier AS ENUM ('free', 'solo', 'team', 'enterprise');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
-- These will be created by Alembic migrations, but we can prepare the database

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a function to generate API key prefixes
CREATE OR REPLACE FUNCTION generate_api_key_prefix()
RETURNS TEXT AS $$
BEGIN
    RETURN 'tds_' || encode(gen_random_bytes(4), 'hex');
END;
$$ language 'plpgsql';

-- Create a function to mask sensitive data for logging
CREATE OR REPLACE FUNCTION mask_sensitive_data(input_text TEXT, visible_chars INTEGER DEFAULT 4)
RETURNS TEXT AS $$
BEGIN
    IF input_text IS NULL OR LENGTH(input_text) <= visible_chars THEN
        RETURN input_text;
    END IF;
    
    RETURN LEFT(input_text, visible_chars) || 
           REPEAT('*', GREATEST(0, LENGTH(input_text) - visible_chars * 2)) || 
           RIGHT(input_text, visible_chars);
END;
$$ language 'plpgsql';

-- Create a function to calculate subscription tier limits
CREATE OR REPLACE FUNCTION get_tier_limits(tier subscription_tier)
RETURNS JSON AS $$
BEGIN
    CASE tier
        WHEN 'free' THEN
            RETURN '{"daily_requests": 100, "burst_limit": 1, "seats": 1}'::json;
        WHEN 'solo' THEN
            RETURN '{"daily_requests": 10000, "burst_limit": 10, "seats": 1}'::json;
        WHEN 'team' THEN
            RETURN '{"daily_requests": 50000, "burst_limit": 50, "seats": 5}'::json;
        WHEN 'enterprise' THEN
            RETURN '{"daily_requests": -1, "burst_limit": 100, "seats": -1}'::json;
        ELSE
            RETURN '{"daily_requests": 100, "burst_limit": 1, "seats": 1}'::json;
    END CASE;
END;
$$ language 'plpgsql';

-- Create a function to check if a user has exceeded their daily quota
CREATE OR REPLACE FUNCTION check_daily_quota(user_uuid UUID, request_date DATE DEFAULT CURRENT_DATE)
RETURNS BOOLEAN AS $$
DECLARE
    daily_limit INTEGER;
    current_usage INTEGER;
    user_tier subscription_tier;
BEGIN
    -- Get user's subscription tier
    SELECT s.tier INTO user_tier
    FROM subscriptions s
    JOIN users u ON u.id = s.user_id
    WHERE u.id = user_uuid AND s.status = 'active';
    
    -- If no active subscription, default to free tier
    IF user_tier IS NULL THEN
        user_tier := 'free';
    END IF;
    
    -- Get daily limit for the tier
    daily_limit := (get_tier_limits(user_tier)->>'daily_requests')::INTEGER;
    
    -- If unlimited (-1), return false (not exceeded)
    IF daily_limit = -1 THEN
        RETURN FALSE;
    END IF;
    
    -- Count current usage for the day
    SELECT COUNT(*) INTO current_usage
    FROM usage
    WHERE user_id = user_uuid 
    AND DATE(timestamp) = request_date
    AND status_code < 400; -- Only count successful requests
    
    -- Return true if quota exceeded
    RETURN current_usage >= daily_limit;
END;
$$ language 'plpgsql';

-- Create indexes for common query patterns
-- Note: These will be properly created by Alembic migrations

-- Grant necessary permissions
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tds_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tds_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO tds_user;

-- Insert default data (will be handled by application)
-- This is just for reference

COMMENT ON DATABASE tds_coder IS 'TDS Coder - AI-Powered Code Completion SaaS Platform';
COMMENT ON EXTENSION "uuid-ossp" IS 'UUID generation functions';
COMMENT ON EXTENSION "pgcrypto" IS 'Cryptographic functions for password hashing';
COMMENT ON EXTENSION "pg_stat_statements" IS 'Query performance monitoring';

-- Log the initialization
DO $$
BEGIN
    RAISE NOTICE 'TDS Coder database initialized successfully at %', NOW();
END $$;
