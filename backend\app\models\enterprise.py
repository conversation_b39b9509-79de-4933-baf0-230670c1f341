"""
Enterprise security and deployment models for SSO, audit logging, and advanced security features.
"""

from sqlalchemy import Column, String, Integer, DateTime, Boolean, Text, ForeignKey, Index, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from enum import Enum

from app.db.base_class import Base


class SSOProvider(str, Enum):
    """Supported SSO providers."""
    SAML = "saml"
    OIDC = "oidc"
    OAUTH2 = "oauth2"
    LDAP = "ldap"


class AuditEventType(str, Enum):
    """Types of audit events."""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_CREATED = "user_created"
    USER_UPDATED = "user_updated"
    USER_DELETED = "user_deleted"
    PASSWORD_CHANGED = "password_changed"
    API_KEY_CREATED = "api_key_created"
    API_KEY_DELETED = "api_key_deleted"
    SUBSCRIPTION_CHANGED = "subscription_changed"
    TEAM_CREATED = "team_created"
    TEAM_MEMBER_ADDED = "team_member_added"
    TEAM_MEMBER_REMOVED = "team_member_removed"
    DATA_EXPORT = "data_export"
    ADMIN_ACTION = "admin_action"
    SECURITY_VIOLATION = "security_violation"
    SYSTEM_CONFIG_CHANGED = "system_config_changed"


class ComplianceFramework(str, Enum):
    """Compliance frameworks."""
    SOC2 = "soc2"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    ISO27001 = "iso27001"
    PCI_DSS = "pci_dss"


class SSOConfiguration(Base):
    """SSO provider configurations for enterprise authentication."""
    __tablename__ = "sso_configurations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    
    name = Column(String(255), nullable=False)
    provider_type = Column(SQLEnum(SSOProvider), nullable=False)
    
    # Provider configuration
    provider_config = Column(JSONB, nullable=False)  # Provider-specific settings
    metadata_url = Column(String(500), nullable=True)  # For SAML/OIDC metadata
    
    # Mapping configuration
    attribute_mapping = Column(JSONB, nullable=False)  # Map provider attributes to user fields
    role_mapping = Column(JSONB, nullable=True)  # Map provider roles to system roles
    
    # Security settings
    require_signed_assertions = Column(Boolean, default=True, nullable=False)
    encrypt_assertions = Column(Boolean, default=False, nullable=False)
    force_authn = Column(Boolean, default=False, nullable=False)
    
    # Status and metadata
    is_active = Column(Boolean, default=True, nullable=False)
    is_default = Column(Boolean, default=False, nullable=False)
    
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    organization = relationship("Organization", back_populates="sso_configurations")
    created_by = relationship("User")

    # Indexes
    __table_args__ = (
        Index('idx_sso_configurations_org_id', 'organization_id'),
        Index('idx_sso_configurations_provider_type', 'provider_type'),
        Index('idx_sso_configurations_is_active', 'is_active'),
    )


class Organization(Base):
    """Enterprise organizations for multi-tenant deployment."""
    __tablename__ = "organizations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    slug = Column(String(100), nullable=False, unique=True)
    domain = Column(String(255), nullable=True)  # Primary domain for auto-assignment
    
    # Enterprise settings
    max_users = Column(Integer, nullable=True)  # User limit
    max_teams = Column(Integer, nullable=True)  # Team limit
    
    # Security settings
    enforce_sso = Column(Boolean, default=False, nullable=False)
    require_2fa = Column(Boolean, default=False, nullable=False)
    allowed_ip_ranges = Column(JSONB, nullable=True)  # CIDR ranges
    session_timeout_minutes = Column(Integer, default=480, nullable=False)  # 8 hours
    
    # Data residency
    data_region = Column(String(50), nullable=True)  # us-east-1, eu-west-1, etc.
    data_retention_days = Column(Integer, default=365, nullable=False)
    
    # Compliance
    compliance_frameworks = Column(JSONB, nullable=True)  # List of frameworks
    
    # Billing
    billing_email = Column(String(255), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    sso_configurations = relationship("SSOConfiguration", back_populates="organization")
    audit_logs = relationship("AuditLog", back_populates="organization")
    users = relationship("User", back_populates="organization")

    # Indexes
    __table_args__ = (
        Index('idx_organizations_slug', 'slug'),
        Index('idx_organizations_domain', 'domain'),
        Index('idx_organizations_is_active', 'is_active'),
    )


class AuditLog(Base):
    """Comprehensive audit logging for compliance and security."""
    __tablename__ = "audit_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=True)
    
    # Event details
    event_type = Column(SQLEnum(AuditEventType), nullable=False)
    event_category = Column(String(50), nullable=False)  # authentication, authorization, data_access, etc.
    event_description = Column(Text, nullable=False)
    
    # Actor information
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    user_email = Column(String(255), nullable=True)  # For deleted users
    actor_type = Column(String(50), nullable=False)  # user, system, api
    
    # Target information
    target_type = Column(String(50), nullable=True)  # user, team, api_key, etc.
    target_id = Column(String(255), nullable=True)
    target_name = Column(String(255), nullable=True)
    
    # Request context
    ip_address = Column(INET, nullable=True)
    user_agent = Column(Text, nullable=True)
    request_id = Column(String(255), nullable=True)
    session_id = Column(String(255), nullable=True)
    
    # Additional metadata
    metadata = Column(JSONB, nullable=True)
    
    # Risk assessment
    risk_score = Column(Integer, nullable=True)  # 0-100
    risk_factors = Column(JSONB, nullable=True)
    
    # Compliance tags
    compliance_tags = Column(JSONB, nullable=True)
    
    # Timestamp
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Relationships
    organization = relationship("Organization", back_populates="audit_logs")
    user = relationship("User")

    # Indexes
    __table_args__ = (
        Index('idx_audit_logs_org_id', 'organization_id'),
        Index('idx_audit_logs_user_id', 'user_id'),
        Index('idx_audit_logs_event_type', 'event_type'),
        Index('idx_audit_logs_timestamp', 'timestamp'),
        Index('idx_audit_logs_ip_address', 'ip_address'),
        Index('idx_audit_logs_risk_score', 'risk_score'),
    )


class SecurityPolicy(Base):
    """Security policies for enterprise deployments."""
    __tablename__ = "security_policies"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    policy_type = Column(String(50), nullable=False)  # password, access, data, etc.
    
    # Policy configuration
    policy_config = Column(JSONB, nullable=False)
    
    # Enforcement
    is_active = Column(Boolean, default=True, nullable=False)
    enforcement_level = Column(String(20), default="enforce", nullable=False)  # enforce, warn, monitor
    
    # Scope
    applies_to_users = Column(Boolean, default=True, nullable=False)
    applies_to_api_keys = Column(Boolean, default=True, nullable=False)
    applies_to_teams = Column(Boolean, default=True, nullable=False)
    
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    organization = relationship("Organization")
    created_by = relationship("User")

    # Indexes
    __table_args__ = (
        Index('idx_security_policies_org_id', 'organization_id'),
        Index('idx_security_policies_policy_type', 'policy_type'),
        Index('idx_security_policies_is_active', 'is_active'),
    )


class DataExportRequest(Base):
    """Track data export requests for compliance."""
    __tablename__ = "data_export_requests"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Request details
    export_type = Column(String(50), nullable=False)  # user_data, audit_logs, usage_data
    data_types = Column(JSONB, nullable=False)  # List of data types to export
    date_range_start = Column(DateTime, nullable=True)
    date_range_end = Column(DateTime, nullable=True)
    
    # Processing
    status = Column(String(20), default="pending", nullable=False)  # pending, processing, completed, failed
    file_path = Column(String(500), nullable=True)  # Path to generated export file
    file_size_bytes = Column(Integer, nullable=True)
    
    # Security
    encryption_key_id = Column(String(255), nullable=True)
    download_token = Column(String(255), nullable=True)
    expires_at = Column(DateTime, nullable=True)
    downloaded_at = Column(DateTime, nullable=True)
    
    # Metadata
    reason = Column(Text, nullable=True)  # Reason for export
    approved_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    approved_at = Column(DateTime, nullable=True)
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    organization = relationship("Organization")
    user = relationship("User", foreign_keys=[user_id])
    approved_by = relationship("User", foreign_keys=[approved_by_id])

    # Indexes
    __table_args__ = (
        Index('idx_data_export_requests_org_id', 'organization_id'),
        Index('idx_data_export_requests_user_id', 'user_id'),
        Index('idx_data_export_requests_status', 'status'),
        Index('idx_data_export_requests_created_at', 'created_at'),
    )
