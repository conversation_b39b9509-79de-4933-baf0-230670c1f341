# TDS Coder Development Setup Script for Windows PowerShell
# This script sets up the development environment for TDS Coder

Write-Host "🚀 Setting up TDS Coder development environment..." -ForegroundColor Green

# Check if Docker is installed
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is available
if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker Compose is not available. Please ensure Docker Desktop is running." -ForegroundColor Red
    exit 1
}

# Create .env file if it doesn't exist
if (!(Test-Path .env)) {
    Write-Host "📝 Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item .env.example .env
    Write-Host "✅ .env file created. Please update it with your configuration." -ForegroundColor Green
} else {
    Write-Host "✅ .env file already exists." -ForegroundColor Green
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path backend\logs | Out-Null
New-Item -ItemType Directory -Force -Path database\backups | Out-Null
New-Item -ItemType Directory -Force -Path frontend\.next | Out-Null
New-Item -ItemType Directory -Force -Path frontend\node_modules | Out-Null

# Start PostgreSQL and Redis first
Write-Host "🐘 Starting PostgreSQL and Redis..." -ForegroundColor Yellow
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
Write-Host "⏳ Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
do {
    Start-Sleep -Seconds 2
    $pgReady = docker-compose exec postgres pg_isready -U tds_user -d tds_coder 2>$null
} while ($LASTEXITCODE -ne 0)

Write-Host "✅ PostgreSQL is ready!" -ForegroundColor Green

# Check if Python is installed
if (!(Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Python is not installed. Please install Python 3.11+ first." -ForegroundColor Red
    exit 1
}

# Install backend dependencies
Write-Host "🐍 Installing backend dependencies..." -ForegroundColor Yellow
Set-Location backend

if (!(Test-Path venv)) {
    python -m venv venv
}

# Activate virtual environment
if (Test-Path venv\Scripts\Activate.ps1) {
    & venv\Scripts\Activate.ps1
} else {
    Write-Host "⚠️  Could not activate virtual environment" -ForegroundColor Yellow
}

# Install requirements
pip install -r requirements.txt

# Initialize Alembic if not already done
if (!(Test-Path alembic)) {
    Write-Host "🔄 Initializing Alembic..." -ForegroundColor Yellow
    alembic init alembic
}

# Run database migrations
Write-Host "🔄 Running database migrations..." -ForegroundColor Yellow
alembic upgrade head

Set-Location ..

# Check if Node.js is installed
if (!(Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Node.js/npm is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    exit 1
}

# Install frontend dependencies
Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Yellow
Set-Location frontend
npm install
Set-Location ..

# Start all services
Write-Host "🚀 Starting all services..." -ForegroundColor Yellow
docker-compose up -d

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service health
Write-Host "🏥 Checking service health..." -ForegroundColor Yellow

# Check backend health
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Backend is healthy" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Backend health check failed" -ForegroundColor Yellow
}

# Check frontend
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Frontend is accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Frontend health check failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Development environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Services:" -ForegroundColor Cyan
Write-Host "   Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "   API Docs: http://localhost:8000/api/v1/docs" -ForegroundColor White
Write-Host "   PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "   Redis: localhost:6379" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Useful commands:" -ForegroundColor Cyan
Write-Host "   View logs: docker-compose logs -f" -ForegroundColor White
Write-Host "   Stop services: docker-compose down" -ForegroundColor White
Write-Host "   Restart services: docker-compose restart" -ForegroundColor White
Write-Host "   Backend shell: docker-compose exec backend bash" -ForegroundColor White
Write-Host "   Database shell: docker-compose exec postgres psql -U tds_user -d tds_coder" -ForegroundColor White
Write-Host ""
Write-Host "📖 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Update .env file with your API keys" -ForegroundColor White
Write-Host "   2. Configure Stripe webhooks" -ForegroundColor White
Write-Host "   3. Set up SendGrid for emails" -ForegroundColor White
Write-Host "   4. Start coding! 🚀" -ForegroundColor White
