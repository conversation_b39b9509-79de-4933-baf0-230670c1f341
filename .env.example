# Database Configuration
DATABASE_URL=postgresql://tds_user:tds_password@localhost:5432/tds_coder
POSTGRES_DB=tds_coder
POSTGRES_USER=tds_user
POSTGRES_PASSWORD=tds_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_PRICE_ID_SOLO=price_your_solo_plan_price_id
STRIPE_PRICE_ID_TEAM=price_your_team_plan_price_id

# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=TDS Coder

# Application Configuration
ENVIRONMENT=development
DEBUG=true
API_V1_STR=/api/v1
PROJECT_NAME=TDS Coder
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
NEXT_PUBLIC_APP_NAME=TDS Coder

# Rate Limiting Configuration
RATE_LIMIT_FREE_DAILY=100
RATE_LIMIT_FREE_BURST=1
RATE_LIMIT_SOLO_DAILY=10000
RATE_LIMIT_SOLO_BURST=10
RATE_LIMIT_TEAM_DAILY=50000
RATE_LIMIT_TEAM_BURST=50

# Security Configuration
BCRYPT_ROUNDS=12
PASSWORD_RESET_TOKEN_EXPIRE_MINUTES=15
EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS=24
SESSION_TIMEOUT_MINUTES=60

# Refact AI Configuration (Phase 2)
REFACT_API_URL=http://localhost:8001
REFACT_API_KEY=your_refact_api_key

# Monitoring Configuration
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=INFO
