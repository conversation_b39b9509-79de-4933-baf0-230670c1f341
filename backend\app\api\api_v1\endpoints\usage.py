"""
Usage tracking and analytics endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any
from datetime import datetime

from app.db.session import get_db
from app.utils.security import get_current_active_user
from app.models.user import User
from app.services.usage import UsageService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


@router.get("/")
async def get_usage_stats(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Get current user's usage statistics."""
    try:
        usage_service = UsageService(db)
        stats = await usage_service.get_user_usage_stats(current_user, days)

        return stats

    except Exception as e:
        logger.error("Failed to get usage stats", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage statistics"
        )


@router.get("/daily")
async def get_daily_usage(
    days: int = Query(30, ge=1, le=365, description="Number of days to retrieve"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Get daily usage breakdown."""
    try:
        usage_service = UsageService(db)
        daily_usage = await usage_service.get_daily_usage(current_user, days)

        return {
            "period_days": days,
            "daily_usage": daily_usage
        }

    except Exception as e:
        logger.error("Failed to get daily usage", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve daily usage"
        )


@router.get("/quota")
async def get_quota_usage(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Get current quota usage."""
    try:
        usage_service = UsageService(db)
        quota = await usage_service.get_quota_usage(current_user)

        return quota

    except Exception as e:
        logger.error("Failed to get quota usage", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve quota usage"
        )
