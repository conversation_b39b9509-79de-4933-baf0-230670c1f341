"""
Webhook endpoints for external service integrations.
"""

from fastapi import APIRouter, Request, HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import json

from app.db.session import get_db
from app.services.stripe_service import StripeService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


@router.post("/stripe")
async def stripe_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db),
):
    """
    Handle Stripe webhook events.
    
    This endpoint receives and processes webhook events from Stripe
    for subscription management, payment processing, and billing updates.
    """
    try:
        # Get raw payload and signature
        payload = await request.body()
        signature = request.headers.get("stripe-signature")
        
        if not signature:
            logger.warning("Missing Stripe signature header")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing signature header"
            )
        
        # Verify webhook signature
        if not StripeService.verify_webhook_signature(payload, signature):
            logger.warning("Invalid Stripe webhook signature")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid signature"
            )
        
        # Parse event data
        try:
            event = json.loads(payload.decode('utf-8'))
        except json.JSONDecodeError:
            logger.error("Invalid JSON in webhook payload")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid JSON payload"
            )
        
        # Process the webhook event
        stripe_service = StripeService(db)
        success = await stripe_service.handle_webhook_event(event)
        
        if not success:
            logger.error("Failed to process webhook event", 
                        event_type=event.get("type"),
                        event_id=event.get("id"))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process webhook"
            )
        
        logger.info("Webhook processed successfully", 
                   event_type=event.get("type"),
                   event_id=event.get("id"))
        
        return {"status": "success"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Unexpected error processing webhook", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
