"""
Analytics endpoints for productivity metrics, team insights, and business intelligence.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from datetime import date, datetime, timedelta
from pydantic import BaseModel, Field
import io

from app.db.session import get_db
from app.utils.security import get_current_active_user
from app.models.user import User
from app.models.team import Team
from app.services.analytics import AnalyticsService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class DateRangeQuery(BaseModel):
    start_date: date = Field(..., description="Start date for analytics period")
    end_date: date = Field(..., description="End date for analytics period")


@router.get("/user/productivity")
async def get_user_productivity_metrics(
    start_date: date = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: date = Query(..., description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get comprehensive productivity metrics for the current user.
    
    Returns detailed analytics including:
    - Completion metrics (total, successful, acceptance rate)
    - Token usage and cost analysis
    - Performance metrics (response times, time saved)
    - Language and model usage breakdown
    - Daily trends and comparisons
    """
    try:
        # Validate date range
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if (end_date - start_date).days > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Date range cannot exceed 365 days"
            )
        
        analytics_service = AnalyticsService(db)
        metrics = await analytics_service.generate_user_productivity_metrics(
            current_user, start_date, end_date
        )
        
        logger.info("User productivity metrics generated", 
                   user_id=str(current_user.id),
                   start_date=start_date.isoformat(),
                   end_date=end_date.isoformat())
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get user productivity metrics", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve productivity metrics"
        )


@router.get("/team/{team_id}/analytics")
async def get_team_analytics(
    team_id: str,
    start_date: date = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: date = Query(..., description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get comprehensive analytics for a team.
    
    Returns team-level insights including:
    - Team performance metrics
    - Member comparison and rankings
    - Top performers identification
    - Language and model usage distribution
    - Team productivity trends
    """
    try:
        # Validate date range
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        # Get team and verify access
        team = await db.get(Team, team_id)
        if not team:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Team not found"
            )
        
        # Check if user has access to team analytics
        if current_user not in team.members and current_user != team.owner:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to team analytics"
            )
        
        analytics_service = AnalyticsService(db)
        metrics = await analytics_service.generate_team_analytics(
            team, start_date, end_date
        )
        
        logger.info("Team analytics generated", 
                   team_id=team_id,
                   user_id=str(current_user.id),
                   start_date=start_date.isoformat(),
                   end_date=end_date.isoformat())
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get team analytics", 
                    error=str(e), 
                    team_id=team_id,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve team analytics"
        )


@router.get("/business/metrics")
async def get_business_metrics(
    start_date: date = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: date = Query(..., description="End date (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get platform-wide business metrics.
    
    **Admin access required.**
    
    Returns business intelligence including:
    - User growth and engagement metrics
    - Subscription distribution and revenue
    - Platform usage statistics
    - Performance and reliability metrics
    """
    try:
        # Check admin access
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )
        
        # Validate date range
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        analytics_service = AnalyticsService(db)
        metrics = await analytics_service.generate_business_metrics(start_date, end_date)
        
        logger.info("Business metrics generated", 
                   admin_user_id=str(current_user.id),
                   start_date=start_date.isoformat(),
                   end_date=end_date.isoformat())
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get business metrics", 
                    error=str(e), 
                    admin_user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve business metrics"
        )


@router.get("/user/export")
async def export_user_report(
    start_date: date = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: date = Query(..., description="End date (YYYY-MM-DD)"),
    format: str = Query("pdf", description="Export format (pdf, csv)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Export user productivity report in specified format.
    
    Supported formats:
    - PDF: Comprehensive formatted report
    - CSV: Raw data for further analysis
    """
    try:
        # Validate parameters
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if format.lower() not in ["pdf", "csv"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Format must be 'pdf' or 'csv'"
            )
        
        analytics_service = AnalyticsService(db)
        report_data = await analytics_service.export_user_report(
            current_user, start_date, end_date, format
        )
        
        # Determine content type and filename
        if format.lower() == "pdf":
            content_type = "application/pdf"
            filename = f"tds_coder_user_report_{start_date}_{end_date}.pdf"
        else:
            content_type = "text/csv"
            filename = f"tds_coder_user_report_{start_date}_{end_date}.csv"
        
        logger.info("User report exported", 
                   user_id=str(current_user.id),
                   format=format,
                   start_date=start_date.isoformat(),
                   end_date=end_date.isoformat())
        
        return StreamingResponse(
            io.BytesIO(report_data),
            media_type=content_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to export user report", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export report"
        )


@router.get("/team/{team_id}/export")
async def export_team_report(
    team_id: str,
    start_date: date = Query(..., description="Start date (YYYY-MM-DD)"),
    end_date: date = Query(..., description="End date (YYYY-MM-DD)"),
    format: str = Query("pdf", description="Export format (pdf, csv)"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Export team analytics report in specified format.
    
    **Team member access required.**
    
    Supported formats:
    - PDF: Comprehensive formatted report
    - CSV: Raw data for further analysis
    """
    try:
        # Validate parameters
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if format.lower() not in ["pdf", "csv"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Format must be 'pdf' or 'csv'"
            )
        
        # Get team and verify access
        team = await db.get(Team, team_id)
        if not team:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Team not found"
            )
        
        if current_user not in team.members and current_user != team.owner:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to team reports"
            )
        
        analytics_service = AnalyticsService(db)
        report_data = await analytics_service.export_team_report(
            team, start_date, end_date, format
        )
        
        # Determine content type and filename
        if format.lower() == "pdf":
            content_type = "application/pdf"
            filename = f"tds_coder_team_report_{team.name}_{start_date}_{end_date}.pdf"
        else:
            content_type = "text/csv"
            filename = f"tds_coder_team_report_{team.name}_{start_date}_{end_date}.csv"
        
        logger.info("Team report exported", 
                   team_id=team_id,
                   user_id=str(current_user.id),
                   format=format,
                   start_date=start_date.isoformat(),
                   end_date=end_date.isoformat())
        
        return StreamingResponse(
            io.BytesIO(report_data),
            media_type=content_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to export team report", 
                    error=str(e), 
                    team_id=team_id,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export report"
        )
