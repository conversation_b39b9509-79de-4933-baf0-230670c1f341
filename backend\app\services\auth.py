"""
Authentication service for user login, registration, and token management.
"""

from typing import Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from passlib.context import CryptContext
from jose import J<PERSON><PERSON><PERSON>r, jwt
import secrets
import pyotp
import qrcode
import io
import base64
import json

from app.models.user import User
from app.core.config import settings
from app.core.logging import get_logger
from app.services.email import EmailService

logger = get_logger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """Authentication service for user management and token operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.email_service = EmailService()
    
    async def authenticate(
        self,
        email: str,
        password: str,
        totp_code: Optional[str] = None,
    ) -> Optional[User]:
        """
        Authenticate user with email, password, and optional TOTP code.
        
        Args:
            email: User's email address
            password: User's password
            totp_code: TOTP code for 2FA (if enabled)
            
        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            # Get user by email
            stmt = select(User).where(User.email == email, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                logger.warning("Authentication failed: user not found", email=email)
                return None
            
            # Verify password
            if not self.verify_password(password, user.hashed_password):
                logger.warning("Authentication failed: invalid password", email=email)
                return None
            
            # Check 2FA if enabled
            if user.is_2fa_enabled:
                if not totp_code:
                    logger.warning("Authentication failed: 2FA code required", email=email)
                    return None
                
                if not self.verify_totp(user.totp_secret, totp_code):
                    logger.warning("Authentication failed: invalid 2FA code", email=email)
                    return None
            
            logger.info("Authentication successful", email=email, user_id=str(user.id))
            return user
            
        except Exception as e:
            logger.error("Authentication error", error=str(e), email=email)
            return None
    
    async def create_tokens(self, user: User) -> Dict[str, Any]:
        """
        Create JWT access and refresh tokens for user.
        
        Args:
            user: User object
            
        Returns:
            Dictionary containing access_token, refresh_token, and expires_in
        """
        try:
            # Create access token
            access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self.create_access_token(
                data={"sub": str(user.id), "email": user.email},
                expires_delta=access_token_expires,
            )
            
            # Create refresh token
            refresh_token_expires = timedelta(days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS)
            refresh_token = self.create_access_token(
                data={"sub": str(user.id), "type": "refresh"},
                expires_delta=refresh_token_expires,
            )
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_in": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            }
            
        except Exception as e:
            logger.error("Token creation failed", error=str(e), user_id=str(user.id))
            raise
    
    def create_access_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None,
    ) -> str:
        """
        Create JWT access token.
        
        Args:
            data: Token payload data
            expires_delta: Token expiration time
            
        Returns:
            JWT token string
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM,
        )
        
        return encoded_jwt
    
    async def refresh_tokens(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            New token pair or None if invalid
        """
        try:
            # Decode refresh token
            payload = jwt.decode(
                refresh_token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM],
            )
            
            user_id = payload.get("sub")
            token_type = payload.get("type")
            
            if not user_id or token_type != "refresh":
                return None
            
            # Get user
            stmt = select(User).where(User.id == user_id, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user or not user.is_active:
                return None
            
            # Create new tokens
            return await self.create_tokens(user)
            
        except JWTError as e:
            logger.warning("Invalid refresh token", error=str(e))
            return None
        except Exception as e:
            logger.error("Token refresh failed", error=str(e))
            return None
    
    async def get_user_from_token(self, token: str) -> Optional[User]:
        """
        Get user from JWT token.
        
        Args:
            token: JWT access token
            
        Returns:
            User object or None if invalid
        """
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM],
            )
            
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            stmt = select(User).where(User.id == user_id, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            return user if user and user.is_active else None
            
        except JWTError:
            return None
        except Exception as e:
            logger.error("Token validation failed", error=str(e))
            return None
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt."""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_totp_secret() -> str:
        """Generate TOTP secret for 2FA."""
        return pyotp.random_base32()
    
    @staticmethod
    def verify_totp(secret: str, code: str) -> bool:
        """Verify TOTP code."""
        totp = pyotp.TOTP(secret)
        return totp.verify(code, valid_window=1)
    
    async def send_verification_email(
        self,
        user: User,
        client_ip: Optional[str] = None,
    ) -> bool:
        """Send email verification email."""
        try:
            # Generate verification token if not exists
            if not user.email_verification_token:
                user.email_verification_token = secrets.token_urlsafe(32)
                user.email_verification_expires = datetime.utcnow() + timedelta(hours=24)
                await self.db.commit()

            # Send email
            success = await self.email_service.send_verification_email(
                to_email=user.email,
                user_name=user.full_name or user.email.split('@')[0],
                verification_token=user.email_verification_token,
            )

            if success:
                logger.info("Verification email sent", email=user.email, client_ip=client_ip)
            else:
                logger.error("Failed to send verification email", email=user.email)

            return success
        except Exception as e:
            logger.error("Failed to send verification email", error=str(e), email=user.email)
            return False
    
    async def send_password_reset_email(
        self,
        email: str,
        client_ip: Optional[str] = None,
    ) -> bool:
        """Send password reset email."""
        try:
            # Get user by email
            stmt = select(User).where(User.email == email, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()

            if not user:
                # Don't reveal if email exists or not for security
                logger.info("Password reset requested for non-existent email", email=email)
                return True

            # Generate reset token
            reset_token = secrets.token_urlsafe(32)
            user.password_reset_token = reset_token
            user.password_reset_expires = datetime.utcnow() + timedelta(minutes=15)
            await self.db.commit()

            # Send email
            success = await self.email_service.send_password_reset_email(
                to_email=user.email,
                user_name=user.full_name or user.email.split('@')[0],
                reset_token=reset_token,
            )

            if success:
                logger.info("Password reset email sent", email=email, client_ip=client_ip)
            else:
                logger.error("Failed to send password reset email", email=email)

            return success
        except Exception as e:
            logger.error("Failed to send password reset email", error=str(e), email=email)
            return False
    
    async def verify_email(self, token: str) -> bool:
        """Verify email using verification token."""
        try:
            # Find user with this verification token
            stmt = select(User).where(
                User.email_verification_token == token,
                User.is_deleted == False
            )
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()

            if not user:
                logger.warning("Invalid verification token", token=token[:8] + "...")
                return False

            # Check if token has expired
            if user.email_verification_expires and user.email_verification_expires < datetime.utcnow():
                logger.warning("Verification token expired", email=user.email)
                return False

            # Verify the user
            user.is_verified = True
            user.email_verification_token = None
            user.email_verification_expires = None
            await self.db.commit()

            logger.info("Email verified successfully", email=user.email, user_id=str(user.id))
            return True

        except Exception as e:
            logger.error("Email verification failed", error=str(e))
            return False
    
    async def reset_password(self, token: str, new_password: str) -> bool:
        """Reset password using reset token."""
        try:
            # Find user with this reset token
            stmt = select(User).where(
                User.password_reset_token == token,
                User.is_deleted == False
            )
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()

            if not user:
                logger.warning("Invalid password reset token", token=token[:8] + "...")
                return False

            # Check if token has expired
            if user.password_reset_expires and user.password_reset_expires < datetime.utcnow():
                logger.warning("Password reset token expired", email=user.email)
                return False

            # Reset the password
            user.hashed_password = self.hash_password(new_password)
            user.password_reset_token = None
            user.password_reset_expires = None
            await self.db.commit()

            logger.info("Password reset successfully", email=user.email, user_id=str(user.id))
            return True

        except Exception as e:
            logger.error("Password reset failed", error=str(e))
            return False
    
    async def update_last_login(self, user: User, client_ip: Optional[str] = None):
        """Update user's last login timestamp."""
        try:
            user.last_login = datetime.utcnow()
            user.last_activity = datetime.utcnow()
            await self.db.commit()
            
            logger.info("Last login updated", user_id=str(user.id), client_ip=client_ip)
            
        except Exception as e:
            logger.error("Failed to update last login", error=str(e), user_id=str(user.id))
    
    async def setup_2fa(self, user: User) -> Dict[str, Any]:
        """
        Set up 2FA for user and return QR code data.

        Args:
            user: User object

        Returns:
            Dictionary with secret, qr_code, and backup_codes
        """
        try:
            # Generate TOTP secret
            secret = self.generate_totp_secret()

            # Generate backup codes
            backup_codes = [secrets.token_hex(4).upper() for _ in range(10)]

            # Create QR code
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user.email,
                issuer_name="TDS Coder"
            )

            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)

            # Convert QR code to base64 image
            img = qr.make_image(fill_color="black", back_color="white")
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()

            # Store secret temporarily (not enabled yet)
            user.totp_secret = secret
            user.backup_codes = json.dumps(backup_codes)
            await self.db.commit()

            logger.info("2FA setup initiated", user_id=str(user.id))

            return {
                "secret": secret,
                "qr_code": f"data:image/png;base64,{qr_code_base64}",
                "backup_codes": backup_codes,
                "manual_entry_key": secret
            }

        except Exception as e:
            logger.error("2FA setup failed", error=str(e), user_id=str(user.id))
            raise

    async def enable_2fa(self, user: User, totp_code: str) -> bool:
        """
        Enable 2FA after verifying TOTP code.

        Args:
            user: User object
            totp_code: TOTP code from authenticator app

        Returns:
            True if 2FA enabled successfully
        """
        try:
            if not user.totp_secret:
                logger.warning("No TOTP secret found for 2FA enable", user_id=str(user.id))
                return False

            # Verify TOTP code
            if not self.verify_totp(user.totp_secret, totp_code):
                logger.warning("Invalid TOTP code for 2FA enable", user_id=str(user.id))
                return False

            # Enable 2FA
            user.is_2fa_enabled = True
            await self.db.commit()

            # Send notification email
            await self.email_service.send_2fa_enabled_email(
                to_email=user.email,
                user_name=user.full_name or user.email.split('@')[0]
            )

            logger.info("2FA enabled successfully", user_id=str(user.id))
            return True

        except Exception as e:
            logger.error("2FA enable failed", error=str(e), user_id=str(user.id))
            return False

    async def disable_2fa(self, user: User, password: str, totp_code: str) -> bool:
        """
        Disable 2FA after verifying password and TOTP code.

        Args:
            user: User object
            password: User's password
            totp_code: TOTP code from authenticator app

        Returns:
            True if 2FA disabled successfully
        """
        try:
            # Verify password
            if not self.verify_password(password, user.hashed_password):
                logger.warning("Invalid password for 2FA disable", user_id=str(user.id))
                return False

            # Verify TOTP code
            if not self.verify_totp(user.totp_secret, totp_code):
                logger.warning("Invalid TOTP code for 2FA disable", user_id=str(user.id))
                return False

            # Disable 2FA
            user.is_2fa_enabled = False
            user.totp_secret = None
            user.backup_codes = None
            await self.db.commit()

            logger.info("2FA disabled successfully", user_id=str(user.id))
            return True

        except Exception as e:
            logger.error("2FA disable failed", error=str(e), user_id=str(user.id))
            return False

    async def verify_backup_code(self, user: User, backup_code: str) -> bool:
        """
        Verify and consume a backup code.

        Args:
            user: User object
            backup_code: Backup code to verify

        Returns:
            True if backup code is valid
        """
        try:
            if not user.backup_codes:
                return False

            backup_codes = json.loads(user.backup_codes)
            backup_code = backup_code.upper().strip()

            if backup_code in backup_codes:
                # Remove used backup code
                backup_codes.remove(backup_code)
                user.backup_codes = json.dumps(backup_codes)
                await self.db.commit()

                logger.info("Backup code used", user_id=str(user.id))
                return True

            return False

        except Exception as e:
            logger.error("Backup code verification failed", error=str(e), user_id=str(user.id))
            return False

    async def logout(self, token: str) -> bool:
        """Logout user and invalidate token."""
        try:
            # TODO: Implement token blacklisting in Redis
            logger.info("User logged out")
            return True
        except Exception as e:
            logger.error("Logout failed", error=str(e))
            return False
