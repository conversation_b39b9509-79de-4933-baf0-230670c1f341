"""
Authentication service for user login, registration, and token management.
"""

from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from passlib.context import CryptContext
from jose import JWTError, jwt
import secrets
import pyotp

from app.models.user import User
from app.core.config import settings
from app.core.logging import get_logger
from app.services.email import EmailService

logger = get_logger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """Authentication service for user management and token operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.email_service = EmailService()
    
    async def authenticate(
        self,
        email: str,
        password: str,
        totp_code: Optional[str] = None,
    ) -> Optional[User]:
        """
        Authenticate user with email, password, and optional TOTP code.
        
        Args:
            email: User's email address
            password: User's password
            totp_code: TOTP code for 2FA (if enabled)
            
        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            # Get user by email
            stmt = select(User).where(User.email == email, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                logger.warning("Authentication failed: user not found", email=email)
                return None
            
            # Verify password
            if not self.verify_password(password, user.hashed_password):
                logger.warning("Authentication failed: invalid password", email=email)
                return None
            
            # Check 2FA if enabled
            if user.is_2fa_enabled:
                if not totp_code:
                    logger.warning("Authentication failed: 2FA code required", email=email)
                    return None
                
                if not self.verify_totp(user.totp_secret, totp_code):
                    logger.warning("Authentication failed: invalid 2FA code", email=email)
                    return None
            
            logger.info("Authentication successful", email=email, user_id=str(user.id))
            return user
            
        except Exception as e:
            logger.error("Authentication error", error=str(e), email=email)
            return None
    
    async def create_tokens(self, user: User) -> Dict[str, Any]:
        """
        Create JWT access and refresh tokens for user.
        
        Args:
            user: User object
            
        Returns:
            Dictionary containing access_token, refresh_token, and expires_in
        """
        try:
            # Create access token
            access_token_expires = timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self.create_access_token(
                data={"sub": str(user.id), "email": user.email},
                expires_delta=access_token_expires,
            )
            
            # Create refresh token
            refresh_token_expires = timedelta(days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS)
            refresh_token = self.create_access_token(
                data={"sub": str(user.id), "type": "refresh"},
                expires_delta=refresh_token_expires,
            )
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_in": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            }
            
        except Exception as e:
            logger.error("Token creation failed", error=str(e), user_id=str(user.id))
            raise
    
    def create_access_token(
        self,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None,
    ) -> str:
        """
        Create JWT access token.
        
        Args:
            data: Token payload data
            expires_delta: Token expiration time
            
        Returns:
            JWT token string
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=15)
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.JWT_SECRET_KEY,
            algorithm=settings.JWT_ALGORITHM,
        )
        
        return encoded_jwt
    
    async def refresh_tokens(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Valid refresh token
            
        Returns:
            New token pair or None if invalid
        """
        try:
            # Decode refresh token
            payload = jwt.decode(
                refresh_token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM],
            )
            
            user_id = payload.get("sub")
            token_type = payload.get("type")
            
            if not user_id or token_type != "refresh":
                return None
            
            # Get user
            stmt = select(User).where(User.id == user_id, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user or not user.is_active:
                return None
            
            # Create new tokens
            return await self.create_tokens(user)
            
        except JWTError as e:
            logger.warning("Invalid refresh token", error=str(e))
            return None
        except Exception as e:
            logger.error("Token refresh failed", error=str(e))
            return None
    
    async def get_user_from_token(self, token: str) -> Optional[User]:
        """
        Get user from JWT token.
        
        Args:
            token: JWT access token
            
        Returns:
            User object or None if invalid
        """
        try:
            payload = jwt.decode(
                token,
                settings.JWT_SECRET_KEY,
                algorithms=[settings.JWT_ALGORITHM],
            )
            
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            stmt = select(User).where(User.id == user_id, User.is_deleted == False)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            return user if user and user.is_active else None
            
        except JWTError:
            return None
        except Exception as e:
            logger.error("Token validation failed", error=str(e))
            return None
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt."""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_totp_secret() -> str:
        """Generate TOTP secret for 2FA."""
        return pyotp.random_base32()
    
    @staticmethod
    def verify_totp(secret: str, code: str) -> bool:
        """Verify TOTP code."""
        totp = pyotp.TOTP(secret)
        return totp.verify(code, valid_window=1)
    
    async def send_verification_email(
        self,
        email: str,
        token: str,
        client_ip: Optional[str] = None,
    ) -> bool:
        """Send email verification email."""
        try:
            # TODO: Implement email sending
            logger.info("Verification email sent", email=email, client_ip=client_ip)
            return True
        except Exception as e:
            logger.error("Failed to send verification email", error=str(e), email=email)
            return False
    
    async def send_password_reset_email(
        self,
        email: str,
        client_ip: Optional[str] = None,
    ) -> bool:
        """Send password reset email."""
        try:
            # TODO: Implement password reset email
            logger.info("Password reset email sent", email=email, client_ip=client_ip)
            return True
        except Exception as e:
            logger.error("Failed to send password reset email", error=str(e), email=email)
            return False
    
    async def verify_email(self, token: str) -> bool:
        """Verify email using verification token."""
        try:
            # TODO: Implement email verification
            return True
        except Exception as e:
            logger.error("Email verification failed", error=str(e))
            return False
    
    async def reset_password(self, token: str, new_password: str) -> bool:
        """Reset password using reset token."""
        try:
            # TODO: Implement password reset
            return True
        except Exception as e:
            logger.error("Password reset failed", error=str(e))
            return False
    
    async def update_last_login(self, user: User, client_ip: Optional[str] = None):
        """Update user's last login timestamp."""
        try:
            user.last_login = datetime.utcnow()
            user.last_activity = datetime.utcnow()
            await self.db.commit()
            
            logger.info("Last login updated", user_id=str(user.id), client_ip=client_ip)
            
        except Exception as e:
            logger.error("Failed to update last login", error=str(e), user_id=str(user.id))
    
    async def logout(self, token: str) -> bool:
        """Logout user and invalidate token."""
        try:
            # TODO: Implement token blacklisting
            logger.info("User logged out")
            return True
        except Exception as e:
            logger.error("Logout failed", error=str(e))
            return False
