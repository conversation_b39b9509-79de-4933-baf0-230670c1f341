#!/usr/bin/env python3
"""
Test script for database migrations.
This script tests migration upgrade and downgrade functionality.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine
from alembic.config import Config
from alembic import command
from app.core.config import settings
from app.core.logging import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


def test_migrations():
    """Test migration upgrade and downgrade."""
    try:
        # Create Alembic config
        alembic_cfg = Config(str(backend_dir / "alembic.ini"))
        alembic_cfg.set_main_option("sqlalchemy.url", settings.DATABASE_URL)
        
        logger.info("Testing database migrations...")
        
        # Test upgrade
        logger.info("Running migration upgrade...")
        command.upgrade(alembic_cfg, "head")
        logger.info("Migration upgrade completed successfully")
        
        # Test that tables exist
        engine = create_engine(settings.DATABASE_URL)
        with engine.connect() as conn:
            # Check if tables exist
            tables = [
                "users", "subscriptions", "api_keys", "usage"
            ]
            
            for table in tables:
                result = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table}'
                    );
                """))
                exists = result.scalar()
                if exists:
                    logger.info(f"✅ Table '{table}' exists")
                else:
                    logger.error(f"❌ Table '{table}' does not exist")
                    return False
            
            # Check if functions exist
            functions = [
                "update_updated_at_column", "check_daily_quota"
            ]
            
            for function in functions:
                result = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.routines 
                        WHERE routine_schema = 'public' 
                        AND routine_name = '{function}'
                    );
                """))
                exists = result.scalar()
                if exists:
                    logger.info(f"✅ Function '{function}' exists")
                else:
                    logger.error(f"❌ Function '{function}' does not exist")
                    return False
            
            # Test quota function
            result = conn.execute(text("""
                SELECT check_daily_quota('00000000-0000-0000-0000-000000000000'::UUID);
            """))
            quota_result = result.scalar()
            logger.info(f"✅ Quota function test result: {quota_result}")
        
        # Test downgrade
        logger.info("Testing migration downgrade...")
        command.downgrade(alembic_cfg, "base")
        logger.info("Migration downgrade completed successfully")
        
        # Test that tables are dropped
        with engine.connect() as conn:
            for table in tables:
                result = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table}'
                    );
                """))
                exists = result.scalar()
                if not exists:
                    logger.info(f"✅ Table '{table}' properly dropped")
                else:
                    logger.error(f"❌ Table '{table}' still exists after downgrade")
                    return False
        
        # Upgrade again for normal operation
        logger.info("Re-running migration upgrade for normal operation...")
        command.upgrade(alembic_cfg, "head")
        logger.info("Migration testing completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"Migration test failed: {str(e)}")
        return False


async def test_async_operations():
    """Test async database operations."""
    try:
        from app.db.session import get_db_session
        from app.models.user import User
        from app.services.auth import AuthService
        
        logger.info("Testing async database operations...")
        
        # Test database connection
        async with get_db_session() as db:
            # Test creating a user
            auth_service = AuthService(db)
            
            # This is just a connection test, not creating actual data
            logger.info("✅ Async database connection successful")
            
        return True
        
    except Exception as e:
        logger.error(f"Async operations test failed: {str(e)}")
        return False


def main():
    """Main test function."""
    logger.info("🚀 Starting database migration tests...")
    
    # Test synchronous migrations
    if not test_migrations():
        logger.error("❌ Migration tests failed")
        sys.exit(1)
    
    # Test async operations
    if not asyncio.run(test_async_operations()):
        logger.error("❌ Async operations tests failed")
        sys.exit(1)
    
    logger.info("🎉 All database tests passed successfully!")


if __name__ == "__main__":
    main()
