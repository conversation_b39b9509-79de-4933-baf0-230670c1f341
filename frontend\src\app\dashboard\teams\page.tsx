'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'
import { 
  UsersIcon, 
  PlusIcon,
  Cog6ToothIcon,
  ShareIcon,
  CodeBracketIcon,
  EnvelopeIcon,
  CalendarIcon,
  UserGroupIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@/lib/api'
import toast from 'react-hot-toast'

interface Team {
  id: string
  name: string
  description?: string
  slug: string
  max_members: number
  is_public: boolean
  enable_usage_pooling: boolean
  member_count: number
  owner_id: string
  created_at: string
}

export default function TeamsPage() {
  const { user } = useAuth()
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null)

  useEffect(() => {
    fetchTeams()
  }, [])

  const fetchTeams = async () => {
    try {
      setLoading(true)
      const data = await apiHelpers.teams.list()
      setTeams(data)
    } catch (error) {
      console.error('Failed to fetch teams:', error)
      toast.error('Failed to load teams')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTeam = async (teamData: any) => {
    try {
      const newTeam = await apiHelpers.teams.create(teamData)
      setTeams(prev => [...prev, newTeam])
      setShowCreateModal(false)
      toast.success('Team created successfully')
    } catch (error) {
      console.error('Failed to create team:', error)
      toast.error('Failed to create team')
    }
  }

  const handleInviteMember = async (inviteData: any) => {
    try {
      await apiHelpers.teams.invite(selectedTeam!.id, inviteData)
      setShowInviteModal(false)
      setSelectedTeam(null)
      toast.success('Invitation sent successfully')
    } catch (error) {
      console.error('Failed to send invitation:', error)
      toast.error('Failed to send invitation')
    }
  }

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Teams
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Collaborate with your team members and share code snippets
            </p>
          </div>
          
          <button
            onClick={() => setShowCreateModal(true)}
            className="mt-4 sm:mt-0 btn-primary px-4 py-2 rounded-md flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Create Team
          </button>
        </div>
      </div>

      {/* Teams Grid */}
      {teams.length === 0 ? (
        <div className="text-center py-12">
          <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No teams yet
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Create your first team to start collaborating with others.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary px-4 py-2 rounded-md flex items-center mx-auto"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Team
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teams.map((team) => (
            <div key={team.id} className="card hover:shadow-lg transition-shadow">
              <div className="card-content">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {team.name}
                    </h3>
                    {team.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {team.description}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        setSelectedTeam(team)
                        setShowInviteModal(true)
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Invite members"
                    >
                      <EnvelopeIcon className="h-4 w-4" />
                    </button>
                    <button
                      className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Team settings"
                    >
                      <Cog6ToothIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Members:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {team.member_count} / {team.max_members}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Visibility:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      team.is_public 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {team.is_public ? 'Public' : 'Private'}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Usage Pooling:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      team.enable_usage_pooling 
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {team.enable_usage_pooling ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <CalendarIcon className="h-4 w-4 mr-1" />
                    Created {new Date(team.created_at).toLocaleDateString()}
                  </div>
                </div>

                <div className="mt-6 flex space-x-2">
                  <button className="flex-1 btn-secondary px-3 py-2 text-sm rounded-md flex items-center justify-center">
                    <CodeBracketIcon className="h-4 w-4 mr-1" />
                    Snippets
                  </button>
                  <button className="flex-1 btn-secondary px-3 py-2 text-sm rounded-md flex items-center justify-center">
                    <ChartBarIcon className="h-4 w-4 mr-1" />
                    Analytics
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Team Modal */}
      {showCreateModal && (
        <CreateTeamModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateTeam}
        />
      )}

      {/* Invite Member Modal */}
      {showInviteModal && selectedTeam && (
        <InviteMemberModal
          team={selectedTeam}
          onClose={() => {
            setShowInviteModal(false)
            setSelectedTeam(null)
          }}
          onSubmit={handleInviteMember}
        />
      )}
    </div>
  )
}

// Create Team Modal Component
function CreateTeamModal({ onClose, onSubmit }: { onClose: () => void, onSubmit: (data: any) => void }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    max_members: 10,
    is_public: false,
    enable_usage_pooling: true
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Create New Team
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Team Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="form-input w-full"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="form-input w-full"
              rows={3}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Maximum Members
            </label>
            <input
              type="number"
              value={formData.max_members}
              onChange={(e) => setFormData(prev => ({ ...prev, max_members: parseInt(e.target.value) }))}
              className="form-input w-full"
              min="2"
              max="100"
            />
          </div>
          
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_public}
                onChange={(e) => setFormData(prev => ({ ...prev, is_public: e.target.checked }))}
                className="form-checkbox"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Make team publicly discoverable
              </span>
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.enable_usage_pooling}
                onChange={(e) => setFormData(prev => ({ ...prev, enable_usage_pooling: e.target.checked }))}
                className="form-checkbox"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Enable usage pooling
              </span>
            </label>
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 btn-secondary px-4 py-2 rounded-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 btn-primary px-4 py-2 rounded-md"
            >
              Create Team
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Invite Member Modal Component
function InviteMemberModal({ 
  team, 
  onClose, 
  onSubmit 
}: { 
  team: Team, 
  onClose: () => void, 
  onSubmit: (data: any) => void 
}) {
  const [formData, setFormData] = useState({
    email: '',
    role: 'member',
    message: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Invite Member to {team.name}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email Address
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              className="form-input w-full"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Role
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
              className="form-input w-full"
            >
              <option value="member">Member</option>
              <option value="admin">Admin</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Message (Optional)
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              className="form-input w-full"
              rows={3}
              placeholder="Add a personal message to the invitation..."
            />
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 btn-secondary px-4 py-2 rounded-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 btn-primary px-4 py-2 rounded-md"
            >
              Send Invitation
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
