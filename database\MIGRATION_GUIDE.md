# TDS Coder Database Migration Guide

## Overview

This guide covers database migration procedures for TDS Coder using Alembic. All database schema changes should be managed through migrations to ensure consistency across environments.

## Prerequisites

- PostgreSQL 15+ running
- Python 3.11+ with backend dependencies installed
- Alembic configured (already done in this project)

## Migration Commands

### Basic Operations

```bash
# Navigate to backend directory
cd backend

# Check current migration status
alembic current

# Show migration history
alembic history --verbose

# Upgrade to latest migration
alembic upgrade head

# Upgrade to specific revision
alembic upgrade <revision_id>

# Downgrade to previous migration
alembic downgrade -1

# Downgrade to specific revision
alembic downgrade <revision_id>

# Downgrade to base (empty database)
alembic downgrade base
```

### Creating New Migrations

```bash
# Auto-generate migration from model changes
alembic revision --autogenerate -m "Description of changes"

# Create empty migration file
alembic revision -m "Description of changes"

# Create migration with specific revision ID
alembic revision -m "Description" --rev-id="002"
```

## Migration Structure

### Current Migrations

1. **001_initial_schema.py** - Initial database schema
   - Creates users, subscriptions, api_keys, and usage tables
   - Sets up indexes and constraints
   - Creates database functions for quota checking
   - Adds triggers for automatic timestamp updates

### Database Schema

#### Users Table
- Primary user information and authentication data
- Includes 2FA support, email verification, password reset
- Soft delete capability

#### Subscriptions Table
- User subscription and billing information
- Stripe integration fields
- Billing address and tax information
- Dunning management for failed payments

#### API Keys Table
- Secure API key management
- Usage tracking and rate limiting
- Scopes and IP restrictions
- Expiration handling

#### Usage Table
- Comprehensive API usage tracking
- Performance metrics and analytics
- Error tracking and debugging
- Geographic and metadata information

## Database Functions

### update_updated_at_column()
Automatically updates the `updated_at` timestamp when records are modified.

**Usage**: Triggered automatically on UPDATE operations.

### check_daily_quota(user_uuid, request_date)
Checks if a user has exceeded their daily API request quota.

**Parameters**:
- `user_uuid`: User's UUID
- `request_date`: Date to check (defaults to current date)

**Returns**: Boolean (true if quota exceeded)

**Example**:
```sql
SELECT check_daily_quota('123e4567-e89b-12d3-a456-************'::UUID);
```

## Migration Best Practices

### 1. Always Review Auto-Generated Migrations
```bash
# After running autogenerate, review the migration file
alembic revision --autogenerate -m "Add new column"
# Edit the generated file to ensure correctness
```

### 2. Test Migrations Thoroughly
```bash
# Test upgrade
alembic upgrade head

# Test downgrade
alembic downgrade -1

# Test upgrade again
alembic upgrade head
```

### 3. Backup Before Production Migrations
```bash
# Create database backup
pg_dump -h localhost -U tds_user -d tds_coder > backup_$(date +%Y%m%d_%H%M%S).sql

# Run migration
alembic upgrade head
```

### 4. Handle Data Migrations Carefully
For migrations that modify existing data:

```python
def upgrade():
    # Create new column
    op.add_column('users', sa.Column('new_field', sa.String(50)))
    
    # Migrate existing data
    connection = op.get_bind()
    connection.execute(
        text("UPDATE users SET new_field = 'default_value' WHERE new_field IS NULL")
    )
    
    # Add constraints after data migration
    op.alter_column('users', 'new_field', nullable=False)
```

## Rollback Procedures

### Emergency Rollback
If a migration causes issues in production:

1. **Immediate rollback**:
   ```bash
   alembic downgrade -1
   ```

2. **Restore from backup** (if needed):
   ```bash
   pg_restore -h localhost -U tds_user -d tds_coder backup_file.sql
   ```

3. **Verify application functionality**

### Planned Rollback
For planned rollbacks during maintenance:

1. **Check current revision**:
   ```bash
   alembic current
   ```

2. **Review migration history**:
   ```bash
   alembic history
   ```

3. **Rollback to specific revision**:
   ```bash
   alembic downgrade <revision_id>
   ```

## Testing Migrations

### Automated Testing
Use the provided test script:

```bash
python scripts/test-migrations.py
```

This script:
- Tests upgrade to head
- Verifies table and function creation
- Tests downgrade to base
- Verifies cleanup
- Re-upgrades for normal operation

### Manual Testing
1. **Create test database**:
   ```sql
   CREATE DATABASE tds_coder_test;
   ```

2. **Update connection string** in test environment

3. **Run migrations**:
   ```bash
   alembic upgrade head
   ```

4. **Test application functionality**

5. **Clean up**:
   ```sql
   DROP DATABASE tds_coder_test;
   ```

## Troubleshooting

### Common Issues

#### Migration Conflicts
```bash
# If you have conflicting migrations
alembic merge -m "Merge conflicting revisions" <rev1> <rev2>
```

#### Corrupted Migration State
```bash
# Reset to specific revision without running migrations
alembic stamp <revision_id>
```

#### Manual Schema Changes
If someone made manual schema changes:

```bash
# Generate migration to match current state
alembic revision --autogenerate -m "Sync with manual changes"
```

### Recovery Procedures

#### Lost Migration Files
1. Recreate migration from current schema
2. Use `alembic stamp head` to mark as applied
3. Continue with new migrations

#### Database Corruption
1. Restore from latest backup
2. Re-run migrations from backup point
3. Verify data integrity

## Environment-Specific Considerations

### Development
- Frequent schema changes are normal
- Use autogenerate liberally
- Test rollbacks regularly

### Staging
- Mirror production procedures
- Test all migrations before production
- Maintain separate migration history

### Production
- Always backup before migrations
- Plan maintenance windows
- Have rollback procedures ready
- Monitor application after migrations

## Security Considerations

### Migration Files
- Review all generated SQL
- Avoid exposing sensitive data in migrations
- Use parameterized queries for data migrations

### Database Access
- Use dedicated migration user with minimal privileges
- Audit migration execution
- Secure migration scripts

## Monitoring

### Migration Metrics
- Track migration execution time
- Monitor database size changes
- Alert on migration failures

### Post-Migration Checks
- Verify application functionality
- Check database performance
- Monitor error rates

## Support

For migration issues:
1. Check this documentation
2. Review Alembic logs
3. Test in development environment
4. Contact the development team

Remember: **Always test migrations in a non-production environment first!**
