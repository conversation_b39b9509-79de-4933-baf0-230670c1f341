"""
Usage tracking model for API requests and analytics.
"""

from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Text, Numeric, Boolean, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime

from app.db.base import BaseModel


class Usage(BaseModel):
    """Usage tracking model for API requests and analytics."""
    
    __tablename__ = "usage"
    
    # Primary Key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Relationships
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    api_key_id = Column(UUID(as_uuid=True), ForeignKey("api_keys.id"), nullable=True, index=True)
    
    # Request Information
    endpoint = Column(String(255), nullable=False, index=True)
    method = Column(String(10), nullable=False)  # GET, POST, etc.
    request_id = Column(String(255), nullable=True, index=True)  # Unique request identifier
    
    # Timing
    timestamp = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False, index=True)
    response_time_ms = Column(Integer, nullable=True)  # Response time in milliseconds
    
    # Request Details
    request_size_bytes = Column(Integer, nullable=True)
    response_size_bytes = Column(Integer, nullable=True)
    status_code = Column(Integer, nullable=False, index=True)
    
    # Client Information
    client_ip = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    referer = Column(String(500), nullable=True)
    
    # API-Specific Data
    model_used = Column(String(100), nullable=True)  # AI model used for completion
    tokens_input = Column(Integer, nullable=True)  # Input tokens for AI requests
    tokens_output = Column(Integer, nullable=True)  # Output tokens for AI requests
    completion_type = Column(String(50), nullable=True)  # code, chat, etc.
    
    # Cost Tracking
    cost_cents = Column(Integer, nullable=True)  # Cost in cents
    credits_used = Column(Numeric(10, 4), nullable=True)  # Credits consumed
    
    # Error Information
    error_code = Column(String(50), nullable=True, index=True)
    error_message = Column(Text, nullable=True)
    
    # Rate Limiting
    rate_limit_hit = Column(Boolean, default=False, nullable=False)
    quota_exceeded = Column(Boolean, default=False, nullable=False)
    
    # Geographic Information
    country_code = Column(String(2), nullable=True)  # ISO country code
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    
    # Additional Metadata
    metadata = Column(Text, nullable=True)  # JSON for additional data
    
    # Relationships
    user = relationship("User", back_populates="usage_records")
    api_key = relationship("APIKey", back_populates="usage_records")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_usage_user_timestamp', 'user_id', 'timestamp'),
        Index('idx_usage_api_key_timestamp', 'api_key_id', 'timestamp'),
        Index('idx_usage_endpoint_timestamp', 'endpoint', 'timestamp'),
        Index('idx_usage_status_timestamp', 'status_code', 'timestamp'),
        Index('idx_usage_daily', 'user_id', 'timestamp'),  # For daily usage queries
    )
    
    def __repr__(self):
        return f"<Usage(id={self.id}, user_id={self.user_id}, endpoint={self.endpoint})>"
    
    @property
    def is_successful(self) -> bool:
        """Check if the request was successful."""
        return 200 <= self.status_code < 300
    
    @property
    def is_error(self) -> bool:
        """Check if the request resulted in an error."""
        return self.status_code >= 400
    
    @property
    def is_server_error(self) -> bool:
        """Check if the request resulted in a server error."""
        return self.status_code >= 500
    
    @property
    def total_tokens(self) -> int:
        """Get total tokens used (input + output)."""
        input_tokens = self.tokens_input or 0
        output_tokens = self.tokens_output or 0
        return input_tokens + output_tokens
    
    @property
    def cost_dollars(self) -> float:
        """Get cost in dollars."""
        if self.cost_cents is None:
            return 0.0
        return self.cost_cents / 100.0
    
    def to_analytics_dict(self) -> dict:
        """Convert to dictionary for analytics purposes."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "endpoint": self.endpoint,
            "method": self.method,
            "status_code": self.status_code,
            "response_time_ms": self.response_time_ms,
            "tokens_total": self.total_tokens,
            "tokens_input": self.tokens_input,
            "tokens_output": self.tokens_output,
            "cost_dollars": self.cost_dollars,
            "model_used": self.model_used,
            "completion_type": self.completion_type,
            "country_code": self.country_code,
            "is_successful": self.is_successful,
            "rate_limit_hit": self.rate_limit_hit,
            "quota_exceeded": self.quota_exceeded,
        }
    
    @classmethod
    def create_from_request(
        cls,
        user_id: uuid.UUID,
        api_key_id: uuid.UUID = None,
        endpoint: str = None,
        method: str = "GET",
        status_code: int = 200,
        **kwargs
    ) -> "Usage":
        """Create a usage record from request data."""
        return cls(
            user_id=user_id,
            api_key_id=api_key_id,
            endpoint=endpoint,
            method=method,
            status_code=status_code,
            timestamp=datetime.utcnow(),
            **kwargs
        )
