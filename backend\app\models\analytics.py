"""
Analytics models for tracking user behavior, productivity metrics, and business intelligence.
"""

from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, Text, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from app.db.base_class import Base


class UserSession(Base):
    """Track user sessions for analytics."""
    __tablename__ = "user_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    session_id = Column(String(255), nullable=False, unique=True)
    start_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    end_time = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    country_code = Column(String(2), nullable=True)
    city = Column(String(100), nullable=True)
    device_type = Column(String(50), nullable=True)  # desktop, mobile, tablet
    browser = Column(String(100), nullable=True)
    os = Column(String(100), nullable=True)
    referrer = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    user = relationship("User", back_populates="sessions")

    # Indexes
    __table_args__ = (
        Index('idx_user_sessions_user_id', 'user_id'),
        Index('idx_user_sessions_start_time', 'start_time'),
        Index('idx_user_sessions_session_id', 'session_id'),
        Index('idx_user_sessions_active', 'is_active'),
    )


class ProductivityMetric(Base):
    """Track productivity metrics for users."""
    __tablename__ = "productivity_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    date = Column(DateTime, nullable=False)  # Date for the metrics (daily aggregation)
    
    # Completion metrics
    total_completions = Column(Integer, default=0, nullable=False)
    accepted_completions = Column(Integer, default=0, nullable=False)
    rejected_completions = Column(Integer, default=0, nullable=False)
    acceptance_rate = Column(Float, default=0.0, nullable=False)
    
    # Token metrics
    total_tokens_input = Column(Integer, default=0, nullable=False)
    total_tokens_output = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, default=0, nullable=False)
    
    # Time metrics
    total_coding_time_seconds = Column(Integer, default=0, nullable=False)
    avg_completion_time_ms = Column(Float, default=0.0, nullable=False)
    time_saved_seconds = Column(Integer, default=0, nullable=False)  # Estimated time saved
    
    # Language breakdown
    languages_used = Column(JSONB, nullable=True)  # {"python": 50, "javascript": 30, ...}
    
    # Model usage
    models_used = Column(JSONB, nullable=True)  # {"gpt-4": 20, "claude-3": 15, ...}
    
    # Error metrics
    total_errors = Column(Integer, default=0, nullable=False)
    error_rate = Column(Float, default=0.0, nullable=False)
    
    # Cost metrics
    total_cost_cents = Column(Integer, default=0, nullable=False)
    
    # Quality metrics
    avg_completion_length = Column(Float, default=0.0, nullable=False)
    unique_files_worked = Column(Integer, default=0, nullable=False)
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    user = relationship("User", back_populates="productivity_metrics")

    # Indexes
    __table_args__ = (
        Index('idx_productivity_metrics_user_date', 'user_id', 'date'),
        Index('idx_productivity_metrics_date', 'date'),
        Index('idx_productivity_metrics_user_id', 'user_id'),
    )


class TeamMetric(Base):
    """Track team-level metrics and comparisons."""
    __tablename__ = "team_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    team_id = Column(UUID(as_uuid=True), ForeignKey("teams.id"), nullable=False)
    date = Column(DateTime, nullable=False)
    
    # Team aggregated metrics
    total_members = Column(Integer, default=0, nullable=False)
    active_members = Column(Integer, default=0, nullable=False)
    total_completions = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, default=0, nullable=False)
    total_cost_cents = Column(Integer, default=0, nullable=False)
    avg_acceptance_rate = Column(Float, default=0.0, nullable=False)
    
    # Top performers
    top_performers = Column(JSONB, nullable=True)  # [{"user_id": "...", "completions": 100}, ...]
    
    # Language distribution
    language_distribution = Column(JSONB, nullable=True)
    
    # Model usage distribution
    model_distribution = Column(JSONB, nullable=True)
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    team = relationship("Team", back_populates="team_metrics")

    # Indexes
    __table_args__ = (
        Index('idx_team_metrics_team_date', 'team_id', 'date'),
        Index('idx_team_metrics_date', 'date'),
        Index('idx_team_metrics_team_id', 'team_id'),
    )


class BusinessMetric(Base):
    """Track business-level metrics for platform analytics."""
    __tablename__ = "business_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime, nullable=False)
    
    # User metrics
    total_users = Column(Integer, default=0, nullable=False)
    active_users_daily = Column(Integer, default=0, nullable=False)
    active_users_weekly = Column(Integer, default=0, nullable=False)
    active_users_monthly = Column(Integer, default=0, nullable=False)
    new_users = Column(Integer, default=0, nullable=False)
    churned_users = Column(Integer, default=0, nullable=False)
    
    # Subscription metrics
    total_subscriptions = Column(Integer, default=0, nullable=False)
    free_tier_users = Column(Integer, default=0, nullable=False)
    solo_tier_users = Column(Integer, default=0, nullable=False)
    team_tier_users = Column(Integer, default=0, nullable=False)
    enterprise_tier_users = Column(Integer, default=0, nullable=False)
    
    # Usage metrics
    total_completions = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, default=0, nullable=False)
    total_api_requests = Column(Integer, default=0, nullable=False)
    
    # Revenue metrics
    total_revenue_cents = Column(Integer, default=0, nullable=False)
    mrr_cents = Column(Integer, default=0, nullable=False)  # Monthly Recurring Revenue
    arr_cents = Column(Integer, default=0, nullable=False)  # Annual Recurring Revenue
    
    # Performance metrics
    avg_response_time_ms = Column(Float, default=0.0, nullable=False)
    error_rate = Column(Float, default=0.0, nullable=False)
    uptime_percentage = Column(Float, default=100.0, nullable=False)
    
    # Model usage
    model_usage_distribution = Column(JSONB, nullable=True)
    
    # Geographic distribution
    geographic_distribution = Column(JSONB, nullable=True)
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Indexes
    __table_args__ = (
        Index('idx_business_metrics_date', 'date'),
    )


class AlertRule(Base):
    """Define alert rules for monitoring metrics."""
    __tablename__ = "alert_rules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    metric_type = Column(String(100), nullable=False)  # user, team, business
    metric_name = Column(String(100), nullable=False)  # error_rate, response_time, etc.
    condition = Column(String(50), nullable=False)  # greater_than, less_than, equals
    threshold_value = Column(Float, nullable=False)
    time_window_minutes = Column(Integer, default=60, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    notification_channels = Column(JSONB, nullable=True)  # ["email", "slack", "webhook"]
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    creator = relationship("User", foreign_keys=[created_by])

    # Indexes
    __table_args__ = (
        Index('idx_alert_rules_active', 'is_active'),
        Index('idx_alert_rules_metric', 'metric_type', 'metric_name'),
    )


class Alert(Base):
    """Track fired alerts."""
    __tablename__ = "alerts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_id = Column(UUID(as_uuid=True), ForeignKey("alert_rules.id"), nullable=False)
    triggered_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    status = Column(String(50), default="active", nullable=False)  # active, resolved, suppressed
    current_value = Column(Float, nullable=False)
    threshold_value = Column(Float, nullable=False)
    message = Column(Text, nullable=True)
    metadata = Column(JSONB, nullable=True)
    notifications_sent = Column(JSONB, nullable=True)  # Track which notifications were sent
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    rule = relationship("AlertRule")

    # Indexes
    __table_args__ = (
        Index('idx_alerts_rule_id', 'rule_id'),
        Index('idx_alerts_status', 'status'),
        Index('idx_alerts_triggered_at', 'triggered_at'),
    )
