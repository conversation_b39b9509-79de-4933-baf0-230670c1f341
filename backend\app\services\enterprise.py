"""
Enterprise security service for SSO, audit logging, and advanced security features.
"""

import asyncio
import json
import ipaddress
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload
import uuid

from app.models.user import User
from app.models.enterprise import (
    Organization, SSOConfiguration, AuditLog, SecurityPolicy, 
    DataExportRequest, AuditEventType, SSOProvider
)
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class EnterpriseSecurityService:
    """Service for enterprise security and compliance features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_organization(
        self, 
        name: str, 
        slug: str,
        domain: Optional[str] = None,
        max_users: Optional[int] = None,
        creator: Optional[User] = None
    ) -> Organization:
        """
        Create a new enterprise organization.
        
        Args:
            name: Organization name
            slug: URL-friendly identifier
            domain: Primary domain for auto-assignment
            max_users: Maximum number of users
            creator: User creating the organization
            
        Returns:
            Created organization object
        """
        try:
            # Check if slug is unique
            existing = await self.db.execute(
                select(Organization).where(Organization.slug == slug)
            )
            if existing.scalar_one_or_none():
                raise ValueError(f"Organization slug '{slug}' already exists")
            
            organization = Organization(
                name=name,
                slug=slug,
                domain=domain,
                max_users=max_users
            )
            
            self.db.add(organization)
            await self.db.commit()
            await self.db.refresh(organization)
            
            # Log organization creation
            if creator:
                await self.log_audit_event(
                    organization=organization,
                    event_type=AuditEventType.ADMIN_ACTION,
                    event_category="organization",
                    event_description=f"Organization '{name}' created",
                    user=creator,
                    target_type="organization",
                    target_id=str(organization.id),
                    target_name=name
                )
            
            logger.info("Organization created successfully", 
                       org_id=str(organization.id),
                       org_name=name,
                       creator_id=str(creator.id) if creator else None)
            
            return organization
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create organization", 
                        error=str(e),
                        org_name=name,
                        creator_id=str(creator.id) if creator else None)
            raise
    
    async def configure_sso(
        self, 
        organization: Organization,
        name: str,
        provider_type: SSOProvider,
        provider_config: Dict[str, Any],
        attribute_mapping: Dict[str, str],
        creator: User,
        metadata_url: Optional[str] = None,
        role_mapping: Optional[Dict[str, str]] = None
    ) -> SSOConfiguration:
        """
        Configure SSO for an organization.
        
        Args:
            organization: Organization to configure SSO for
            name: SSO configuration name
            provider_type: Type of SSO provider
            provider_config: Provider-specific configuration
            attribute_mapping: Mapping of provider attributes to user fields
            creator: User creating the configuration
            metadata_url: Optional metadata URL for SAML/OIDC
            role_mapping: Optional role mapping configuration
            
        Returns:
            Created SSO configuration
        """
        try:
            sso_config = SSOConfiguration(
                organization_id=organization.id,
                name=name,
                provider_type=provider_type,
                provider_config=provider_config,
                metadata_url=metadata_url,
                attribute_mapping=attribute_mapping,
                role_mapping=role_mapping or {},
                created_by_id=creator.id
            )
            
            self.db.add(sso_config)
            await self.db.commit()
            await self.db.refresh(sso_config)
            
            # Log SSO configuration
            await self.log_audit_event(
                organization=organization,
                event_type=AuditEventType.SYSTEM_CONFIG_CHANGED,
                event_category="sso",
                event_description=f"SSO configuration '{name}' created for {provider_type.value}",
                user=creator,
                target_type="sso_configuration",
                target_id=str(sso_config.id),
                target_name=name
            )
            
            logger.info("SSO configuration created", 
                       org_id=str(organization.id),
                       sso_config_id=str(sso_config.id),
                       provider_type=provider_type.value,
                       creator_id=str(creator.id))
            
            return sso_config
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to configure SSO", 
                        error=str(e),
                        org_id=str(organization.id),
                        provider_type=provider_type.value,
                        creator_id=str(creator.id))
            raise
    
    async def log_audit_event(
        self,
        event_type: AuditEventType,
        event_category: str,
        event_description: str,
        organization: Optional[Organization] = None,
        user: Optional[User] = None,
        user_email: Optional[str] = None,
        actor_type: str = "user",
        target_type: Optional[str] = None,
        target_id: Optional[str] = None,
        target_name: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        risk_score: Optional[int] = None,
        risk_factors: Optional[List[str]] = None,
        compliance_tags: Optional[List[str]] = None
    ) -> AuditLog:
        """
        Log an audit event for compliance and security monitoring.
        
        Args:
            event_type: Type of audit event
            event_category: Category of the event
            event_description: Human-readable description
            organization: Organization context (optional)
            user: User who performed the action (optional)
            user_email: Email for deleted users (optional)
            actor_type: Type of actor (user, system, api)
            target_type: Type of target object (optional)
            target_id: ID of target object (optional)
            target_name: Name of target object (optional)
            ip_address: IP address of request (optional)
            user_agent: User agent string (optional)
            request_id: Request ID for correlation (optional)
            session_id: Session ID (optional)
            metadata: Additional metadata (optional)
            risk_score: Risk score 0-100 (optional)
            risk_factors: List of risk factors (optional)
            compliance_tags: Compliance framework tags (optional)
            
        Returns:
            Created audit log entry
        """
        try:
            audit_log = AuditLog(
                organization_id=organization.id if organization else None,
                event_type=event_type,
                event_category=event_category,
                event_description=event_description,
                user_id=user.id if user else None,
                user_email=user_email or (user.email if user else None),
                actor_type=actor_type,
                target_type=target_type,
                target_id=target_id,
                target_name=target_name,
                ip_address=ip_address,
                user_agent=user_agent,
                request_id=request_id,
                session_id=session_id,
                metadata=metadata,
                risk_score=risk_score,
                risk_factors=risk_factors,
                compliance_tags=compliance_tags
            )
            
            self.db.add(audit_log)
            await self.db.commit()
            
            # Log high-risk events immediately
            if risk_score and risk_score >= 80:
                logger.warning("High-risk audit event logged", 
                              event_type=event_type.value,
                              risk_score=risk_score,
                              user_id=str(user.id) if user else None,
                              org_id=str(organization.id) if organization else None)
            
            return audit_log
            
        except Exception as e:
            logger.error("Failed to log audit event", 
                        error=str(e),
                        event_type=event_type.value,
                        user_id=str(user.id) if user else None)
            # Don't raise exception for audit logging failures
            return None
    
    async def check_ip_access(
        self, 
        organization: Organization, 
        ip_address: str
    ) -> bool:
        """
        Check if an IP address is allowed for the organization.
        
        Args:
            organization: Organization to check
            ip_address: IP address to validate
            
        Returns:
            True if IP is allowed, False otherwise
        """
        try:
            # If no IP restrictions, allow all
            if not organization.allowed_ip_ranges:
                return True
            
            ip = ipaddress.ip_address(ip_address)
            
            for ip_range in organization.allowed_ip_ranges:
                network = ipaddress.ip_network(ip_range, strict=False)
                if ip in network:
                    return True
            
            # Log security violation
            await self.log_audit_event(
                organization=organization,
                event_type=AuditEventType.SECURITY_VIOLATION,
                event_category="access_control",
                event_description=f"IP address {ip_address} blocked by organization policy",
                actor_type="system",
                ip_address=ip_address,
                risk_score=70,
                risk_factors=["ip_restriction_violation"],
                compliance_tags=["access_control"]
            )
            
            return False
            
        except Exception as e:
            logger.error("Failed to check IP access", 
                        error=str(e),
                        org_id=str(organization.id),
                        ip_address=ip_address)
            # Default to deny on error
            return False
    
    async def create_data_export_request(
        self, 
        user: User,
        export_type: str,
        data_types: List[str],
        organization: Optional[Organization] = None,
        date_range_start: Optional[datetime] = None,
        date_range_end: Optional[datetime] = None,
        reason: Optional[str] = None
    ) -> DataExportRequest:
        """
        Create a data export request for compliance purposes.
        
        Args:
            user: User requesting the export
            export_type: Type of export (user_data, audit_logs, usage_data)
            data_types: List of specific data types to export
            organization: Organization context (optional)
            date_range_start: Start date for data range (optional)
            date_range_end: End date for data range (optional)
            reason: Reason for the export (optional)
            
        Returns:
            Created data export request
        """
        try:
            export_request = DataExportRequest(
                organization_id=organization.id if organization else None,
                user_id=user.id,
                export_type=export_type,
                data_types=data_types,
                date_range_start=date_range_start,
                date_range_end=date_range_end,
                reason=reason,
                expires_at=datetime.utcnow() + timedelta(days=30)  # 30 days to download
            )
            
            self.db.add(export_request)
            await self.db.commit()
            await self.db.refresh(export_request)
            
            # Log data export request
            await self.log_audit_event(
                organization=organization,
                event_type=AuditEventType.DATA_EXPORT,
                event_category="data_access",
                event_description=f"Data export requested: {export_type}",
                user=user,
                target_type="data_export_request",
                target_id=str(export_request.id),
                metadata={
                    "export_type": export_type,
                    "data_types": data_types,
                    "reason": reason
                },
                compliance_tags=["data_export", "gdpr"]
            )
            
            logger.info("Data export request created", 
                       export_request_id=str(export_request.id),
                       user_id=str(user.id),
                       export_type=export_type,
                       org_id=str(organization.id) if organization else None)
            
            return export_request
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create data export request", 
                        error=str(e),
                        user_id=str(user.id),
                        export_type=export_type)
            raise
    
    async def get_audit_logs(
        self,
        organization: Optional[Organization] = None,
        user: Optional[User] = None,
        event_types: Optional[List[AuditEventType]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        risk_score_min: Optional[int] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[AuditLog]:
        """
        Retrieve audit logs with filtering options.
        
        Args:
            organization: Filter by organization (optional)
            user: Filter by user (optional)
            event_types: Filter by event types (optional)
            start_date: Filter by start date (optional)
            end_date: Filter by end date (optional)
            risk_score_min: Minimum risk score filter (optional)
            limit: Maximum number of logs to return
            offset: Number of logs to skip
            
        Returns:
            List of audit log entries
        """
        try:
            query = select(AuditLog)
            
            # Apply filters
            conditions = []
            
            if organization:
                conditions.append(AuditLog.organization_id == organization.id)
            
            if user:
                conditions.append(AuditLog.user_id == user.id)
            
            if event_types:
                conditions.append(AuditLog.event_type.in_(event_types))
            
            if start_date:
                conditions.append(AuditLog.timestamp >= start_date)
            
            if end_date:
                conditions.append(AuditLog.timestamp <= end_date)
            
            if risk_score_min:
                conditions.append(AuditLog.risk_score >= risk_score_min)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # Order and paginate
            query = query.order_by(desc(AuditLog.timestamp)).limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            audit_logs = result.scalars().all()
            
            return list(audit_logs)
            
        except Exception as e:
            logger.error("Failed to retrieve audit logs", 
                        error=str(e),
                        org_id=str(organization.id) if organization else None,
                        user_id=str(user.id) if user else None)
            raise
