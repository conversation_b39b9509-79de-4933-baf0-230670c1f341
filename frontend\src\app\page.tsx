import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowRightIcon, CodeBracketIcon, SparklesIcon, ShieldCheckIcon } from '@heroicons/react/24/outline'

export const metadata: Metadata = {
  title: 'TDS Coder - AI-Powered Code Completion for Developers',
  description: 'Boost your coding productivity with TDS Coder\'s AI-powered code completion. Trusted by developers worldwide.',
}

const features = [
  {
    name: 'AI-Powered Completions',
    description: 'Get intelligent code suggestions powered by advanced AI models trained on millions of code repositories.',
    icon: SparklesIcon,
  },
  {
    name: 'Multi-Language Support',
    description: 'Support for 50+ programming languages including Python, JavaScript, TypeScript, Go, Rust, and more.',
    icon: CodeBracketIcon,
  },
  {
    name: 'Enterprise Security',
    description: 'Your code stays private with enterprise-grade security, encryption, and compliance standards.',
    icon: ShieldCheckIcon,
  },
]

const plans = [
  {
    name: 'Free',
    price: '$0',
    description: 'Perfect for getting started',
    features: [
      '100 completions per day',
      'Basic code completion',
      'Community support',
      'VS Code extension',
    ],
    cta: 'Get Started',
    href: '/auth/register',
    popular: false,
  },
  {
    name: 'Solo',
    price: '$19',
    description: 'For individual developers',
    features: [
      '10,000 completions per day',
      'Advanced AI models',
      'Chat with AI',
      'Priority support',
      'All IDE plugins',
    ],
    cta: 'Start Free Trial',
    href: '/auth/register?plan=solo',
    popular: true,
  },
  {
    name: 'Team',
    price: '$49',
    description: 'For development teams',
    features: [
      '50,000 completions per day',
      'Team dashboard',
      'Usage analytics',
      '5 team members',
      'Admin controls',
    ],
    cta: 'Start Free Trial',
    href: '/auth/register?plan=team',
    popular: false,
  },
]

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900">
      {/* Header */}
      <header className="relative">
        <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
          <div className="flex w-full items-center justify-between border-b border-indigo-500 py-6 lg:border-none">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-indigo-600 flex items-center justify-center">
                  <CodeBracketIcon className="h-5 w-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900 dark:text-white">TDS Coder</span>
              </Link>
            </div>
            <div className="ml-10 space-x-4">
              <Link
                href="/auth/login"
                className="inline-block rounded-md border border-transparent py-2 px-4 text-base font-medium text-indigo-600 hover:bg-opacity-75 dark:text-indigo-400"
              >
                Sign in
              </Link>
              <Link
                href="/auth/register"
                className="inline-block rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-base font-medium text-white hover:bg-indigo-700"
              >
                Get Started
              </Link>
            </div>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main>
        <div className="relative">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="py-24 text-center">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-6xl">
                <span className="block">AI-Powered</span>
                <span className="block text-gradient">Code Completion</span>
              </h1>
              <p className="mx-auto mt-6 max-w-2xl text-lg text-gray-600 dark:text-gray-300">
                Boost your coding productivity with intelligent AI suggestions. 
                Write better code faster with TDS Coder's advanced AI assistant.
              </p>
              <div className="mt-10 flex justify-center gap-x-6">
                <Link
                  href="/auth/register"
                  className="rounded-md bg-indigo-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Start Free Trial
                </Link>
                <Link
                  href="/docs"
                  className="rounded-md px-6 py-3 text-base font-semibold text-gray-900 dark:text-white ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:ring-gray-400 dark:hover:ring-gray-500"
                >
                  View Documentation <ArrowRightIcon className="ml-2 h-4 w-4 inline" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-24 bg-white dark:bg-gray-800">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                Everything you need to code faster
              </h2>
              <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                Powerful AI features designed for modern developers
              </p>
            </div>
            <div className="mt-20">
              <div className="grid grid-cols-1 gap-12 sm:grid-cols-2 lg:grid-cols-3">
                {features.map((feature) => (
                  <div key={feature.name} className="relative">
                    <div className="flex h-12 w-12 items-center justify-center rounded-md bg-indigo-500 text-white">
                      <feature.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                    <div className="mt-6">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">{feature.name}</h3>
                      <p className="mt-2 text-base text-gray-500 dark:text-gray-400">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="py-24 bg-gray-50 dark:bg-gray-900">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
                Simple, transparent pricing
              </h2>
              <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
                Choose the plan that's right for you
              </p>
            </div>
            <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {plans.map((plan) => (
                <div
                  key={plan.name}
                  className={`relative rounded-2xl border ${
                    plan.popular
                      ? 'border-indigo-500 shadow-lg'
                      : 'border-gray-200 dark:border-gray-700'
                  } bg-white dark:bg-gray-800 p-8`}
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                      <span className="rounded-full bg-indigo-500 px-4 py-1 text-sm font-semibold text-white">
                        Most Popular
                      </span>
                    </div>
                  )}
                  <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{plan.name}</h3>
                    <div className="mt-4 flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                      <span className="ml-1 text-xl text-gray-500 dark:text-gray-400">/month</span>
                    </div>
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">{plan.description}</p>
                  </div>
                  <ul className="mt-8 space-y-3">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-center">
                        <svg
                          className="h-5 w-5 text-indigo-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="mt-8">
                    <Link
                      href={plan.href}
                      className={`block w-full rounded-md px-4 py-2 text-center text-sm font-semibold ${
                        plan.popular
                          ? 'bg-indigo-600 text-white hover:bg-indigo-500'
                          : 'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600'
                      }`}
                    >
                      {plan.cta}
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800">
        <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-indigo-600 flex items-center justify-center">
                <CodeBracketIcon className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">TDS Coder</span>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              © 2024 TDS Coder. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
