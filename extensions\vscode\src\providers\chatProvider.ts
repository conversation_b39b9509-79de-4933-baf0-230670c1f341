import * as vscode from 'vscode';
import axios from 'axios';
import { AuthenticationManager } from '../auth/authManager';
import { ConfigurationManager } from '../config/configManager';
import { TelemetryManager } from '../telemetry/telemetryManager';

export class TDSCoderChatProvider {
    constructor(
        private authManager: AuthenticationManager,
        private configManager: ConfigurationManager,
        private telemetryManager: TelemetryManager
    ) {}

    async sendChatMessage(message: string, context?: any): Promise<string> {
        if (!this.authManager.isAuthenticated()) {
            throw new Error('Not authenticated');
        }

        const apiKey = this.authManager.getApiKey();
        const apiUrl = this.configManager.getApiUrl();

        if (!apiKey) {
            throw new Error('No API key available');
        }

        try {
            const requestData = {
                messages: [
                    {
                        role: 'user',
                        content: message
                    }
                ],
                model: this.configManager.getModel() === 'auto' ? undefined : this.configManager.getModel(),
                max_tokens: this.configManager.getMaxTokens(),
                temperature: this.configManager.getTemperature()
            };

            const response = await axios.post(
                `${apiUrl}/api/v1/v1/chat/completions`,
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 30000 // 30 second timeout for chat
                }
            );

            if (response.data && response.data.choices && response.data.choices.length > 0) {
                const assistantMessage = response.data.choices[0].message.content;
                
                // Track telemetry
                this.telemetryManager.trackEvent('chat_message_sent', {
                    messageLength: message.length,
                    responseLength: assistantMessage.length,
                    model: response.data.model,
                    tokens: response.data.usage?.total_tokens
                });

                return assistantMessage;
            }

            throw new Error('No response from chat API');

        } catch (error) {
            this.telemetryManager.trackEvent('chat_error', {
                error: error instanceof Error ? error.message : 'Unknown error',
                messageLength: message.length
            });

            if (axios.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    throw new Error('Authentication failed. Please check your API key.');
                } else if (error.response?.status === 429) {
                    throw new Error('Rate limit exceeded. Please try again later.');
                } else if (error.response?.status === 402) {
                    throw new Error('Quota exceeded. Please upgrade your plan.');
                }
            }

            throw error;
        }
    }
}
