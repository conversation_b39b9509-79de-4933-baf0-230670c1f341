"""
User service for user management operations.
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime
import secrets

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.auth import AuthService
from app.core.logging import get_logger

logger = get_logger(__name__)


class UserService:
    """Service for user management operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(self, user_data: UserCreate) -> User:
        """
        Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            Created user object
        """
        try:
            # Hash password
            hashed_password = AuthService.hash_password(user_data.password)
            
            # Generate email verification token
            verification_token = secrets.token_urlsafe(32)
            
            # Create user
            user = User(
                email=user_data.email,
                username=user_data.username,
                full_name=user_data.full_name,
                company=user_data.company,
                timezone=user_data.timezone,
                theme=user_data.theme,
                language=user_data.language,
                email_notifications=user_data.email_notifications,
                marketing_emails=user_data.marketing_emails,
                hashed_password=hashed_password,
                email_verification_token=verification_token,
                email_verification_expires=datetime.utcnow() + timedelta(hours=24),
            )
            
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            logger.info("User created", user_id=str(user.id), email=user.email)
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error("User creation failed", error=str(e), email=user_data.email)
            raise
    
    async def get_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        try:
            stmt = select(User).where(User.id == user_id, User.is_deleted == False)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user by ID", error=str(e), user_id=user_id)
            return None
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        try:
            stmt = select(User).where(User.email == email, User.is_deleted == False)
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error("Failed to get user by email", error=str(e), email=email)
            return None
    
    async def update(self, user: User, user_data: UserUpdate) -> User:
        """Update user data."""
        try:
            # Update fields
            for field, value in user_data.dict(exclude_unset=True).items():
                setattr(user, field, value)
            
            await self.db.commit()
            await self.db.refresh(user)
            
            logger.info("User updated", user_id=str(user.id))
            return user
            
        except Exception as e:
            await self.db.rollback()
            logger.error("User update failed", error=str(e), user_id=str(user.id))
            raise
    
    async def delete(self, user: User) -> bool:
        """Soft delete user."""
        try:
            user.soft_delete()
            await self.db.commit()
            
            logger.info("User deleted", user_id=str(user.id))
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error("User deletion failed", error=str(e), user_id=str(user.id))
            return False
