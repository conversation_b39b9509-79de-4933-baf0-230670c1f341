"""
API key management endpoints.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Any, List, Optional
from pydantic import BaseModel, Field
import uuid

from app.db.session import get_db
from app.utils.security import get_current_active_user
from app.models.user import User
from app.models.api_key import APIKey
from app.services.api_key import APIKeyService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class APIKeyCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    expires_days: Optional[int] = Field(None, ge=1, le=365)
    scopes: Optional[List[str]] = None
    allowed_ips: Optional[List[str]] = None
    custom_daily_limit: Optional[int] = Field(None, ge=1)
    custom_burst_limit: Optional[int] = Field(None, ge=1)


class APIKeyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    scopes: Optional[List[str]] = None
    allowed_ips: Optional[List[str]] = None
    custom_daily_limit: Optional[int] = Field(None, ge=1)
    custom_burst_limit: Optional[int] = Field(None, ge=1)


class APIKeyResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    masked_key: str
    is_active: bool
    is_revoked: bool
    expires_at: Optional[str]
    never_expires: bool
    last_used: Optional[str]
    usage_count: int
    scopes: Optional[List[str]]
    allowed_ips: Optional[List[str]]
    custom_daily_limit: Optional[int]
    custom_burst_limit: Optional[int]
    created_at: str
    days_until_expiry: int


@router.get("/", response_model=List[APIKeyResponse])
async def list_api_keys(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """List user's API keys."""
    try:
        api_key_service = APIKeyService(db)
        api_keys = await api_key_service.get_user_api_keys(current_user)

        return [
            APIKeyResponse(
                id=str(api_key.id),
                name=api_key.name,
                description=api_key.description,
                masked_key=api_key.masked_key,
                is_active=api_key.is_active,
                is_revoked=api_key.is_revoked,
                expires_at=api_key.expires_at.isoformat() if api_key.expires_at else None,
                never_expires=api_key.never_expires,
                last_used=api_key.last_used.isoformat() if api_key.last_used else None,
                usage_count=api_key.usage_count,
                scopes=api_key.get_scopes(),
                allowed_ips=api_key.get_allowed_ips(),
                custom_daily_limit=api_key.custom_daily_limit,
                custom_burst_limit=api_key.custom_burst_limit,
                created_at=api_key.created_at.isoformat(),
                days_until_expiry=api_key.days_until_expiry,
            )
            for api_key in api_keys
        ]

    except Exception as e:
        logger.error("Failed to list API keys", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve API keys"
        )


@router.post("/")
async def create_api_key(
    api_key_data: APIKeyCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Create new API key."""
    try:
        api_key_service = APIKeyService(db)

        # Check if user has reached API key limit
        existing_keys = await api_key_service.get_user_api_keys(current_user)
        active_keys = [key for key in existing_keys if key.is_valid]

        # Limit based on subscription tier (free: 2, solo: 5, team: 10, enterprise: unlimited)
        subscription = await api_key_service.get_user_subscription(current_user)
        max_keys = {
            "free": 2,
            "solo": 5,
            "team": 10,
            "enterprise": -1  # unlimited
        }.get(subscription.tier.value, 2)

        if max_keys != -1 and len(active_keys) >= max_keys:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Maximum number of API keys ({max_keys}) reached for your plan"
            )

        # Create the API key
        api_key, raw_key = await api_key_service.create_api_key(
            user=current_user,
            name=api_key_data.name,
            description=api_key_data.description,
            expires_days=api_key_data.expires_days,
            scopes=api_key_data.scopes,
            allowed_ips=api_key_data.allowed_ips,
            custom_daily_limit=api_key_data.custom_daily_limit,
            custom_burst_limit=api_key_data.custom_burst_limit,
        )

        logger.info("API key created",
                   api_key_id=str(api_key.id),
                   user_id=str(current_user.id))

        return {
            "id": str(api_key.id),
            "name": api_key.name,
            "key": raw_key,  # Only returned once during creation
            "masked_key": api_key.masked_key,
            "expires_at": api_key.expires_at.isoformat() if api_key.expires_at else None,
            "never_expires": api_key.never_expires,
            "scopes": api_key.get_scopes(),
            "allowed_ips": api_key.get_allowed_ips(),
            "created_at": api_key.created_at.isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create API key", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create API key"
        )


@router.put("/{key_id}")
async def update_api_key(
    key_id: str,
    api_key_data: APIKeyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Update API key settings."""
    try:
        api_key_service = APIKeyService(db)

        # Get the API key
        api_key = await api_key_service.get_api_key_by_id(key_id, current_user)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Update the API key
        updated_key = await api_key_service.update_api_key(api_key, api_key_data)

        logger.info("API key updated",
                   api_key_id=str(api_key.id),
                   user_id=str(current_user.id))

        return {
            "id": str(updated_key.id),
            "name": updated_key.name,
            "description": updated_key.description,
            "masked_key": updated_key.masked_key,
            "scopes": updated_key.get_scopes(),
            "allowed_ips": updated_key.get_allowed_ips(),
            "custom_daily_limit": updated_key.custom_daily_limit,
            "custom_burst_limit": updated_key.custom_burst_limit,
            "updated_at": updated_key.updated_at.isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update API key", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update API key"
        )


@router.delete("/{key_id}")
async def revoke_api_key(
    key_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Revoke API key."""
    try:
        api_key_service = APIKeyService(db)

        # Get the API key
        api_key = await api_key_service.get_api_key_by_id(key_id, current_user)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Revoke the API key
        await api_key_service.revoke_api_key(api_key, "Revoked by user")

        logger.info("API key revoked",
                   api_key_id=str(api_key.id),
                   user_id=str(current_user.id))

        return {"message": "API key revoked successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to revoke API key", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke API key"
        )


@router.get("/{key_id}/usage")
async def get_api_key_usage(
    key_id: str,
    days: int = 30,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Get usage statistics for a specific API key."""
    try:
        api_key_service = APIKeyService(db)

        # Get the API key
        api_key = await api_key_service.get_api_key_by_id(key_id, current_user)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )

        # Get usage statistics
        usage_stats = await api_key_service.get_api_key_usage_stats(api_key, days)

        return usage_stats

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get API key usage", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage statistics"
        )
