export type SubscriptionStatus = 
  | 'active' 
  | 'inactive' 
  | 'canceled' 
  | 'past_due' 
  | 'unpaid' 
  | 'trialing' 
  | 'paused'

export type SubscriptionTier = 'free' | 'solo' | 'team' | 'enterprise'

export interface Subscription {
  id: string
  user_id: string
  tier: SubscriptionTier
  status: SubscriptionStatus
  stripe_customer_id?: string
  stripe_subscription_id?: string
  stripe_price_id?: string
  current_period_start?: string
  current_period_end?: string
  trial_start?: string
  trial_end?: string
  canceled_at?: string
  ended_at?: string
  amount?: number
  currency: string
  daily_request_limit: number
  burst_limit: number
  seats_limit: number
  features?: string[]
  billing_name?: string
  billing_email?: string
  billing_address?: {
    line1?: string
    line2?: string
    city?: string
    state?: string
    postal_code?: string
    country?: string
  }
  tax_id?: string
  tax_rate?: number
  proration_amount: number
  credit_balance: number
  failed_payment_count: number
  last_failed_payment?: string
  next_retry_date?: string
  pause_collection: boolean
  pause_start?: string
  pause_end?: string
  created_at: string
  updated_at: string
}

export interface SubscriptionPlan {
  id: string
  name: string
  tier: SubscriptionTier
  price: number
  currency: string
  interval: 'month' | 'year'
  daily_requests: number
  burst_limit: number
  seats: number
  features: string[]
  popular?: boolean
  stripe_price_id: string
}

export interface UsageLimits {
  daily_requests: number
  burst_limit: number
  seats: number
}

export interface BillingInfo {
  name?: string
  email?: string
  address?: {
    line1?: string
    line2?: string
    city?: string
    state?: string
    postal_code?: string
    country?: string
  }
  tax_id?: string
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'bank_account'
  card?: {
    brand: string
    last4: string
    exp_month: number
    exp_year: number
  }
  bank_account?: {
    bank_name: string
    last4: string
    account_type: string
  }
  is_default: boolean
  created_at: string
}

export interface Invoice {
  id: string
  number: string
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible'
  amount_due: number
  amount_paid: number
  currency: string
  description?: string
  invoice_pdf?: string
  hosted_invoice_url?: string
  period_start: string
  period_end: string
  due_date?: string
  paid_at?: string
  created_at: string
}
