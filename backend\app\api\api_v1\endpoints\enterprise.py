"""
Enterprise security and deployment endpoints for SSO, audit logging, and advanced features.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from datetime import datetime, date
from pydantic import BaseModel, Field, EmailStr
import uuid

from app.db.session import get_db
from app.utils.security import get_current_active_user, require_admin
from app.models.user import User
from app.models.enterprise import (
    Organization, SSOConfiguration, AuditLog, DataExportRequest,
    AuditEventType, SSOProvider
)
from app.services.enterprise import EnterpriseSecurityService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class OrganizationCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Organization name")
    slug: str = Field(..., min_length=1, max_length=100, description="URL-friendly identifier")
    domain: Optional[str] = Field(None, description="Primary domain for auto-assignment")
    max_users: Optional[int] = Field(None, ge=1, description="Maximum number of users")


class SSOConfigurationCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="SSO configuration name")
    provider_type: SSOProvider = Field(..., description="SSO provider type")
    provider_config: dict = Field(..., description="Provider-specific configuration")
    metadata_url: Optional[str] = Field(None, description="Metadata URL for SAML/OIDC")
    attribute_mapping: dict = Field(..., description="Attribute mapping configuration")
    role_mapping: Optional[dict] = Field(None, description="Role mapping configuration")


class DataExportCreate(BaseModel):
    export_type: str = Field(..., description="Type of export (user_data, audit_logs, usage_data)")
    data_types: List[str] = Field(..., description="List of data types to export")
    date_range_start: Optional[datetime] = Field(None, description="Start date for data range")
    date_range_end: Optional[datetime] = Field(None, description="End date for data range")
    reason: Optional[str] = Field(None, max_length=1000, description="Reason for export")


@router.post("/organizations", dependencies=[Depends(require_admin)])
async def create_organization(
    org_data: OrganizationCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a new enterprise organization.
    
    **Admin access required.**
    
    Creates an organization for enterprise customers with advanced
    security features, SSO integration, and compliance capabilities.
    """
    try:
        enterprise_service = EnterpriseSecurityService(db)
        organization = await enterprise_service.create_organization(
            name=org_data.name,
            slug=org_data.slug,
            domain=org_data.domain,
            max_users=org_data.max_users,
            creator=current_user
        )
        
        return {
            "id": str(organization.id),
            "name": organization.name,
            "slug": organization.slug,
            "domain": organization.domain,
            "max_users": organization.max_users,
            "is_active": organization.is_active,
            "created_at": organization.created_at.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to create organization", 
                    error=str(e), 
                    admin_user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization"
        )


@router.get("/organizations", dependencies=[Depends(require_admin)])
async def list_organizations(
    limit: int = Query(50, ge=1, le=100, description="Maximum number of organizations to return"),
    offset: int = Query(0, ge=0, description="Number of organizations to skip"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    List all organizations.
    
    **Admin access required.**
    
    Returns a paginated list of all organizations in the system.
    """
    try:
        from sqlalchemy import select
        
        query = select(Organization).where(
            Organization.is_deleted == False
        ).order_by(Organization.created_at.desc()).limit(limit).offset(offset)
        
        result = await db.execute(query)
        organizations = result.scalars().all()
        
        return [
            {
                "id": str(org.id),
                "name": org.name,
                "slug": org.slug,
                "domain": org.domain,
                "max_users": org.max_users,
                "is_active": org.is_active,
                "created_at": org.created_at.isoformat()
            }
            for org in organizations
        ]
        
    except Exception as e:
        logger.error("Failed to list organizations", 
                    error=str(e), 
                    admin_user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organizations"
        )


@router.post("/organizations/{org_id}/sso", dependencies=[Depends(require_admin)])
async def configure_sso(
    org_id: str,
    sso_data: SSOConfigurationCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Configure SSO for an organization.
    
    **Admin access required.**
    
    Sets up SAML, OIDC, or other SSO providers for enterprise authentication.
    """
    try:
        organization = await db.get(Organization, org_id)
        if not organization or organization.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        enterprise_service = EnterpriseSecurityService(db)
        sso_config = await enterprise_service.configure_sso(
            organization=organization,
            name=sso_data.name,
            provider_type=sso_data.provider_type,
            provider_config=sso_data.provider_config,
            attribute_mapping=sso_data.attribute_mapping,
            creator=current_user,
            metadata_url=sso_data.metadata_url,
            role_mapping=sso_data.role_mapping
        )
        
        return {
            "id": str(sso_config.id),
            "name": sso_config.name,
            "provider_type": sso_config.provider_type.value,
            "is_active": sso_config.is_active,
            "is_default": sso_config.is_default,
            "created_at": sso_config.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to configure SSO", 
                    error=str(e), 
                    org_id=org_id,
                    admin_user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to configure SSO"
        )


@router.get("/audit-logs")
async def get_audit_logs(
    event_types: Optional[str] = Query(None, description="Comma-separated list of event types"),
    start_date: Optional[datetime] = Query(None, description="Start date for filtering"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering"),
    risk_score_min: Optional[int] = Query(None, ge=0, le=100, description="Minimum risk score"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Retrieve audit logs for compliance and security monitoring.
    
    Returns audit logs that the user has access to based on their permissions.
    Admin users can see all logs, while regular users see only their own.
    """
    try:
        enterprise_service = EnterpriseSecurityService(db)
        
        # Parse event types
        event_type_list = None
        if event_types:
            try:
                event_type_list = [
                    AuditEventType(event_type.strip()) 
                    for event_type in event_types.split(',') 
                    if event_type.strip()
                ]
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid event type: {e}"
                )
        
        # Determine access scope
        user_filter = None if current_user.is_superuser else current_user
        
        audit_logs = await enterprise_service.get_audit_logs(
            user=user_filter,
            event_types=event_type_list,
            start_date=start_date,
            end_date=end_date,
            risk_score_min=risk_score_min,
            limit=limit,
            offset=offset
        )
        
        return [
            {
                "id": str(log.id),
                "event_type": log.event_type.value,
                "event_category": log.event_category,
                "event_description": log.event_description,
                "user_email": log.user_email,
                "actor_type": log.actor_type,
                "target_type": log.target_type,
                "target_name": log.target_name,
                "ip_address": str(log.ip_address) if log.ip_address else None,
                "risk_score": log.risk_score,
                "risk_factors": log.risk_factors,
                "compliance_tags": log.compliance_tags,
                "timestamp": log.timestamp.isoformat(),
                "metadata": log.metadata
            }
            for log in audit_logs
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to retrieve audit logs", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve audit logs"
        )


@router.post("/data-export")
async def create_data_export_request(
    export_data: DataExportCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a data export request for compliance purposes.
    
    Allows users to request exports of their data for GDPR compliance,
    data portability, or other regulatory requirements.
    """
    try:
        enterprise_service = EnterpriseSecurityService(db)
        
        # Get user's organization if applicable
        organization = None  # Would need to implement organization lookup
        
        export_request = await enterprise_service.create_data_export_request(
            user=current_user,
            export_type=export_data.export_type,
            data_types=export_data.data_types,
            organization=organization,
            date_range_start=export_data.date_range_start,
            date_range_end=export_data.date_range_end,
            reason=export_data.reason
        )
        
        return {
            "id": str(export_request.id),
            "export_type": export_request.export_type,
            "data_types": export_request.data_types,
            "status": export_request.status,
            "expires_at": export_request.expires_at.isoformat(),
            "created_at": export_request.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to create data export request", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create data export request"
        )


@router.get("/data-export")
async def list_data_export_requests(
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of requests to return"),
    offset: int = Query(0, ge=0, description="Number of requests to skip"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    List data export requests for the current user.
    
    Returns export requests created by the current user, with optional status filtering.
    """
    try:
        from sqlalchemy import select
        
        query = select(DataExportRequest).where(
            and_(
                DataExportRequest.user_id == current_user.id,
                DataExportRequest.is_deleted == False
            )
        )
        
        if status:
            query = query.where(DataExportRequest.status == status)
        
        query = query.order_by(DataExportRequest.created_at.desc()).limit(limit).offset(offset)
        
        result = await db.execute(query)
        export_requests = result.scalars().all()
        
        return [
            {
                "id": str(req.id),
                "export_type": req.export_type,
                "data_types": req.data_types,
                "status": req.status,
                "file_size_bytes": req.file_size_bytes,
                "expires_at": req.expires_at.isoformat() if req.expires_at else None,
                "downloaded_at": req.downloaded_at.isoformat() if req.downloaded_at else None,
                "created_at": req.created_at.isoformat()
            }
            for req in export_requests
        ]
        
    except Exception as e:
        logger.error("Failed to list data export requests", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve export requests"
        )
