import * as vscode from 'vscode';

export class ConfigurationManager {
    private static readonly EXTENSION_ID = 'tdsCoder';
    
    private configuration: vscode.WorkspaceConfiguration;

    constructor() {
        this.configuration = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_ID);
    }

    reloadConfiguration(): void {
        this.configuration = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_ID);
    }

    // API Configuration
    getApiKey(): string {
        return this.configuration.get<string>('apiKey', '');
    }

    getApiUrl(): string {
        return this.configuration.get<string>('apiUrl', 'https://api.tdscoder.com');
    }

    // Model Configuration
    getModel(): string {
        return this.configuration.get<string>('model', 'auto');
    }

    getMaxTokens(): number {
        return this.configuration.get<number>('maxTokens', 150);
    }

    getTemperature(): number {
        return this.configuration.get<number>('temperature', 0.2);
    }

    // Feature Toggles
    getEnableInlineCompletion(): boolean {
        return this.configuration.get<boolean>('enableInlineCompletion', true);
    }

    getEnableChat(): boolean {
        return this.configuration.get<boolean>('enableChat', true);
    }

    getEnableTelemetry(): boolean {
        return this.configuration.get<boolean>('enableTelemetry', true);
    }

    getEnableOfflineMode(): boolean {
        return this.configuration.get<boolean>('enableOfflineMode', false);
    }

    getEnableLogging(): boolean {
        return this.configuration.get<boolean>('enableLogging', false);
    }

    // Performance Configuration
    getCompletionDelay(): number {
        return this.configuration.get<number>('completionDelay', 300);
    }

    getCacheSize(): number {
        return this.configuration.get<number>('cacheSize', 1000);
    }

    // Update Settings
    async updateSetting(key: string, value: any, target?: vscode.ConfigurationTarget): Promise<void> {
        try {
            await this.configuration.update(
                key, 
                value, 
                target || vscode.ConfigurationTarget.Global
            );
            this.reloadConfiguration();
        } catch (error) {
            console.error(`Failed to update setting ${key}:`, error);
            throw new Error(`Failed to update setting: ${key}`);
        }
    }

    // Validation Methods
    validateConfiguration(): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // Validate API URL
        const apiUrl = this.getApiUrl();
        if (!apiUrl || !this.isValidUrl(apiUrl)) {
            errors.push('Invalid API URL');
        }

        // Validate model settings
        const maxTokens = this.getMaxTokens();
        if (maxTokens < 1 || maxTokens > 4000) {
            errors.push('Max tokens must be between 1 and 4000');
        }

        const temperature = this.getTemperature();
        if (temperature < 0 || temperature > 2) {
            errors.push('Temperature must be between 0 and 2');
        }

        const completionDelay = this.getCompletionDelay();
        if (completionDelay < 100 || completionDelay > 2000) {
            errors.push('Completion delay must be between 100 and 2000 milliseconds');
        }

        const cacheSize = this.getCacheSize();
        if (cacheSize < 100 || cacheSize > 10000) {
            errors.push('Cache size must be between 100 and 10000');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    private isValidUrl(url: string): boolean {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    // Get all configuration as object
    getAllConfiguration(): any {
        return {
            apiKey: this.getApiKey() ? '***' : '', // Mask API key
            apiUrl: this.getApiUrl(),
            model: this.getModel(),
            maxTokens: this.getMaxTokens(),
            temperature: this.getTemperature(),
            enableInlineCompletion: this.getEnableInlineCompletion(),
            enableChat: this.getEnableChat(),
            enableTelemetry: this.getEnableTelemetry(),
            enableOfflineMode: this.getEnableOfflineMode(),
            enableLogging: this.getEnableLogging(),
            completionDelay: this.getCompletionDelay(),
            cacheSize: this.getCacheSize()
        };
    }

    // Reset to defaults
    async resetToDefaults(): Promise<void> {
        const defaultSettings = [
            { key: 'apiKey', value: '' },
            { key: 'apiUrl', value: 'https://api.tdscoder.com' },
            { key: 'model', value: 'auto' },
            { key: 'maxTokens', value: 150 },
            { key: 'temperature', value: 0.2 },
            { key: 'enableInlineCompletion', value: true },
            { key: 'enableChat', value: true },
            { key: 'enableTelemetry', value: true },
            { key: 'enableOfflineMode', value: false },
            { key: 'enableLogging', value: false },
            { key: 'completionDelay', value: 300 },
            { key: 'cacheSize', value: 1000 }
        ];

        for (const setting of defaultSettings) {
            await this.updateSetting(setting.key, setting.value);
        }
    }

    // Export/Import Configuration
    exportConfiguration(): string {
        const config = this.getAllConfiguration();
        // Remove sensitive data
        delete config.apiKey;
        return JSON.stringify(config, null, 2);
    }

    async importConfiguration(configJson: string): Promise<void> {
        try {
            const config = JSON.parse(configJson);
            
            // Validate imported configuration
            const validKeys = [
                'apiUrl', 'model', 'maxTokens', 'temperature',
                'enableInlineCompletion', 'enableChat', 'enableTelemetry',
                'enableOfflineMode', 'enableLogging', 'completionDelay', 'cacheSize'
            ];

            for (const [key, value] of Object.entries(config)) {
                if (validKeys.includes(key)) {
                    await this.updateSetting(key, value);
                }
            }

        } catch (error) {
            throw new Error('Invalid configuration format');
        }
    }

    // Configuration change listeners
    onConfigurationChanged(callback: (event: vscode.ConfigurationChangeEvent) => void): vscode.Disposable {
        return vscode.workspace.onDidChangeConfiguration((event) => {
            if (event.affectsConfiguration(ConfigurationManager.EXTENSION_ID)) {
                this.reloadConfiguration();
                callback(event);
            }
        });
    }

    // Get workspace-specific settings
    getWorkspaceConfiguration(): any {
        const workspaceConfig = vscode.workspace.getConfiguration(ConfigurationManager.EXTENSION_ID, vscode.ConfigurationTarget.Workspace);
        return {
            hasWorkspaceSettings: workspaceConfig.has('apiKey') || workspaceConfig.has('model'),
            settings: {
                model: workspaceConfig.get('model'),
                maxTokens: workspaceConfig.get('maxTokens'),
                temperature: workspaceConfig.get('temperature'),
                enableInlineCompletion: workspaceConfig.get('enableInlineCompletion')
            }
        };
    }

    // Language-specific settings
    getLanguageSpecificSettings(languageId: string): any {
        const languageConfig = vscode.workspace.getConfiguration(`[${languageId}]`);
        const tdsCoderConfig = languageConfig.get(ConfigurationManager.EXTENSION_ID, {});
        
        return {
            model: tdsCoderConfig.model || this.getModel(),
            maxTokens: tdsCoderConfig.maxTokens || this.getMaxTokens(),
            temperature: tdsCoderConfig.temperature || this.getTemperature(),
            enableInlineCompletion: tdsCoderConfig.enableInlineCompletion !== undefined 
                ? tdsCoderConfig.enableInlineCompletion 
                : this.getEnableInlineCompletion()
        };
    }
}
