@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-foreground;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dark .shimmer {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200px 100%;
}

/* Code syntax highlighting */
.code-block {
  @apply bg-secondary rounded-lg p-4 overflow-x-auto;
}

.code-block pre {
  @apply m-0;
}

.code-block code {
  @apply text-sm font-mono;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
}

/* Button variants */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 focus-ring;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus-ring;
}

.btn-destructive {
  @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-ring;
}

/* Form styles */
.form-input {
  @apply w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus-ring;
}

.form-label {
  @apply block text-sm font-medium text-foreground mb-1;
}

.form-error {
  @apply text-destructive text-sm mt-1;
}

/* Card styles */
.card {
  @apply bg-card text-card-foreground rounded-lg border shadow-sm;
}

.card-header {
  @apply p-6 pb-0;
}

.card-content {
  @apply p-6;
}

.card-footer {
  @apply p-6 pt-0;
}

/* Navigation styles */
.nav-link {
  @apply text-muted-foreground hover:text-foreground transition-colors;
}

.nav-link.active {
  @apply text-foreground font-medium;
}

/* Utility classes */
.text-gradient {
  @apply bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent;
}

.glass {
  @apply bg-background/80 backdrop-blur-sm border border-border/50;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
