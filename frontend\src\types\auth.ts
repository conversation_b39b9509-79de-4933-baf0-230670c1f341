export interface User {
  id: string
  email: string
  username?: string
  full_name?: string
  company?: string
  timezone: string
  theme: string
  language: string
  email_notifications: boolean
  marketing_emails: boolean
  is_active: boolean
  is_verified: boolean
  role: 'user' | 'admin' | 'super_admin'
  avatar_url?: string
  bio?: string
  website?: string
  location?: string
  is_2fa_enabled: boolean
  last_login?: string
  last_activity?: string
  created_at: string
  updated_at: string
}

export interface LoginCredentials {
  email: string
  password: string
  remember_me?: boolean
  totp_code?: string
}

export interface RegisterData {
  email: string
  username?: string
  full_name?: string
  company?: string
  password: string
  confirm_password: string
  timezone?: string
  marketing_emails?: boolean
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  new_password: string
  confirm_password: string
}

export interface ChangePasswordData {
  current_password: string
  new_password: string
  confirm_password: string
}

export interface TwoFactorSetup {
  totp_secret: string
  totp_code: string
}

export interface TwoFactorDisable {
  password: string
  totp_code: string
}
