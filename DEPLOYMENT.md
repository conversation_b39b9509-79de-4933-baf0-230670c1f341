# TDS Coder - Enterprise Deployment Guide

This guide covers deployment options for TDS Coder, from development to enterprise-scale production deployments.

## Table of Contents

1. [Quick Start (Development)](#quick-start-development)
2. [Docker Compose (Production)](#docker-compose-production)
3. [Kubernetes (Enterprise)](#kubernetes-enterprise)
4. [Security Configuration](#security-configuration)
5. [Monitoring & Observability](#monitoring--observability)
6. [Backup & Recovery](#backup--recovery)
7. [Scaling & Performance](#scaling--performance)
8. [Troubleshooting](#troubleshooting)

## Quick Start (Development)

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Local Development Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/tdscoder/platform.git
   cd platform
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development services:**
   ```bash
   docker-compose up -d postgres redis
   ```

4. **Run backend:**
   ```bash
   cd backend
   pip install -r requirements.txt
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

5. **Run frontend:**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

6. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Docker Compose (Production)

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ disk space

### Production Deployment

1. **Prepare the environment:**
   ```bash
   # Create production directory
   mkdir tds-coder-prod
   cd tds-coder-prod
   
   # Download deployment files
   wget https://raw.githubusercontent.com/tdscoder/platform/main/docker-compose.prod.yml
   wget https://raw.githubusercontent.com/tdscoder/platform/main/.env.prod.example
   ```

2. **Configure environment:**
   ```bash
   cp .env.prod.example .env
   # Edit .env with your production settings
   ```

3. **Required environment variables:**
   ```bash
   # Security
   SECRET_KEY=your-super-secret-key-here
   
   # Database
   POSTGRES_PASSWORD=your-secure-db-password
   
   # Redis
   REDIS_PASSWORD=your-secure-redis-password
   
   # Email
   RESEND_API_KEY=your-resend-api-key
   
   # Stripe (for billing)
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   
   # AI Models
   OPENAI_API_KEY=sk-...
   ANTHROPIC_API_KEY=sk-ant-...
   
   # Monitoring
   GRAFANA_ADMIN_PASSWORD=your-grafana-password
   ```

4. **Deploy the stack:**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

5. **Initialize the database:**
   ```bash
   docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head
   ```

6. **Create admin user:**
   ```bash
   docker-compose -f docker-compose.prod.yml exec backend python -m app.scripts.create_admin
   ```

### SSL/TLS Configuration

1. **Obtain SSL certificates:**
   ```bash
   # Using Let's Encrypt with Certbot
   sudo certbot certonly --standalone -d your-domain.com -d api.your-domain.com
   ```

2. **Configure Nginx:**
   ```bash
   # Copy certificates to nginx/ssl/
   sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/
   sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/
   ```

3. **Restart Nginx:**
   ```bash
   docker-compose -f docker-compose.prod.yml restart nginx
   ```

## Kubernetes (Enterprise)

### Prerequisites

- Kubernetes cluster 1.24+
- kubectl configured
- Helm 3.0+ (optional)
- cert-manager for SSL certificates
- Ingress controller (nginx recommended)

### Enterprise Deployment

1. **Prepare secrets:**
   ```bash
   # Create namespace
   kubectl create namespace tds-coder
   
   # Create secrets
   kubectl create secret generic tds-coder-secrets \
     --from-literal=SECRET_KEY=your-secret-key \
     --from-literal=DATABASE_URL=postgresql://... \
     --from-literal=REDIS_URL=redis://... \
     --from-literal=OPENAI_API_KEY=sk-... \
     --from-literal=ANTHROPIC_API_KEY=sk-ant-... \
     -n tds-coder
   
   kubectl create secret generic postgres-secret \
     --from-literal=password=your-db-password \
     -n tds-coder
   
   kubectl create secret generic redis-secret \
     --from-literal=password=your-redis-password \
     -n tds-coder
   ```

2. **Deploy the application:**
   ```bash
   kubectl apply -f k8s/tds-coder-deployment.yaml
   ```

3. **Configure ingress:**
   ```bash
   # Update the ingress with your domain
   sed -i 's/your-domain.com/yourdomain.com/g' k8s/tds-coder-deployment.yaml
   kubectl apply -f k8s/tds-coder-deployment.yaml
   ```

4. **Verify deployment:**
   ```bash
   kubectl get pods -n tds-coder
   kubectl get services -n tds-coder
   kubectl get ingress -n tds-coder
   ```

### High Availability Configuration

1. **Database clustering:**
   ```bash
   # Use PostgreSQL with streaming replication
   # Or managed database service (AWS RDS, Google Cloud SQL)
   ```

2. **Redis clustering:**
   ```bash
   # Deploy Redis Cluster or use managed service
   # (AWS ElastiCache, Google Memorystore)
   ```

3. **Application scaling:**
   ```bash
   # Scale backend
   kubectl scale deployment tds-coder-backend --replicas=5 -n tds-coder
   
   # Scale frontend
   kubectl scale deployment tds-coder-frontend --replicas=3 -n tds-coder
   ```

## Security Configuration

### Network Security

1. **Firewall rules:**
   ```bash
   # Allow only necessary ports
   # 80, 443 (HTTP/HTTPS)
   # 22 (SSH, restrict to admin IPs)
   # Database ports only from application servers
   ```

2. **VPC/Network isolation:**
   ```bash
   # Use private subnets for databases
   # Use security groups/network policies
   # Enable VPC flow logs
   ```

### Application Security

1. **Environment variables:**
   ```bash
   # Use secrets management
   # Rotate keys regularly
   # Use strong passwords
   ```

2. **HTTPS enforcement:**
   ```bash
   # Force HTTPS redirects
   # Use HSTS headers
   # Implement CSP headers
   ```

3. **Rate limiting:**
   ```bash
   # Configure rate limits per tier
   # Implement DDoS protection
   # Use WAF if available
   ```

### SSO Configuration

1. **SAML setup:**
   ```bash
   # Configure SAML provider in admin panel
   # Upload metadata
   # Test authentication flow
   ```

2. **OIDC setup:**
   ```bash
   # Configure OIDC provider
   # Set up client credentials
   # Configure attribute mapping
   ```

## Monitoring & Observability

### Metrics Collection

1. **Prometheus configuration:**
   ```yaml
   # monitoring/prometheus.yml
   global:
     scrape_interval: 15s
   
   scrape_configs:
     - job_name: 'tds-coder-backend'
       static_configs:
         - targets: ['backend:8000']
   ```

2. **Grafana dashboards:**
   ```bash
   # Import pre-built dashboards
   # Configure alerts
   # Set up notification channels
   ```

### Log Aggregation

1. **Centralized logging:**
   ```bash
   # Use ELK stack or Loki
   # Configure log retention
   # Set up log-based alerts
   ```

2. **Audit logging:**
   ```bash
   # Enable audit logs in application
   # Configure compliance reporting
   # Set up security alerts
   ```

### Health Checks

1. **Application health:**
   ```bash
   # /health endpoint for basic checks
   # /health/detailed for comprehensive checks
   # Database connectivity
   # Redis connectivity
   # External API availability
   ```

2. **Infrastructure monitoring:**
   ```bash
   # CPU, memory, disk usage
   # Network connectivity
   # SSL certificate expiration
   ```

## Backup & Recovery

### Database Backups

1. **Automated backups:**
   ```bash
   # Daily full backups
   # Hourly incremental backups
   # Point-in-time recovery
   ```

2. **Backup verification:**
   ```bash
   # Regular restore tests
   # Backup integrity checks
   # Cross-region replication
   ```

### Application Data

1. **Configuration backups:**
   ```bash
   # Environment variables
   # SSL certificates
   # Application configurations
   ```

2. **User data exports:**
   ```bash
   # GDPR compliance exports
   # Data portability
   # Audit trail preservation
   ```

## Scaling & Performance

### Horizontal Scaling

1. **Application tier:**
   ```bash
   # Load balancer configuration
   # Session management
   # Stateless design
   ```

2. **Database scaling:**
   ```bash
   # Read replicas
   # Connection pooling
   # Query optimization
   ```

### Performance Optimization

1. **Caching strategy:**
   ```bash
   # Redis for session storage
   # Application-level caching
   # CDN for static assets
   ```

2. **Database optimization:**
   ```bash
   # Index optimization
   # Query performance tuning
   # Connection pooling
   ```

## Troubleshooting

### Common Issues

1. **Database connection errors:**
   ```bash
   # Check database connectivity
   kubectl logs deployment/postgres -n tds-coder
   
   # Verify credentials
   kubectl get secret tds-coder-secrets -o yaml -n tds-coder
   ```

2. **API authentication failures:**
   ```bash
   # Check API key configuration
   # Verify JWT token settings
   # Review rate limiting logs
   ```

3. **Performance issues:**
   ```bash
   # Check resource utilization
   kubectl top pods -n tds-coder
   
   # Review application logs
   kubectl logs deployment/tds-coder-backend -n tds-coder
   ```

### Support Channels

- **Documentation:** https://docs.tdscoder.com
- **Community:** https://discord.gg/tdscoder
- **Enterprise Support:** <EMAIL>
- **Security Issues:** <EMAIL>

---

For additional support or custom deployment requirements, please contact our enterprise <NAME_EMAIL>.
