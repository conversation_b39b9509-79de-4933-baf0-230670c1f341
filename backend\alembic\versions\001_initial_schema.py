"""Initial schema with users, subscriptions, api_keys, and usage tables

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create custom enum types
    user_role_enum = postgresql.ENUM('user', 'admin', 'super_admin', name='user_role')
    user_role_enum.create(op.get_bind())
    
    subscription_status_enum = postgresql.ENUM(
        'active', 'inactive', 'canceled', 'past_due', 'unpaid', 'trialing', 'paused',
        name='subscription_status'
    )
    subscription_status_enum.create(op.get_bind())
    
    subscription_tier_enum = postgresql.ENUM(
        'free', 'solo', 'team', 'enterprise',
        name='subscription_tier'
    )
    subscription_tier_enum.create(op.get_bind())

    # Create users table
    op.create_table('users',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=True),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.Column('company', sa.String(length=255), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=False, server_default='UTC'),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('is_verified', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('role', user_role_enum, nullable=False, server_default='user'),
        sa.Column('email_verification_token', sa.String(length=255), nullable=True),
        sa.Column('email_verification_expires', sa.DateTime(timezone=True), nullable=True),
        sa.Column('password_reset_token', sa.String(length=255), nullable=True),
        sa.Column('password_reset_expires', sa.DateTime(timezone=True), nullable=True),
        sa.Column('totp_secret', sa.String(length=32), nullable=True),
        sa.Column('is_2fa_enabled', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('backup_codes', sa.Text(), nullable=True),
        sa.Column('avatar_url', sa.String(length=500), nullable=True),
        sa.Column('bio', sa.Text(), nullable=True),
        sa.Column('website', sa.String(length=500), nullable=True),
        sa.Column('location', sa.String(length=255), nullable=True),
        sa.Column('theme', sa.String(length=20), nullable=False, server_default='system'),
        sa.Column('language', sa.String(length=10), nullable=False, server_default='en'),
        sa.Column('email_notifications', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('marketing_emails', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_activity', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for users table
    op.create_index('ix_users_email', 'users', ['email'], unique=True)
    op.create_index('ix_users_username', 'users', ['username'], unique=True)
    op.create_index('ix_users_email_verification_token', 'users', ['email_verification_token'])
    op.create_index('ix_users_password_reset_token', 'users', ['password_reset_token'])
    op.create_index('ix_users_created_at', 'users', ['created_at'])
    op.create_index('ix_users_updated_at', 'users', ['updated_at'])
    op.create_index('ix_users_is_deleted', 'users', ['is_deleted'])

    # Create subscriptions table
    op.create_table('subscriptions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tier', subscription_tier_enum, nullable=False, server_default='free'),
        sa.Column('status', subscription_status_enum, nullable=False, server_default='active'),
        sa.Column('stripe_customer_id', sa.String(length=255), nullable=True),
        sa.Column('stripe_subscription_id', sa.String(length=255), nullable=True),
        sa.Column('stripe_price_id', sa.String(length=255), nullable=True),
        sa.Column('stripe_payment_method_id', sa.String(length=255), nullable=True),
        sa.Column('current_period_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('current_period_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('trial_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('trial_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('canceled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('ended_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('amount', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('currency', sa.String(length=3), nullable=False, server_default='USD'),
        sa.Column('daily_request_limit', sa.Integer(), nullable=False, server_default='100'),
        sa.Column('burst_limit', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('seats_limit', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('features', sa.Text(), nullable=True),
        sa.Column('billing_name', sa.String(length=255), nullable=True),
        sa.Column('billing_email', sa.String(length=255), nullable=True),
        sa.Column('billing_address_line1', sa.String(length=255), nullable=True),
        sa.Column('billing_address_line2', sa.String(length=255), nullable=True),
        sa.Column('billing_city', sa.String(length=100), nullable=True),
        sa.Column('billing_state', sa.String(length=100), nullable=True),
        sa.Column('billing_postal_code', sa.String(length=20), nullable=True),
        sa.Column('billing_country', sa.String(length=2), nullable=True),
        sa.Column('tax_id', sa.String(length=50), nullable=True),
        sa.Column('tax_rate', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('proration_amount', sa.Numeric(precision=10, scale=2), nullable=False, server_default='0'),
        sa.Column('credit_balance', sa.Numeric(precision=10, scale=2), nullable=False, server_default='0'),
        sa.Column('failed_payment_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('last_failed_payment', sa.DateTime(timezone=True), nullable=True),
        sa.Column('next_retry_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('pause_collection', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('pause_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('pause_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE')
    )
    
    # Create indexes for subscriptions table
    op.create_index('ix_subscriptions_user_id', 'subscriptions', ['user_id'])
    op.create_index('ix_subscriptions_stripe_customer_id', 'subscriptions', ['stripe_customer_id'])
    op.create_index('ix_subscriptions_stripe_subscription_id', 'subscriptions', ['stripe_subscription_id'])
    op.create_index('ix_subscriptions_created_at', 'subscriptions', ['created_at'])
    op.create_index('ix_subscriptions_updated_at', 'subscriptions', ['updated_at'])
    op.create_index('ix_subscriptions_is_deleted', 'subscriptions', ['is_deleted'])

    # Create api_keys table
    op.create_table('api_keys',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('key_hash', sa.String(length=64), nullable=False),
        sa.Column('key_prefix', sa.String(length=8), nullable=False),
        sa.Column('key_suffix', sa.String(length=4), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('is_revoked', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('revoked_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('revoked_reason', sa.String(length=255), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('never_expires', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('last_used', sa.DateTime(timezone=True), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('scopes', sa.Text(), nullable=True),
        sa.Column('allowed_ips', sa.Text(), nullable=True),
        sa.Column('custom_daily_limit', sa.Integer(), nullable=True),
        sa.Column('custom_burst_limit', sa.Integer(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_by_ip', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE')
    )

    # Create indexes for api_keys table
    op.create_index('ix_api_keys_user_id', 'api_keys', ['user_id'])
    op.create_index('ix_api_keys_key_hash', 'api_keys', ['key_hash'], unique=True)
    op.create_index('ix_api_keys_key_prefix', 'api_keys', ['key_prefix'])
    op.create_index('ix_api_keys_created_at', 'api_keys', ['created_at'])
    op.create_index('ix_api_keys_updated_at', 'api_keys', ['updated_at'])
    op.create_index('ix_api_keys_is_deleted', 'api_keys', ['is_deleted'])

    # Create usage table
    op.create_table('usage',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('api_key_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('endpoint', sa.String(length=255), nullable=False),
        sa.Column('method', sa.String(length=10), nullable=False),
        sa.Column('request_id', sa.String(length=255), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('response_time_ms', sa.Integer(), nullable=True),
        sa.Column('request_size_bytes', sa.Integer(), nullable=True),
        sa.Column('response_size_bytes', sa.Integer(), nullable=True),
        sa.Column('status_code', sa.Integer(), nullable=False),
        sa.Column('client_ip', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('referer', sa.String(length=500), nullable=True),
        sa.Column('model_used', sa.String(length=100), nullable=True),
        sa.Column('tokens_input', sa.Integer(), nullable=True),
        sa.Column('tokens_output', sa.Integer(), nullable=True),
        sa.Column('completion_type', sa.String(length=50), nullable=True),
        sa.Column('cost_cents', sa.Integer(), nullable=True),
        sa.Column('credits_used', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('error_code', sa.String(length=50), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('rate_limit_hit', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('quota_exceeded', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('country_code', sa.String(length=2), nullable=True),
        sa.Column('region', sa.String(length=100), nullable=True),
        sa.Column('city', sa.String(length=100), nullable=True),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['api_key_id'], ['api_keys.id'], ondelete='SET NULL')
    )

    # Create indexes for usage table
    op.create_index('ix_usage_user_id', 'usage', ['user_id'])
    op.create_index('ix_usage_api_key_id', 'usage', ['api_key_id'])
    op.create_index('ix_usage_endpoint', 'usage', ['endpoint'])
    op.create_index('ix_usage_timestamp', 'usage', ['timestamp'])
    op.create_index('ix_usage_status_code', 'usage', ['status_code'])
    op.create_index('ix_usage_error_code', 'usage', ['error_code'])
    op.create_index('ix_usage_request_id', 'usage', ['request_id'])
    op.create_index('ix_usage_created_at', 'usage', ['created_at'])
    op.create_index('ix_usage_updated_at', 'usage', ['updated_at'])
    op.create_index('ix_usage_is_deleted', 'usage', ['is_deleted'])

    # Create composite indexes for performance
    op.create_index('ix_usage_user_timestamp', 'usage', ['user_id', 'timestamp'])
    op.create_index('ix_usage_api_key_timestamp', 'usage', ['api_key_id', 'timestamp'])
    op.create_index('ix_usage_endpoint_timestamp', 'usage', ['endpoint', 'timestamp'])
    op.create_index('ix_usage_status_timestamp', 'usage', ['status_code', 'timestamp'])
    op.create_index('ix_usage_daily', 'usage', ['user_id', 'timestamp'])

    # Create database functions
    op.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)

    # Create triggers for updated_at
    op.execute("""
        CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    """)

    op.execute("""
        CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    """)

    op.execute("""
        CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    """)

    op.execute("""
        CREATE TRIGGER update_usage_updated_at BEFORE UPDATE ON usage
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    """)

    # Create quota checking function
    op.execute("""
        CREATE OR REPLACE FUNCTION check_daily_quota(user_uuid UUID, request_date DATE DEFAULT CURRENT_DATE)
        RETURNS BOOLEAN AS $$
        DECLARE
            daily_limit INTEGER;
            current_usage INTEGER;
            user_tier subscription_tier;
        BEGIN
            -- Get user's subscription tier
            SELECT s.tier INTO user_tier
            FROM subscriptions s
            WHERE s.user_id = user_uuid AND s.status = 'active' AND s.is_deleted = false;

            -- If no active subscription, default to free tier
            IF user_tier IS NULL THEN
                user_tier := 'free';
            END IF;

            -- Get daily limit for the tier
            CASE user_tier
                WHEN 'free' THEN daily_limit := 100;
                WHEN 'solo' THEN daily_limit := 10000;
                WHEN 'team' THEN daily_limit := 50000;
                WHEN 'enterprise' THEN daily_limit := -1; -- Unlimited
                ELSE daily_limit := 100;
            END CASE;

            -- If unlimited (-1), return false (not exceeded)
            IF daily_limit = -1 THEN
                RETURN FALSE;
            END IF;

            -- Count current usage for the day
            SELECT COUNT(*) INTO current_usage
            FROM usage
            WHERE user_id = user_uuid
            AND DATE(timestamp) = request_date
            AND status_code < 400
            AND is_deleted = false;

            -- Return true if quota exceeded
            RETURN current_usage >= daily_limit;
        END;
        $$ language 'plpgsql';
    """)


def downgrade() -> None:
    # Drop functions
    op.execute("DROP FUNCTION IF EXISTS check_daily_quota(UUID, DATE);")
    op.execute("DROP FUNCTION IF EXISTS update_updated_at_column();")

    # Drop tables in reverse order (due to foreign key constraints)
    op.drop_table('usage')
    op.drop_table('api_keys')
    op.drop_table('subscriptions')
    op.drop_table('users')

    # Drop enum types
    op.execute("DROP TYPE IF EXISTS subscription_tier;")
    op.execute("DROP TYPE IF EXISTS subscription_status;")
    op.execute("DROP TYPE IF EXISTS user_role;")
