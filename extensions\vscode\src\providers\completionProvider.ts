import * as vscode from 'vscode';
import axios from 'axios';
import { AuthenticationManager } from '../auth/authManager';
import { ConfigurationManager } from '../config/configManager';
import { TelemetryManager } from '../telemetry/telemetryManager';
import { CacheManager } from '../cache/cacheManager';

export class TDSCoderProvider implements vscode.InlineCompletionItemProvider {
    private debounceTimer: NodeJS.Timeout | undefined;
    private lastCompletionTime = 0;
    private readonly minCompletionInterval = 100; // Minimum time between completions

    constructor(
        private authManager: AuthenticationManager,
        private configManager: ConfigurationManager,
        private telemetryManager: TelemetryManager,
        private cacheManager: CacheManager
    ) {}

    async provideInlineCompletionItems(
        document: vscode.TextDocument,
        position: vscode.Position,
        context: vscode.InlineCompletionContext,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | undefined> {
        
        // Check if inline completion is enabled
        if (!this.configManager.getEnableInlineCompletion()) {
            return undefined;
        }

        // Check authentication
        if (!this.authManager.isAuthenticated()) {
            return undefined;
        }

        // Rate limiting
        const now = Date.now();
        if (now - this.lastCompletionTime < this.minCompletionInterval) {
            return undefined;
        }
        this.lastCompletionTime = now;

        // Clear existing debounce timer
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // Return a promise that resolves after debounce delay
        return new Promise((resolve) => {
            this.debounceTimer = setTimeout(async () => {
                try {
                    const completions = await this.getCompletions(document, position, token);
                    resolve(completions);
                } catch (error) {
                    console.error('TDS Coder completion error:', error);
                    resolve(undefined);
                }
            }, this.configManager.getCompletionDelay());
        });
    }

    private async getCompletions(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken
    ): Promise<vscode.InlineCompletionItem[]> {
        
        // Check if request was cancelled
        if (token.isCancellationRequested) {
            return [];
        }

        // Get context around cursor
        const context = this.getCompletionContext(document, position);
        
        // Check cache first
        if (this.configManager.getEnableOfflineMode()) {
            const cachedCompletion = await this.cacheManager.getCachedCompletion(context.prompt);
            if (cachedCompletion) {
                this.telemetryManager.trackEvent('completion_cache_hit', {
                    language: document.languageId,
                    promptLength: context.prompt.length
                });
                
                return [new vscode.InlineCompletionItem(
                    cachedCompletion.text,
                    new vscode.Range(position, position)
                )];
            }
        }

        try {
            // Make API request
            const completion = await this.requestCompletion(context, document.languageId, token);
            
            if (!completion || token.isCancellationRequested) {
                return [];
            }

            // Cache the completion
            if (this.configManager.getEnableOfflineMode()) {
                await this.cacheManager.cacheCompletion(context.prompt, completion);
            }

            // Track telemetry
            this.telemetryManager.trackEvent('completion_generated', {
                language: document.languageId,
                model: completion.model,
                promptLength: context.prompt.length,
                completionLength: completion.text.length,
                responseTime: completion.responseTime
            });

            // Create completion item
            const completionItem = new vscode.InlineCompletionItem(
                completion.text,
                new vscode.Range(position, position)
            );

            // Add command to accept completion
            completionItem.command = {
                command: 'tdsCoder.acceptCompletion',
                title: 'Accept TDS Coder completion',
                arguments: [completion]
            };

            return [completionItem];

        } catch (error) {
            console.error('TDS Coder API error:', error);
            
            // Track error
            this.telemetryManager.trackEvent('completion_error', {
                error: error instanceof Error ? error.message : 'Unknown error',
                language: document.languageId
            });

            return [];
        }
    }

    private getCompletionContext(document: vscode.TextDocument, position: vscode.Position) {
        const linePrefix = document.lineAt(position).text.substring(0, position.character);
        const lineSuffix = document.lineAt(position).text.substring(position.character);
        
        // Get surrounding context (up to 50 lines before and 10 lines after)
        const startLine = Math.max(0, position.line - 50);
        const endLine = Math.min(document.lineCount - 1, position.line + 10);
        
        const beforeContext = [];
        for (let i = startLine; i < position.line; i++) {
            beforeContext.push(document.lineAt(i).text);
        }
        
        const afterContext = [];
        for (let i = position.line + 1; i <= endLine; i++) {
            afterContext.push(document.lineAt(i).text);
        }

        // Create prompt
        const prompt = [
            ...beforeContext,
            linePrefix
        ].join('\n');

        return {
            prompt,
            linePrefix,
            lineSuffix,
            beforeContext: beforeContext.join('\n'),
            afterContext: afterContext.join('\n'),
            filePath: document.fileName,
            cursorPosition: position
        };
    }

    private async requestCompletion(
        context: any,
        language: string,
        token: vscode.CancellationToken
    ): Promise<any> {
        const apiKey = this.authManager.getApiKey();
        const apiUrl = this.configManager.getApiUrl();
        
        if (!apiKey) {
            throw new Error('No API key configured');
        }

        const requestData = {
            prompt: context.prompt,
            language: language,
            model: this.configManager.getModel() === 'auto' ? undefined : this.configManager.getModel(),
            max_tokens: this.configManager.getMaxTokens(),
            temperature: this.configManager.getTemperature(),
            completion_type: 'inline',
            context: {
                file_path: context.filePath,
                surrounding_code: context.beforeContext + '\n' + context.afterContext,
                cursor_position: {
                    line: context.cursorPosition.line,
                    character: context.cursorPosition.character
                }
            }
        };

        const startTime = Date.now();

        try {
            const response = await axios.post(
                `${apiUrl}/api/v1/v1/completions`,
                requestData,
                {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'User-Agent': `TDS-Coder-VSCode/${vscode.extensions.getExtension('tdscoder.tds-coder')?.packageJSON.version}`
                    },
                    timeout: 10000, // 10 second timeout
                    cancelToken: new axios.CancelToken((cancel) => {
                        token.onCancellationRequested(() => {
                            cancel('Request cancelled by user');
                        });
                    })
                }
            );

            const responseTime = Date.now() - startTime;

            if (response.data && response.data.choices && response.data.choices.length > 0) {
                return {
                    text: response.data.choices[0].text,
                    model: response.data.model,
                    responseTime,
                    usage: response.data.usage,
                    metadata: response.data.metadata
                };
            }

            return null;

        } catch (error) {
            if (axios.isCancel(error)) {
                console.log('Request cancelled');
                return null;
            }
            
            // Handle specific error cases
            if (axios.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    vscode.window.showErrorMessage('TDS Coder: Authentication failed. Please check your API key.');
                    this.authManager.clearAuthentication();
                } else if (error.response?.status === 429) {
                    vscode.window.showWarningMessage('TDS Coder: Rate limit exceeded. Please try again later.');
                } else if (error.response?.status === 402) {
                    vscode.window.showWarningMessage('TDS Coder: Quota exceeded. Please upgrade your plan.');
                }
            }
            
            throw error;
        }
    }
}
