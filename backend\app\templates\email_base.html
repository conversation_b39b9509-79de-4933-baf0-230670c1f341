<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            display: inline-block;
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border-radius: 8px;
            margin-bottom: 16px;
            position: relative;
        }
        .logo::after {
            content: '</>';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .brand {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin: 0;
        }
        .tagline {
            color: #6b7280;
            margin: 4px 0 0 0;
            font-size: 14px;
        }
        h1 {
            color: #1f2937;
            font-size: 28px;
            margin: 0 0 16px 0;
            font-weight: 600;
        }
        p {
            margin: 16px 0;
            color: #4b5563;
        }
        .button {
            display: inline-block;
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 0;
            transition: background-color 0.2s;
        }
        .button:hover {
            background-color: #2563eb;
        }
        .code {
            background-color: #f3f4f6;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 2px 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #374151;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        .security-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-notice p {
            margin: 0;
            color: #92400e;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo"></div>
            <h2 class="brand">TDS Coder</h2>
            <p class="tagline">AI-Powered Code Completion</p>
        </div>
        
        <h1>{{ heading }}</h1>
        
        {{ content }}
        
        <div class="footer">
            <p>
                This email was sent by TDS Coder. If you have any questions, 
                <a href="mailto:<EMAIL>">contact our support team</a>.
            </p>
            <p>
                <a href="{{ unsubscribe_url }}">Unsubscribe</a> | 
                <a href="https://tdscoder.com/privacy">Privacy Policy</a> | 
                <a href="https://tdscoder.com/terms">Terms of Service</a>
            </p>
        </div>
    </div>
</body>
</html>
