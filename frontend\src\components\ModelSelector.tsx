'use client'

import { useState, useEffect } from 'react'
import { 
  ChevronDownIcon, 
  CheckIcon,
  CpuChipIcon,
  ClockIcon,
  CurrencyDollarIcon,
  SparklesIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@/lib/api'

interface ModelInfo {
  id: string
  name: string
  provider: string
  description: string
  contextWindow: number
  maxTokens: number
  costPer1kTokens: number
  bestFor: string[]
  tier: 'free' | 'solo' | 'team' | 'enterprise'
  isRecommended?: boolean
}

const modelDatabase: Record<string, ModelInfo> = {
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    description: 'Fast and efficient model for general coding tasks',
    contextWindow: 16385,
    maxTokens: 4096,
    costPer1kTokens: 0.002,
    bestFor: ['general', 'chat', 'quick_completions'],
    tier: 'free',
    isRecommended: true
  },
  'gpt-4o': {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    description: 'Balanced performance and cost efficiency',
    contextWindow: 128000,
    maxTokens: 4096,
    costPer1kTokens: 0.01,
    bestFor: ['balanced_performance', 'cost_efficiency', 'general_coding'],
    tier: 'solo'
  },
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    description: 'Advanced model with large context window',
    contextWindow: 128000,
    maxTokens: 4096,
    costPer1kTokens: 0.02,
    bestFor: ['large_context', 'document_analysis', 'refactoring'],
    tier: 'team'
  },
  'claude-3-haiku': {
    id: 'claude-3-haiku',
    name: 'Claude 3 Haiku',
    provider: 'Anthropic',
    description: 'Fast and cost-effective for simple tasks',
    contextWindow: 200000,
    maxTokens: 4096,
    costPer1kTokens: 0.00125,
    bestFor: ['fast_completions', 'simple_tasks', 'cost_efficiency'],
    tier: 'free'
  },
  'claude-3.5-sonnet': {
    id: 'claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for coding and reasoning tasks',
    contextWindow: 200000,
    maxTokens: 8192,
    costPer1kTokens: 0.015,
    bestFor: ['coding', 'reasoning', 'analysis'],
    tier: 'team',
    isRecommended: true
  },
  'codestral': {
    id: 'codestral',
    name: 'Codestral',
    provider: 'Mistral AI',
    description: 'Specialized model for code completion',
    contextWindow: 32768,
    maxTokens: 32768,
    costPer1kTokens: 0.002,
    bestFor: ['code_completion', 'code_generation', 'programming_tasks'],
    tier: 'solo',
    isRecommended: true
  },
  'deepseek-coder-v3': {
    id: 'deepseek-coder-v3',
    name: 'DeepSeek Coder V3',
    provider: 'DeepSeek',
    description: 'Latest coding-focused model with excellent performance',
    contextWindow: 64000,
    maxTokens: 8192,
    costPer1kTokens: 0.00021,
    bestFor: ['code_completion', 'debugging', 'code_optimization'],
    tier: 'solo',
    isRecommended: true
  },
  'gemini-1.5-flash': {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    provider: 'Google',
    description: 'Ultra-fast model with massive context window',
    contextWindow: 1000000,
    maxTokens: 8192,
    costPer1kTokens: 0.0004,
    bestFor: ['fast_completions', 'cost_efficiency', 'high_throughput'],
    tier: 'free'
  },
  'gemini-1.5-pro': {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    provider: 'Google',
    description: 'Advanced model with 2M token context window',
    contextWindow: 2000000,
    maxTokens: 8192,
    costPer1kTokens: 0.00625,
    bestFor: ['large_context', 'document_analysis', 'complex_reasoning'],
    tier: 'team'
  }
}

interface ModelSelectorProps {
  selectedModel: string
  onModelChange: (modelId: string) => void
  availableModels: string[]
  userTier: 'free' | 'solo' | 'team' | 'enterprise'
  className?: string
}

export default function ModelSelector({
  selectedModel,
  onModelChange,
  availableModels,
  userTier,
  className = ''
}: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [showDetails, setShowDetails] = useState<string | null>(null)

  const filteredModels = availableModels
    .map(id => modelDatabase[id])
    .filter(Boolean)
    .sort((a, b) => {
      // Sort by recommendation, then by tier, then by cost
      if (a.isRecommended && !b.isRecommended) return -1
      if (!a.isRecommended && b.isRecommended) return 1
      
      const tierOrder = { free: 0, solo: 1, team: 2, enterprise: 3 }
      const tierDiff = tierOrder[a.tier] - tierOrder[b.tier]
      if (tierDiff !== 0) return tierDiff
      
      return a.costPer1kTokens - b.costPer1kTokens
    })

  const selectedModelInfo = modelDatabase[selectedModel]

  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'free': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'solo': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'team': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'enterprise': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
  }

  const formatContextWindow = (tokens: number) => {
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(0)}K`
    return tokens.toString()
  }

  return (
    <div className={`relative ${className}`}>
      {/* Selected Model Display */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
      >
        <div className="flex items-center space-x-3">
          <CpuChipIcon className="h-5 w-5 text-gray-400" />
          <div className="text-left">
            <div className="font-medium text-gray-900 dark:text-white">
              {selectedModelInfo?.name || selectedModel}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {selectedModelInfo?.provider}
            </div>
          </div>
          {selectedModelInfo?.isRecommended && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              <SparklesIcon className="h-3 w-3 mr-1" />
              Recommended
            </span>
          )}
        </div>
        <ChevronDownIcon className={`h-5 w-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-96 overflow-y-auto">
          {filteredModels.map((model) => (
            <div key={model.id} className="relative">
              <button
                onClick={() => {
                  onModelChange(model.id)
                  setIsOpen(false)
                }}
                className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {model.name}
                      </span>
                      {model.isRecommended && (
                        <SparklesIcon className="h-4 w-4 text-yellow-500" />
                      )}
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierBadgeColor(model.tier)}`}>
                        {model.tier}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {model.provider} • {model.description}
                    </div>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center">
                        <ClockIcon className="h-3 w-3 mr-1" />
                        {formatContextWindow(model.contextWindow)} context
                      </div>
                      <div className="flex items-center">
                        <CurrencyDollarIcon className="h-3 w-3 mr-1" />
                        ${model.costPer1kTokens.toFixed(4)}/1K tokens
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowDetails(showDetails === model.id ? null : model.id)
                      }}
                      className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <InformationCircleIcon className="h-4 w-4" />
                    </button>
                    {selectedModel === model.id && (
                      <CheckIcon className="h-5 w-5 text-indigo-600" />
                    )}
                  </div>
                </div>
                
                {/* Model Details */}
                {showDetails === model.id && (
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div className="text-sm">
                      <div className="font-medium text-gray-900 dark:text-white mb-2">
                        Best for:
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {model.bestFor.map((use) => (
                          <span
                            key={use}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200"
                          >
                            {use.replace('_', ' ')}
                          </span>
                        ))}
                      </div>
                      <div className="mt-3 grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="font-medium">Max Tokens:</span> {model.maxTokens.toLocaleString()}
                        </div>
                        <div>
                          <span className="font-medium">Context Window:</span> {formatContextWindow(model.contextWindow)}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </button>
            </div>
          ))}
          
          {filteredModels.length === 0 && (
            <div className="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
              No models available for your subscription tier.
              <br />
              <a href="/dashboard/billing" className="text-indigo-600 hover:text-indigo-500">
                Upgrade your plan
              </a> to access more models.
            </div>
          )}
        </div>
      )}
    </div>
  )
}
