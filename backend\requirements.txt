# FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.13.0
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Payment Processing
stripe==7.8.0

# Email
sendgrid==6.11.0

# Environment & Configuration
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Validation & Serialization
email-validator==2.1.0
phonenumbers==8.13.26

# Rate Limiting
slowapi==0.1.9

# Monitoring & Logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
pytest-mock==3.12.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# CORS
fastapi-cors==0.0.6

# Background Tasks
celery==5.3.4
redis==5.0.1

# File Upload
python-multipart==0.0.6
pillow==10.1.0

# Date/Time
python-dateutil==2.8.2
pytz==2023.3

# UUID
uuid==1.30
