'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth'
import { 
  UserIcon, 
  ShieldCheckIcon, 
  BellIcon, 
  GlobeAltIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import toast from 'react-hot-toast'
import { apiHelpers } from '@/lib/api'

const profileSchema = z.object({
  full_name: z.string().min(2, 'Full name must be at least 2 characters'),
  company: z.string().optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  location: z.string().optional(),
  timezone: z.string(),
  language: z.string(),
  theme: z.enum(['light', 'dark', 'system']),
  email_notifications: z.boolean(),
  marketing_emails: z.boolean(),
})

const passwordSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      'Password must contain uppercase, lowercase, number, and special character'),
  confirm_password: z.string(),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"],
})

type ProfileFormData = z.infer<typeof profileSchema>
type PasswordFormData = z.infer<typeof passwordSchema>

export default function SettingsPage() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('profile')
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      full_name: user?.full_name || '',
      company: user?.company || '',
      bio: user?.bio || '',
      website: user?.website || '',
      location: user?.location || '',
      timezone: user?.timezone || 'UTC',
      language: user?.language || 'en',
      theme: user?.theme || 'system',
      email_notifications: user?.email_notifications ?? true,
      marketing_emails: user?.marketing_emails ?? false,
    }
  })

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
  })

  const onProfileSubmit = async (data: ProfileFormData) => {
    setIsLoading(true)
    try {
      await apiHelpers.users.update(data)
      toast.success('Profile updated successfully!')
    } catch (error) {
      toast.error('Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  const onPasswordSubmit = async (data: PasswordFormData) => {
    setIsLoading(true)
    try {
      await apiHelpers.users.changePassword(data)
      toast.success('Password changed successfully!')
      passwordForm.reset()
    } catch (error) {
      toast.error('Failed to change password')
    } finally {
      setIsLoading(false)
    }
  }

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'preferences', name: 'Preferences', icon: GlobeAltIcon },
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    activeTab === tab.id
                      ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800'
                  }`}
                >
                  <tab.icon className="mr-3 h-5 w-5" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              {activeTab === 'profile' && (
                <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="p-6 space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Profile Information
                    </h3>
                    
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label className="form-label">Full Name *</label>
                        <input
                          {...profileForm.register('full_name')}
                          className="form-input"
                          placeholder="Enter your full name"
                        />
                        {profileForm.formState.errors.full_name && (
                          <p className="form-error">{profileForm.formState.errors.full_name.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Company</label>
                        <input
                          {...profileForm.register('company')}
                          className="form-input"
                          placeholder="Enter your company"
                        />
                      </div>

                      <div className="sm:col-span-2">
                        <label className="form-label">Bio</label>
                        <textarea
                          {...profileForm.register('bio')}
                          rows={3}
                          className="form-input"
                          placeholder="Tell us about yourself"
                        />
                        {profileForm.formState.errors.bio && (
                          <p className="form-error">{profileForm.formState.errors.bio.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Website</label>
                        <input
                          {...profileForm.register('website')}
                          type="url"
                          className="form-input"
                          placeholder="https://example.com"
                        />
                        {profileForm.formState.errors.website && (
                          <p className="form-error">{profileForm.formState.errors.website.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Location</label>
                        <input
                          {...profileForm.register('location')}
                          className="form-input"
                          placeholder="City, Country"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="btn-primary px-4 py-2 rounded-md disabled:opacity-50"
                    >
                      {isLoading ? 'Saving...' : 'Save Changes'}
                    </button>
                  </div>
                </form>
              )}

              {activeTab === 'security' && (
                <div className="p-6 space-y-8">
                  {/* Change Password */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Change Password
                    </h3>
                    
                    <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                      <div>
                        <label className="form-label">Current Password</label>
                        <div className="relative">
                          <input
                            {...passwordForm.register('current_password')}
                            type={showCurrentPassword ? 'text' : 'password'}
                            className="form-input pr-10"
                            placeholder="Enter current password"
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          >
                            {showCurrentPassword ? (
                              <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                            ) : (
                              <EyeIcon className="h-5 w-5 text-gray-400" />
                            )}
                          </button>
                        </div>
                        {passwordForm.formState.errors.current_password && (
                          <p className="form-error">{passwordForm.formState.errors.current_password.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">New Password</label>
                        <div className="relative">
                          <input
                            {...passwordForm.register('new_password')}
                            type={showNewPassword ? 'text' : 'password'}
                            className="form-input pr-10"
                            placeholder="Enter new password"
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? (
                              <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                            ) : (
                              <EyeIcon className="h-5 w-5 text-gray-400" />
                            )}
                          </button>
                        </div>
                        {passwordForm.formState.errors.new_password && (
                          <p className="form-error">{passwordForm.formState.errors.new_password.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Confirm New Password</label>
                        <div className="relative">
                          <input
                            {...passwordForm.register('confirm_password')}
                            type={showConfirmPassword ? 'text' : 'password'}
                            className="form-input pr-10"
                            placeholder="Confirm new password"
                          />
                          <button
                            type="button"
                            className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? (
                              <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                            ) : (
                              <EyeIcon className="h-5 w-5 text-gray-400" />
                            )}
                          </button>
                        </div>
                        {passwordForm.formState.errors.confirm_password && (
                          <p className="form-error">{passwordForm.formState.errors.confirm_password.message}</p>
                        )}
                      </div>

                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isLoading}
                          className="btn-primary px-4 py-2 rounded-md disabled:opacity-50"
                        >
                          {isLoading ? 'Changing...' : 'Change Password'}
                        </button>
                      </div>
                    </form>
                  </div>

                  {/* Two-Factor Authentication */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          Two-Factor Authentication
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Add an extra layer of security to your account
                        </p>
                      </div>
                      <div className="flex items-center">
                        {user?.is_2fa_enabled ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <ShieldCheckIcon className="w-4 h-4 mr-1" />
                            Enabled
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            Disabled
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      {user?.is_2fa_enabled ? (
                        <button className="btn-secondary px-4 py-2 rounded-md">
                          Disable 2FA
                        </button>
                      ) : (
                        <button className="btn-primary px-4 py-2 rounded-md">
                          Enable 2FA
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Add other tabs content here */}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
