"""
Subscription model for billing and plan management.
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Numeric, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
import enum
from datetime import datetime

from app.db.base import BaseModel


class SubscriptionStatus(str, enum.Enum):
    """Subscription status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    CANCELED = "canceled"
    PAST_DUE = "past_due"
    UNPAID = "unpaid"
    TRIALING = "trialing"
    PAUSED = "paused"


class SubscriptionTier(str, enum.Enum):
    """Subscription tier enumeration."""
    FREE = "free"
    SOLO = "solo"
    TEAM = "team"
    ENTERPRISE = "enterprise"


class Subscription(BaseModel):
    """Subscription model for billing and plan management."""
    
    __tablename__ = "subscriptions"
    
    # Primary Key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # User Relationship
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Subscription Details
    tier = Column(Enum(SubscriptionTier), default=SubscriptionTier.FREE, nullable=False)
    status = Column(Enum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE, nullable=False)
    
    # Stripe Integration
    stripe_customer_id = Column(String(255), nullable=True, index=True)
    stripe_subscription_id = Column(String(255), nullable=True, index=True)
    stripe_price_id = Column(String(255), nullable=True)
    stripe_payment_method_id = Column(String(255), nullable=True)
    
    # Billing Information
    current_period_start = Column(DateTime(timezone=True), nullable=True)
    current_period_end = Column(DateTime(timezone=True), nullable=True)
    trial_start = Column(DateTime(timezone=True), nullable=True)
    trial_end = Column(DateTime(timezone=True), nullable=True)
    canceled_at = Column(DateTime(timezone=True), nullable=True)
    ended_at = Column(DateTime(timezone=True), nullable=True)
    
    # Pricing
    amount = Column(Numeric(10, 2), nullable=True)  # Monthly amount in dollars
    currency = Column(String(3), default="USD", nullable=False)
    
    # Usage Limits
    daily_request_limit = Column(Integer, nullable=False, default=100)
    burst_limit = Column(Integer, nullable=False, default=1)
    seats_limit = Column(Integer, nullable=False, default=1)
    
    # Features (JSON array of feature names)
    features = Column(Text, nullable=True)  # JSON array
    
    # Billing Address
    billing_name = Column(String(255), nullable=True)
    billing_email = Column(String(255), nullable=True)
    billing_address_line1 = Column(String(255), nullable=True)
    billing_address_line2 = Column(String(255), nullable=True)
    billing_city = Column(String(100), nullable=True)
    billing_state = Column(String(100), nullable=True)
    billing_postal_code = Column(String(20), nullable=True)
    billing_country = Column(String(2), nullable=True)  # ISO country code
    
    # Tax Information
    tax_id = Column(String(50), nullable=True)  # VAT/Tax ID
    tax_rate = Column(Numeric(5, 4), nullable=True)  # Tax rate as decimal
    
    # Proration and Credits
    proration_amount = Column(Numeric(10, 2), default=0, nullable=False)
    credit_balance = Column(Numeric(10, 2), default=0, nullable=False)
    
    # Dunning Management
    failed_payment_count = Column(Integer, default=0, nullable=False)
    last_failed_payment = Column(DateTime(timezone=True), nullable=True)
    next_retry_date = Column(DateTime(timezone=True), nullable=True)
    
    # Pause/Resume
    pause_collection = Column(Boolean, default=False, nullable=False)
    pause_start = Column(DateTime(timezone=True), nullable=True)
    pause_end = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="subscription")
    
    def __repr__(self):
        return f"<Subscription(id={self.id}, user_id={self.user_id}, tier={self.tier})>"
    
    @property
    def is_active(self) -> bool:
        """Check if subscription is currently active."""
        return self.status == SubscriptionStatus.ACTIVE
    
    @property
    def is_trial(self) -> bool:
        """Check if subscription is in trial period."""
        if not self.trial_end:
            return False
        return datetime.utcnow() <= self.trial_end
    
    @property
    def is_expired(self) -> bool:
        """Check if subscription has expired."""
        if not self.current_period_end:
            return False
        return datetime.utcnow() > self.current_period_end
    
    @property
    def days_until_renewal(self) -> int:
        """Get number of days until subscription renewal."""
        if not self.current_period_end:
            return 0
        delta = self.current_period_end - datetime.utcnow()
        return max(0, delta.days)
    
    def can_access_feature(self, feature: str) -> bool:
        """Check if subscription tier includes specific feature."""
        if not self.features:
            return False
        
        import json
        try:
            feature_list = json.loads(self.features)
            return feature in feature_list or "all" in feature_list
        except (json.JSONDecodeError, TypeError):
            return False
    
    def get_usage_limits(self) -> dict:
        """Get usage limits for this subscription."""
        return {
            "daily_requests": self.daily_request_limit,
            "burst_limit": self.burst_limit,
            "seats": self.seats_limit,
        }
