apiVersion: v1
kind: Namespace
metadata:
  name: tds-coder
  labels:
    name: tds-coder
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tds-coder-config
  namespace: tds-coder
data:
  ENVIRONMENT: "production"
  DEBUG: "false"
  ACCESS_TOKEN_EXPIRE_MINUTES: "30"
  REFRESH_TOKEN_EXPIRE_DAYS: "7"
  DEFAULT_MODEL: "gpt-3.5-turbo"
  RATE_LIMIT_FREE_TIER: "100"
  RATE_LIMIT_SOLO_TIER: "10000"
  RATE_LIMIT_TEAM_TIER: "50000"
---
apiVersion: v1
kind: Secret
metadata:
  name: tds-coder-secrets
  namespace: tds-coder
type: Opaque
stringData:
  SECRET_KEY: "your-secret-key-here"
  DATABASE_URL: "********************************************/tds_coder"
  REDIS_URL: "redis://:password@redis:6379/0"
  RESEND_API_KEY: "your-resend-api-key"
  STRIPE_SECRET_KEY: "your-stripe-secret-key"
  STRIPE_WEBHOOK_SECRET: "your-stripe-webhook-secret"
  OPENAI_API_KEY: "your-openai-api-key"
  ANTHROPIC_API_KEY: "your-anthropic-api-key"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: tds-coder
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: "tds_coder"
        - name: POSTGRES_USER
          value: "tds_coder"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - tds_coder
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - tds_coder
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: tds-coder
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: tds-coder
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        ports:
        - containerPort: 6379
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: tds-coder
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tds-coder-backend
  namespace: tds-coder
  labels:
    app: tds-coder-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tds-coder-backend
  template:
    metadata:
      labels:
        app: tds-coder-backend
    spec:
      containers:
      - name: backend
        image: tdscoder/backend:latest
        envFrom:
        - configMapRef:
            name: tds-coder-config
        - secretRef:
            name: tds-coder-secrets
        ports:
        - containerPort: 8000
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: tds-coder-backend
  namespace: tds-coder
spec:
  selector:
    app: tds-coder-backend
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tds-coder-frontend
  namespace: tds-coder
  labels:
    app: tds-coder-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tds-coder-frontend
  template:
    metadata:
      labels:
        app: tds-coder-frontend
    spec:
      containers:
      - name: frontend
        image: tdscoder/frontend:latest
        env:
        - name: NEXT_PUBLIC_API_URL
          value: "https://api.your-domain.com"
        - name: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
          valueFrom:
            secretKeyRef:
              name: tds-coder-secrets
              key: STRIPE_PUBLISHABLE_KEY
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: tds-coder-frontend
  namespace: tds-coder
spec:
  selector:
    app: tds-coder-frontend
  ports:
  - port: 3000
    targetPort: 3000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tds-coder-ingress
  namespace: tds-coder
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - your-domain.com
    - api.your-domain.com
    secretName: tds-coder-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tds-coder-frontend
            port:
              number: 3000
  - host: api.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tds-coder-backend
            port:
              number: 8000
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: tds-coder
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tds-coder-backend-pdb
  namespace: tds-coder
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tds-coder-backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tds-coder-frontend-pdb
  namespace: tds-coder
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tds-coder-frontend
