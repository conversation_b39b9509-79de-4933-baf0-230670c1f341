"""
Base database model and imports for all models.
"""

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, DateTime, Boolean
from sqlalchemy.sql import func
from datetime import datetime

# Create base class for all models
Base = declarative_base()


class TimestampMixin:
    """Mixin to add created_at and updated_at timestamps to models."""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True,
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True,
    )


class SoftDeleteMixin:
    """Mixin to add soft delete functionality to models."""
    
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    def soft_delete(self):
        """Mark the record as deleted."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """Restore a soft-deleted record."""
        self.is_deleted = False
        self.deleted_at = None


class BaseModel(Base, TimestampMixin, SoftDeleteMixin):
    """Base model class with common fields and functionality."""
    
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    
    def to_dict(self):
        """Convert model instance to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }


# Import all models here to ensure they are registered with SQLAlchemy
# This is important for Alembic migrations to work properly
from app.models.user import User  # noqa
from app.models.subscription import Subscription  # noqa
from app.models.api_key import APIKey  # noqa
from app.models.usage import Usage  # noqa
