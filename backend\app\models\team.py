"""
Team collaboration models for workspace management, shared resources, and member coordination.
"""

from sqlalchemy import Column, String, Integer, DateTime, Boolean, Text, ForeignKey, Table, Index, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from enum import Enum

from app.db.base_class import Base


class TeamRole(str, Enum):
    """Team member roles with different permissions."""
    OWNER = "owner"
    ADMIN = "admin"
    MEMBER = "member"
    VIEWER = "viewer"


class InvitationStatus(str, Enum):
    """Team invitation status."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    EXPIRED = "expired"


# Association table for team members
team_members = Table(
    'team_members',
    Base.metadata,
    Column('team_id', UUID(as_uuid=True), ForeignKey('teams.id'), primary_key=True),
    Column('user_id', UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True),
    <PERSON>umn('role', SQLEnum(TeamRole), nullable=False, default=TeamRole.MEMBER),
    Column('joined_at', DateTime, nullable=False, default=datetime.utcnow),
    Column('is_active', Boolean, default=True, nullable=False),
    Index('idx_team_members_team_id', 'team_id'),
    Index('idx_team_members_user_id', 'user_id'),
    Index('idx_team_members_role', 'role'),
)


class Team(Base):
    """Team workspace for collaboration."""
    __tablename__ = "teams"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    slug = Column(String(100), nullable=False, unique=True)  # URL-friendly identifier
    
    # Team settings
    max_members = Column(Integer, default=10, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False)  # Public teams can be discovered
    allow_external_sharing = Column(Boolean, default=True, nullable=False)
    
    # Collaboration settings
    default_snippet_visibility = Column(String(20), default="team", nullable=False)  # team, public, private
    require_approval_for_snippets = Column(Boolean, default=False, nullable=False)
    enable_code_review = Column(Boolean, default=True, nullable=False)
    
    # Usage pooling
    enable_usage_pooling = Column(Boolean, default=True, nullable=False)
    monthly_usage_limit = Column(Integer, nullable=True)  # Total team limit
    
    # Owner and metadata
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    owner = relationship("User", foreign_keys=[owner_id], back_populates="owned_teams")
    members = relationship("User", secondary=team_members, back_populates="teams")
    snippets = relationship("CodeSnippet", back_populates="team")
    invitations = relationship("TeamInvitation", back_populates="team")
    team_metrics = relationship("TeamMetric", back_populates="team")

    # Indexes
    __table_args__ = (
        Index('idx_teams_owner_id', 'owner_id'),
        Index('idx_teams_slug', 'slug'),
        Index('idx_teams_is_public', 'is_public'),
        Index('idx_teams_created_at', 'created_at'),
    )

    def get_member_role(self, user_id: uuid.UUID) -> TeamRole:
        """Get the role of a specific user in this team."""
        if self.owner_id == user_id:
            return TeamRole.OWNER
        
        # This would need to be implemented with a proper query
        # For now, return MEMBER as default
        return TeamRole.MEMBER

    def can_user_access(self, user_id: uuid.UUID) -> bool:
        """Check if a user can access this team."""
        if self.owner_id == user_id:
            return True
        
        # Check if user is a member (would need proper query)
        return True  # Placeholder

    def get_usage_stats(self) -> dict:
        """Get team usage statistics."""
        # This would aggregate usage from all team members
        return {
            "total_members": len(self.members),
            "monthly_usage": 0,  # Placeholder
            "usage_limit": self.monthly_usage_limit,
        }


class TeamInvitation(Base):
    """Team member invitations."""
    __tablename__ = "team_invitations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    team_id = Column(UUID(as_uuid=True), ForeignKey("teams.id"), nullable=False)
    inviter_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    invitee_email = Column(String(255), nullable=False)
    invitee_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)  # Set when user exists
    
    role = Column(SQLEnum(TeamRole), nullable=False, default=TeamRole.MEMBER)
    status = Column(SQLEnum(InvitationStatus), nullable=False, default=InvitationStatus.PENDING)
    
    invitation_token = Column(String(255), nullable=False, unique=True)
    expires_at = Column(DateTime, nullable=False)
    
    message = Column(Text, nullable=True)  # Optional message from inviter
    
    # Response tracking
    responded_at = Column(DateTime, nullable=True)
    response_message = Column(Text, nullable=True)
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    team = relationship("Team", back_populates="invitations")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invitee = relationship("User", foreign_keys=[invitee_id])

    # Indexes
    __table_args__ = (
        Index('idx_team_invitations_team_id', 'team_id'),
        Index('idx_team_invitations_invitee_email', 'invitee_email'),
        Index('idx_team_invitations_status', 'status'),
        Index('idx_team_invitations_token', 'invitation_token'),
        Index('idx_team_invitations_expires_at', 'expires_at'),
    )

    def is_expired(self) -> bool:
        """Check if invitation has expired."""
        return datetime.utcnow() > self.expires_at

    def can_be_accepted(self) -> bool:
        """Check if invitation can be accepted."""
        return (
            self.status == InvitationStatus.PENDING and
            not self.is_expired() and
            not self.is_deleted
        )


class CodeSnippet(Base):
    """Shared code snippets for team collaboration."""
    __tablename__ = "code_snippets"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    team_id = Column(UUID(as_uuid=True), ForeignKey("teams.id"), nullable=True)  # Null for personal snippets
    author_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Code content
    code = Column(Text, nullable=False)
    language = Column(String(50), nullable=False)
    
    # Metadata
    tags = Column(JSONB, nullable=True)  # ["python", "api", "utility"]
    visibility = Column(String(20), default="team", nullable=False)  # team, public, private
    
    # Usage tracking
    usage_count = Column(Integer, default=0, nullable=False)
    last_used_at = Column(DateTime, nullable=True)
    
    # Approval workflow
    requires_approval = Column(Boolean, default=False, nullable=False)
    is_approved = Column(Boolean, default=True, nullable=False)
    approved_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    approved_at = Column(DateTime, nullable=True)
    
    # Version control
    version = Column(Integer, default=1, nullable=False)
    parent_snippet_id = Column(UUID(as_uuid=True), ForeignKey("code_snippets.id"), nullable=True)
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    team = relationship("Team", back_populates="snippets")
    author = relationship("User", foreign_keys=[author_id], back_populates="authored_snippets")
    approved_by = relationship("User", foreign_keys=[approved_by_id])
    parent_snippet = relationship("CodeSnippet", remote_side=[id])
    child_snippets = relationship("CodeSnippet", back_populates="parent_snippet")

    # Indexes
    __table_args__ = (
        Index('idx_code_snippets_team_id', 'team_id'),
        Index('idx_code_snippets_author_id', 'author_id'),
        Index('idx_code_snippets_language', 'language'),
        Index('idx_code_snippets_visibility', 'visibility'),
        Index('idx_code_snippets_tags', 'tags'),
        Index('idx_code_snippets_created_at', 'created_at'),
    )

    def can_user_access(self, user_id: uuid.UUID, user_teams: list = None) -> bool:
        """Check if a user can access this snippet."""
        # Author can always access
        if self.author_id == user_id:
            return True
        
        # Public snippets are accessible to all
        if self.visibility == "public":
            return True
        
        # Private snippets only accessible to author
        if self.visibility == "private":
            return False
        
        # Team snippets accessible to team members
        if self.visibility == "team" and self.team_id:
            if user_teams:
                return any(team.id == self.team_id for team in user_teams)
            # Would need to check team membership
            return False
        
        return False

    def increment_usage(self):
        """Increment usage counter."""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()


class CodingStandard(Base):
    """Team coding standards and rules."""
    __tablename__ = "coding_standards"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    team_id = Column(UUID(as_uuid=True), ForeignKey("teams.id"), nullable=False)
    
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Rule configuration
    language = Column(String(50), nullable=True)  # Null for language-agnostic rules
    rule_type = Column(String(50), nullable=False)  # naming, formatting, structure, etc.
    rule_config = Column(JSONB, nullable=False)  # Rule-specific configuration
    
    # Enforcement
    is_active = Column(Boolean, default=True, nullable=False)
    enforcement_level = Column(String(20), default="warning", nullable=False)  # error, warning, info
    
    # Metadata
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_deleted = Column(Boolean, default=False, nullable=False)

    # Relationships
    team = relationship("Team")
    created_by = relationship("User")

    # Indexes
    __table_args__ = (
        Index('idx_coding_standards_team_id', 'team_id'),
        Index('idx_coding_standards_language', 'language'),
        Index('idx_coding_standards_rule_type', 'rule_type'),
        Index('idx_coding_standards_is_active', 'is_active'),
    )
