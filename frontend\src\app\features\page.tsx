'use client'

import Link from 'next/link'
import { 
  SparklesIcon, 
  CodeBracketIcon, 
  ShieldCheckIcon,
  BoltIcon,
  CpuChipIcon,
  GlobeAltIcon,
  ChartBarIcon,
  UsersIcon,
  CloudIcon,
  LockClosedIcon,
  RocketLaunchIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline'

const features = [
  {
    name: 'AI-Powered Code Completion',
    description: 'Get intelligent, context-aware code suggestions that understand your project structure and coding patterns.',
    icon: SparklesIcon,
    details: [
      'Multi-line code completion',
      'Function and class generation',
      'Smart variable naming',
      'Context-aware suggestions',
      'Code documentation generation'
    ]
  },
  {
    name: 'Multi-Language Support',
    description: 'Support for 50+ programming languages with specialized models for each language ecosystem.',
    icon: CodeBracketIcon,
    details: [
      'Python, JavaScript, TypeScript',
      'Java, C++, C#, Go, Rust',
      'React, Vue, Angular frameworks',
      'SQL, HTML, CSS, JSON',
      'Shell scripts and configuration files'
    ]
  },
  {
    name: 'Enterprise Security',
    description: 'Bank-grade security with zero data retention and complete privacy protection.',
    icon: ShieldCheckIcon,
    details: [
      'End-to-end encryption',
      'Zero data retention policy',
      'SOC 2 Type II compliance',
      'GDPR and CCPA compliant',
      'On-premise deployment options'
    ]
  },
  {
    name: 'Lightning Fast Performance',
    description: 'Sub-100ms response times with global edge deployment for instant code suggestions.',
    icon: BoltIcon,
    details: [
      'Sub-100ms response times',
      'Global edge network',
      'Offline mode support',
      'Intelligent caching',
      'Optimized for low latency'
    ]
  },
  {
    name: 'Advanced AI Models',
    description: 'State-of-the-art language models fine-tuned specifically for code generation.',
    icon: CpuChipIcon,
    details: [
      'GPT-4 based models',
      'Code-specific training',
      'Continuous model updates',
      'Custom model fine-tuning',
      'Multi-modal understanding'
    ]
  },
  {
    name: 'Universal IDE Integration',
    description: 'Seamless integration with all popular editors and IDEs with native extensions.',
    icon: GlobeAltIcon,
    details: [
      'VS Code native extension',
      'JetBrains plugin suite',
      'Vim/Neovim support',
      'Sublime Text integration',
      'Web-based editor support'
    ]
  },
  {
    name: 'Usage Analytics',
    description: 'Comprehensive analytics to track productivity gains and usage patterns.',
    icon: ChartBarIcon,
    details: [
      'Productivity metrics',
      'Usage statistics',
      'Team performance insights',
      'Cost optimization reports',
      'Custom dashboards'
    ]
  },
  {
    name: 'Team Collaboration',
    description: 'Built-in team features for sharing snippets, templates, and best practices.',
    icon: UsersIcon,
    details: [
      'Shared code snippets',
      'Team templates',
      'Collaborative workspaces',
      'Permission management',
      'Activity tracking'
    ]
  },
  {
    name: 'Cloud & On-Premise',
    description: 'Flexible deployment options to meet your infrastructure and compliance needs.',
    icon: CloudIcon,
    details: [
      'Cloud-hosted service',
      'On-premise deployment',
      'Hybrid configurations',
      'Private cloud support',
      'Air-gapped environments'
    ]
  },
  {
    name: 'Advanced Security Controls',
    description: 'Granular security controls and audit trails for enterprise compliance.',
    icon: LockClosedIcon,
    details: [
      'Role-based access control',
      'Audit logging',
      'IP whitelisting',
      'SSO integration',
      'Compliance reporting'
    ]
  },
  {
    name: 'Continuous Learning',
    description: 'AI models that adapt to your coding style and project patterns over time.',
    icon: AcademicCapIcon,
    details: [
      'Personal coding style adaptation',
      'Project-specific learning',
      'Team pattern recognition',
      'Feedback-driven improvements',
      'Custom model training'
    ]
  },
  {
    name: 'Developer Productivity',
    description: 'Measurable productivity improvements with detailed metrics and insights.',
    icon: RocketLaunchIcon,
    details: [
      '40% faster coding',
      'Reduced debugging time',
      'Fewer syntax errors',
      'Improved code quality',
      'Accelerated learning'
    ]
  }
]

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Link href="/" className="text-xl font-bold text-gray-900 dark:text-white">
              TDS Coder
            </Link>
            <div className="space-x-4">
              <Link href="/pricing" className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                Pricing
              </Link>
              <Link href="/auth/login" className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                Sign In
              </Link>
              <Link href="/auth/register" className="btn-primary px-4 py-2 rounded-md">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-20">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
              Powerful features for modern developers
            </h1>
            <p className="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              TDS Coder combines cutting-edge AI with developer-friendly tools to supercharge your coding workflow. 
              Discover all the features that make developers more productive.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 xl:grid-cols-3">
            {features.map((feature) => (
              <div key={feature.name} className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center mb-6">
                  <div className="flex h-12 w-12 items-center justify-center rounded-md bg-indigo-500 text-white mr-4">
                    <feature.icon className="h-6 w-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {feature.name}
                  </h3>
                </div>
                
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {feature.description}
                </p>
                
                <ul className="space-y-2">
                  {feature.details.map((detail, index) => (
                    <li key={index} className="flex items-start text-sm text-gray-500 dark:text-gray-400">
                      <svg className="h-4 w-4 text-indigo-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {detail}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div className="mt-24 text-center bg-indigo-600 rounded-lg p-12">
            <h2 className="text-3xl font-bold text-white">
              Ready to experience the future of coding?
            </h2>
            <p className="mt-4 text-lg text-indigo-100">
              Join thousands of developers who are already coding faster with TDS Coder.
            </p>
            <div className="mt-8 space-x-4">
              <Link href="/auth/register" className="inline-block bg-white text-indigo-600 px-6 py-3 rounded-md font-medium hover:bg-gray-100">
                Start Free Trial
              </Link>
              <Link href="/pricing" className="inline-block border border-white text-white px-6 py-3 rounded-md font-medium hover:bg-indigo-700">
                View Pricing
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
