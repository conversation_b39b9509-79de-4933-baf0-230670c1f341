import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'TDS Coder - AI-Powered Code Completion',
  description: 'Professional AI coding assistant for developers and teams',
  keywords: ['AI', 'code completion', 'developer tools', 'programming'],
  authors: [{ name: 'TDS Coder Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'TDS Coder - AI-Powered Code Completion',
    description: 'Professional AI coding assistant for developers and teams',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TDS Coder - AI-Powered Code Completion',
    description: 'Professional AI coding assistant for developers and teams',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
