"""
Subscription management endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any
from pydantic import BaseModel

from app.db.session import get_db
from app.utils.security import get_current_active_user
from app.models.user import User
from app.services.stripe_service import StripeService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class SubscriptionUpgrade(BaseModel):
    price_id: str
    payment_method_id: str


class PaymentMethodAdd(BaseModel):
    payment_method_id: str


@router.get("/")
async def get_subscription(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Get current user's subscription."""
    try:
        stripe_service = StripeService(db)
        subscription = await stripe_service.get_user_subscription(current_user)

        return {
            "id": str(subscription.id),
            "tier": subscription.tier,
            "status": subscription.status,
            "current_period_start": subscription.current_period_start,
            "current_period_end": subscription.current_period_end,
            "trial_start": subscription.trial_start,
            "trial_end": subscription.trial_end,
            "canceled_at": subscription.canceled_at,
            "amount": float(subscription.amount) if subscription.amount else None,
            "currency": subscription.currency,
            "daily_request_limit": subscription.daily_request_limit,
            "burst_limit": subscription.burst_limit,
            "seats_limit": subscription.seats_limit,
            "is_trial": subscription.is_trial,
            "is_expired": subscription.is_expired,
            "days_until_renewal": subscription.days_until_renewal,
        }

    except Exception as e:
        logger.error("Failed to get subscription", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subscription"
        )


@router.post("/upgrade")
async def upgrade_subscription(
    upgrade_data: SubscriptionUpgrade,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Upgrade subscription plan."""
    try:
        stripe_service = StripeService(db)

        # Get current subscription
        subscription = await stripe_service.get_user_subscription(current_user)

        if subscription.stripe_subscription_id:
            # Update existing subscription
            stripe_subscription = await stripe_service.update_subscription(
                subscription,
                upgrade_data.price_id
            )
        else:
            # Create new subscription
            result = await stripe_service.create_subscription(
                current_user,
                upgrade_data.price_id,
                upgrade_data.payment_method_id
            )
            stripe_subscription = result["subscription"]

        logger.info("Subscription upgraded successfully",
                   user_id=str(current_user.id),
                   price_id=upgrade_data.price_id)

        return {
            "subscription_id": stripe_subscription.id,
            "status": stripe_subscription.status,
            "client_secret": getattr(stripe_subscription, "client_secret", None)
        }

    except Exception as e:
        logger.error("Failed to upgrade subscription",
                    error=str(e),
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upgrade subscription"
        )


@router.post("/cancel")
async def cancel_subscription(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Cancel subscription at period end."""
    try:
        stripe_service = StripeService(db)
        subscription = await stripe_service.get_user_subscription(current_user)

        if not subscription.stripe_subscription_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No active subscription to cancel"
            )

        stripe_subscription = await stripe_service.cancel_subscription(
            subscription,
            at_period_end=True
        )

        logger.info("Subscription cancelled", user_id=str(current_user.id))

        return {
            "message": "Subscription will be cancelled at the end of the current period",
            "cancel_at_period_end": stripe_subscription.cancel_at_period_end,
            "current_period_end": subscription.current_period_end
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel subscription",
                    error=str(e),
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel subscription"
        )


@router.post("/resume")
async def resume_subscription(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Resume a cancelled subscription."""
    try:
        stripe_service = StripeService(db)
        subscription = await stripe_service.get_user_subscription(current_user)

        if not subscription.stripe_subscription_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No subscription to resume"
            )

        # Resume subscription by removing cancel_at_period_end
        import stripe
        stripe_subscription = stripe.Subscription.modify(
            subscription.stripe_subscription_id,
            cancel_at_period_end=False
        )

        await stripe_service.update_subscription_from_stripe(subscription, stripe_subscription)

        logger.info("Subscription resumed", user_id=str(current_user.id))

        return {
            "message": "Subscription resumed successfully",
            "status": stripe_subscription.status
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to resume subscription",
                    error=str(e),
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resume subscription"
        )


@router.post("/payment-method")
async def add_payment_method(
    payment_data: PaymentMethodAdd,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Add a payment method to the customer."""
    try:
        stripe_service = StripeService(db)

        payment_method = await stripe_service.add_payment_method(
            current_user,
            payment_data.payment_method_id
        )

        logger.info("Payment method added",
                   user_id=str(current_user.id),
                   payment_method_id=payment_method.id)

        return {
            "payment_method_id": payment_method.id,
            "type": payment_method.type,
            "card": payment_method.card if payment_method.type == "card" else None
        }

    except Exception as e:
        logger.error("Failed to add payment method",
                    error=str(e),
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add payment method"
        )
