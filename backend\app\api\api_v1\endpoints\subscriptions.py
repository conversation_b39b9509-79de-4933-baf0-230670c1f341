"""
Subscription management endpoints.
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any

from app.db.session import get_db
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


@router.get("/")
async def get_subscription(
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Get current user's subscription."""
    # TODO: Implement subscription retrieval
    pass


@router.post("/upgrade")
async def upgrade_subscription(
    db: AsyncSession = Depends(get_db),
) -> Any:
    """Upgrade subscription plan."""
    # TODO: Implement Stripe integration for plan upgrade
    pass
