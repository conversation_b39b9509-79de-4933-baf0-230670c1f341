"""
Team collaboration service for managing teams, invitations, and shared resources.
"""

import asyncio
import secrets
import string
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, update
from sqlalchemy.orm import selectinload
import uuid

from app.models.user import User
from app.models.team import Team, TeamInvitation, CodeSnippet, CodingStandard, TeamRole, InvitationStatus
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class TeamService:
    """Service for team collaboration features."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_team(
        self, 
        owner: User, 
        name: str, 
        description: Optional[str] = None,
        max_members: int = 10
    ) -> Team:
        """
        Create a new team.
        
        Args:
            owner: User who will own the team
            name: Team name
            description: Optional team description
            max_members: Maximum number of team members
            
        Returns:
            Created team object
        """
        try:
            # Generate unique slug
            slug = await self._generate_unique_slug(name)
            
            team = Team(
                name=name,
                description=description,
                slug=slug,
                max_members=max_members,
                owner_id=owner.id
            )
            
            self.db.add(team)
            await self.db.commit()
            await self.db.refresh(team)
            
            # Add owner as first member
            await self._add_member_to_team(team, owner, TeamRole.OWNER)
            
            logger.info("Team created successfully", 
                       team_id=str(team.id),
                       owner_id=str(owner.id),
                       team_name=name)
            
            return team
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create team", 
                        error=str(e),
                        owner_id=str(owner.id),
                        team_name=name)
            raise
    
    async def invite_member(
        self, 
        team: Team, 
        inviter: User, 
        invitee_email: str,
        role: TeamRole = TeamRole.MEMBER,
        message: Optional[str] = None
    ) -> TeamInvitation:
        """
        Invite a user to join a team.
        
        Args:
            team: Team to invite to
            inviter: User sending the invitation
            invitee_email: Email of user to invite
            role: Role to assign to invitee
            message: Optional message from inviter
            
        Returns:
            Created invitation object
        """
        try:
            # Check if inviter has permission to invite
            if not await self._can_user_invite(team, inviter):
                raise ValueError("User does not have permission to invite members")
            
            # Check if team has space for new members
            current_member_count = await self._get_team_member_count(team)
            if current_member_count >= team.max_members:
                raise ValueError("Team has reached maximum member limit")
            
            # Check if invitation already exists
            existing_invitation = await self._get_pending_invitation(team.id, invitee_email)
            if existing_invitation:
                raise ValueError("Invitation already exists for this email")
            
            # Check if user is already a member
            invitee = await self._get_user_by_email(invitee_email)
            if invitee and await self._is_user_team_member(team, invitee):
                raise ValueError("User is already a team member")
            
            # Generate invitation token
            invitation_token = self._generate_invitation_token()
            
            # Create invitation
            invitation = TeamInvitation(
                team_id=team.id,
                inviter_id=inviter.id,
                invitee_email=invitee_email,
                invitee_id=invitee.id if invitee else None,
                role=role,
                invitation_token=invitation_token,
                expires_at=datetime.utcnow() + timedelta(days=7),  # 7 days to accept
                message=message
            )
            
            self.db.add(invitation)
            await self.db.commit()
            await self.db.refresh(invitation)
            
            # TODO: Send invitation email
            await self._send_invitation_email(invitation)
            
            logger.info("Team invitation sent", 
                       team_id=str(team.id),
                       inviter_id=str(inviter.id),
                       invitee_email=invitee_email,
                       invitation_id=str(invitation.id))
            
            return invitation
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to send team invitation", 
                        error=str(e),
                        team_id=str(team.id),
                        inviter_id=str(inviter.id),
                        invitee_email=invitee_email)
            raise
    
    async def accept_invitation(
        self, 
        invitation_token: str, 
        user: User
    ) -> Team:
        """
        Accept a team invitation.
        
        Args:
            invitation_token: Invitation token
            user: User accepting the invitation
            
        Returns:
            Team that user joined
        """
        try:
            # Get invitation
            invitation = await self._get_invitation_by_token(invitation_token)
            if not invitation:
                raise ValueError("Invalid invitation token")
            
            if not invitation.can_be_accepted():
                raise ValueError("Invitation cannot be accepted")
            
            # Verify user email matches invitation
            if invitation.invitee_email != user.email:
                raise ValueError("Email does not match invitation")
            
            # Add user to team
            team = invitation.team
            await self._add_member_to_team(team, user, invitation.role)
            
            # Update invitation status
            invitation.status = InvitationStatus.ACCEPTED
            invitation.invitee_id = user.id
            invitation.responded_at = datetime.utcnow()
            
            await self.db.commit()
            
            logger.info("Team invitation accepted", 
                       team_id=str(team.id),
                       user_id=str(user.id),
                       invitation_id=str(invitation.id))
            
            return team
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to accept team invitation", 
                        error=str(e),
                        invitation_token=invitation_token,
                        user_id=str(user.id))
            raise
    
    async def create_code_snippet(
        self, 
        author: User, 
        title: str,
        code: str,
        language: str,
        team: Optional[Team] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        visibility: str = "team"
    ) -> CodeSnippet:
        """
        Create a shared code snippet.
        
        Args:
            author: User creating the snippet
            title: Snippet title
            code: Code content
            language: Programming language
            team: Team to share with (optional)
            description: Optional description
            tags: Optional tags for categorization
            visibility: Visibility level (team, public, private)
            
        Returns:
            Created code snippet
        """
        try:
            # Validate team access if team snippet
            if team and visibility == "team":
                if not await self._is_user_team_member(team, author):
                    raise ValueError("User is not a member of the specified team")
            
            # Check if approval is required
            requires_approval = False
            is_approved = True
            
            if team and team.require_approval_for_snippets:
                user_role = await self._get_user_team_role(team, author)
                if user_role not in [TeamRole.OWNER, TeamRole.ADMIN]:
                    requires_approval = True
                    is_approved = False
            
            snippet = CodeSnippet(
                team_id=team.id if team else None,
                author_id=author.id,
                title=title,
                description=description,
                code=code,
                language=language,
                tags=tags,
                visibility=visibility,
                requires_approval=requires_approval,
                is_approved=is_approved
            )
            
            self.db.add(snippet)
            await self.db.commit()
            await self.db.refresh(snippet)
            
            logger.info("Code snippet created", 
                       snippet_id=str(snippet.id),
                       author_id=str(author.id),
                       team_id=str(team.id) if team else None,
                       language=language)
            
            return snippet
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create code snippet", 
                        error=str(e),
                        author_id=str(author.id),
                        team_id=str(team.id) if team else None)
            raise
    
    async def get_team_snippets(
        self, 
        team: Team, 
        user: User,
        language: Optional[str] = None,
        tags: Optional[List[str]] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[CodeSnippet]:
        """
        Get code snippets for a team.
        
        Args:
            team: Team to get snippets for
            user: User requesting snippets
            language: Optional language filter
            tags: Optional tag filters
            limit: Maximum number of snippets to return
            offset: Number of snippets to skip
            
        Returns:
            List of code snippets
        """
        try:
            # Verify user has access to team
            if not await self._is_user_team_member(team, user):
                raise ValueError("User does not have access to team snippets")
            
            # Build query
            query = select(CodeSnippet).where(
                and_(
                    CodeSnippet.team_id == team.id,
                    CodeSnippet.is_deleted == False,
                    CodeSnippet.is_approved == True
                )
            )
            
            # Apply filters
            if language:
                query = query.where(CodeSnippet.language == language)
            
            if tags:
                # Filter by tags (PostgreSQL JSONB contains)
                for tag in tags:
                    query = query.where(CodeSnippet.tags.contains([tag]))
            
            # Order and paginate
            query = query.order_by(desc(CodeSnippet.created_at)).limit(limit).offset(offset)
            
            result = await self.db.execute(query)
            snippets = result.scalars().all()
            
            return list(snippets)
            
        except Exception as e:
            logger.error("Failed to get team snippets", 
                        error=str(e),
                        team_id=str(team.id),
                        user_id=str(user.id))
            raise
    
    async def get_user_teams(self, user: User) -> List[Team]:
        """Get all teams that a user belongs to."""
        try:
            # This would need a proper join query with the team_members table
            # For now, return empty list as placeholder
            return []
            
        except Exception as e:
            logger.error("Failed to get user teams",
                        error=str(e),
                        user_id=str(user.id))
            raise

    # Helper methods

    async def _generate_unique_slug(self, name: str) -> str:
        """Generate a unique slug for a team."""
        base_slug = name.lower().replace(' ', '-').replace('_', '-')
        # Remove special characters
        base_slug = ''.join(c for c in base_slug if c.isalnum() or c == '-')

        # Check if slug exists
        counter = 0
        slug = base_slug

        while True:
            existing = await self.db.execute(
                select(Team).where(Team.slug == slug)
            )
            if not existing.scalar_one_or_none():
                break

            counter += 1
            slug = f"{base_slug}-{counter}"

        return slug

    async def _add_member_to_team(self, team: Team, user: User, role: TeamRole):
        """Add a user to a team with specified role."""
        # This would need to insert into the team_members association table
        # For now, this is a placeholder
        pass

    async def _can_user_invite(self, team: Team, user: User) -> bool:
        """Check if user can invite members to team."""
        if team.owner_id == user.id:
            return True

        # Check if user is admin
        user_role = await self._get_user_team_role(team, user)
        return user_role in [TeamRole.OWNER, TeamRole.ADMIN]

    async def _get_team_member_count(self, team: Team) -> int:
        """Get current number of team members."""
        # This would count from team_members table
        return 1  # Placeholder

    async def _get_pending_invitation(self, team_id: uuid.UUID, email: str) -> Optional[TeamInvitation]:
        """Get pending invitation for email to team."""
        stmt = select(TeamInvitation).where(
            and_(
                TeamInvitation.team_id == team_id,
                TeamInvitation.invitee_email == email,
                TeamInvitation.status == InvitationStatus.PENDING,
                TeamInvitation.is_deleted == False
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        stmt = select(User).where(
            and_(
                User.email == email,
                User.is_deleted == False
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _is_user_team_member(self, team: Team, user: User) -> bool:
        """Check if user is a member of the team."""
        if team.owner_id == user.id:
            return True

        # This would check team_members table
        return False  # Placeholder

    def _generate_invitation_token(self) -> str:
        """Generate a secure invitation token."""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

    async def _send_invitation_email(self, invitation: TeamInvitation):
        """Send invitation email to invitee."""
        # This would integrate with email service
        # For now, just log the invitation
        logger.info("Invitation email would be sent",
                   invitation_id=str(invitation.id),
                   invitee_email=invitation.invitee_email)

    async def _get_invitation_by_token(self, token: str) -> Optional[TeamInvitation]:
        """Get invitation by token."""
        stmt = select(TeamInvitation).where(
            and_(
                TeamInvitation.invitation_token == token,
                TeamInvitation.is_deleted == False
            )
        ).options(selectinload(TeamInvitation.team))

        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_user_team_role(self, team: Team, user: User) -> TeamRole:
        """Get user's role in team."""
        if team.owner_id == user.id:
            return TeamRole.OWNER

        # This would query team_members table
        return TeamRole.MEMBER  # Placeholder
