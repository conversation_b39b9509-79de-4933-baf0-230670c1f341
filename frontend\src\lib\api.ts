import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

// Create axios instance with base configuration
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL + '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh and errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          const response = await axios.post(
            `${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/refresh`,
            { refresh_token: refreshToken }
          )

          const { access_token, refresh_token: newRefreshToken } = response.data

          // Update tokens
          localStorage.setItem('access_token', access_token)
          localStorage.setItem('refresh_token', newRefreshToken)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.')
    } else if (error.response?.status === 429) {
      toast.error('Too many requests. Please slow down.')
    } else if (error.response?.data?.detail) {
      // Don't show toast for auth errors as they're handled by components
      if (!originalRequest.url?.includes('/auth/')) {
        toast.error(error.response.data.detail)
      }
    } else if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please try again.')
    } else if (!error.response) {
      toast.error('Network error. Please check your connection.')
    }

    return Promise.reject(error)
  }
)

// API helper functions
export const apiHelpers = {
  // Auth endpoints
  auth: {
    login: (credentials: any) => api.post('/auth/login', credentials),
    register: (data: any) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    refresh: (refreshToken: string) => api.post('/auth/refresh', { refresh_token: refreshToken }),
    forgotPassword: (email: string) => api.post('/auth/forgot-password', { email }),
    resetPassword: (token: string, password: string) => 
      api.post('/auth/reset-password', { token, new_password: password, confirm_password: password }),
    verifyEmail: (token: string) => api.post('/auth/verify-email', { token }),
  },

  // User endpoints
  users: {
    me: () => api.get('/users/me'),
    update: (data: any) => api.put('/users/me', data),
    changePassword: (data: any) => api.post('/users/me/change-password', data),
    enable2FA: (data: any) => api.post('/users/me/2fa/enable', data),
    disable2FA: (data: any) => api.post('/users/me/2fa/disable', data),
  },

  // Subscription endpoints
  subscriptions: {
    get: () => api.get('/subscriptions'),
    upgrade: (planId: string) => api.post('/subscriptions/upgrade', { plan_id: planId }),
    cancel: () => api.post('/subscriptions/cancel'),
    resume: () => api.post('/subscriptions/resume'),
    updatePaymentMethod: (paymentMethodId: string) => 
      api.post('/subscriptions/payment-method', { payment_method_id: paymentMethodId }),
  },

  // API Keys endpoints
  apiKeys: {
    list: () => api.get('/api-keys'),
    create: (data: any) => api.post('/api-keys', data),
    revoke: (keyId: string) => api.delete(`/api-keys/${keyId}`),
    update: (keyId: string, data: any) => api.put(`/api-keys/${keyId}`, data),
  },

  // Usage endpoints
  usage: {
    stats: () => api.get('/usage'),
    daily: (days: number = 30) => api.get(`/usage/daily?days=${days}`),
    monthly: (months: number = 12) => api.get(`/usage/monthly?months=${months}`),
    export: (startDate: string, endDate: string) => 
      api.get(`/usage/export?start_date=${startDate}&end_date=${endDate}`),
  },

  // Completions endpoints (Phase 2)
  completions: {
    create: (data: any) => api.post('/completions', data),
    chat: (data: any) => api.post('/completions/chat', data),
  },

  // Analytics endpoints (Phase 2)
  analytics: {
    getUserProductivity: (startDate: string, endDate: string) =>
      api.get(`/analytics/user/productivity?start_date=${startDate}&end_date=${endDate}`),

    getTeamAnalytics: (teamId: string, startDate: string, endDate: string) =>
      api.get(`/analytics/team/${teamId}/analytics?start_date=${startDate}&end_date=${endDate}`),

    getBusinessMetrics: (startDate: string, endDate: string) =>
      api.get(`/analytics/business/metrics?start_date=${startDate}&end_date=${endDate}`),

    exportUserReport: async (startDate: string, endDate: string, format: 'pdf' | 'csv') => {
      const response = await fetch(`${API_BASE_URL}/analytics/user/export?start_date=${startDate}&end_date=${endDate}&format=${format}`, {
        headers: {
          'Authorization': `Bearer ${getToken()}`,
        },
      })

      if (!response.ok) {
        throw new Error('Failed to export report')
      }

      return response.blob()
    },

    exportTeamReport: async (teamId: string, startDate: string, endDate: string, format: 'pdf' | 'csv') => {
      const response = await fetch(`${API_BASE_URL}/analytics/team/${teamId}/export?start_date=${startDate}&end_date=${endDate}&format=${format}`, {
        headers: {
          'Authorization': `Bearer ${getToken()}`,
        },
      })

      if (!response.ok) {
        throw new Error('Failed to export report')
      }

      return response.blob()
    },
  },
}

export { api }
export default api
