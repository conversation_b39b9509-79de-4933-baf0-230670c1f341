"""
API key authentication middleware for protecting API endpoints.
"""

from fastapi import Request, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import re

from app.db.session import get_db
from app.models.api_key import APIKey
from app.models.user import User
from app.services.api_key import APIKeyService
from app.services.usage import UsageService
from app.core.logging import get_logger

logger = get_logger(__name__)

# Security scheme for API key authentication
api_key_scheme = HTTPBearer(scheme_name="API Key")


class APIKeyAuth:
    """API key authentication dependency."""
    
    def __init__(self, required_scopes: Optional[list] = None):
        self.required_scopes = required_scopes or []
    
    async def __call__(
        self,
        request: Request,
        credentials: HTTPAuthorizationCredentials = Depends(api_key_scheme),
        db: AsyncSession = Depends(get_db),
    ) -> tuple[<PERSON>Key, User]:
        """
        Authenticate API key and return API key and user objects.
        
        Args:
            request: FastAPI request object
            credentials: HTTP Bearer credentials
            db: Database session
            
        Returns:
            Tuple of (APIKey, User) objects
            
        Raises:
            HTTPException: If authentication fails
        """
        try:
            # Extract API key from credentials
            api_key_value = credentials.credentials
            
            # Validate API key format
            if not self._is_valid_api_key_format(api_key_value):
                logger.warning("Invalid API key format", client_ip=self._get_client_ip(request))
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid API key format",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Authenticate API key
            api_key_service = APIKeyService(db)
            api_key = await api_key_service.authenticate_api_key(api_key_value)
            
            if not api_key:
                logger.warning("Invalid API key", client_ip=self._get_client_ip(request))
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired API key",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Get user
            user = await db.get(User, api_key.user_id)
            if not user or not user.is_active or user.is_deleted:
                logger.warning("API key belongs to inactive user", 
                             api_key_id=str(api_key.id),
                             user_id=str(api_key.user_id))
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User account is inactive",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Check IP restrictions
            client_ip = self._get_client_ip(request)
            if not self._check_ip_allowed(api_key, client_ip):
                logger.warning("API key access denied from IP", 
                             api_key_id=str(api_key.id),
                             client_ip=client_ip)
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied from this IP address",
                )
            
            # Check scopes
            if not self._check_scopes(api_key, self.required_scopes):
                logger.warning("API key lacks required scopes", 
                             api_key_id=str(api_key.id),
                             required_scopes=self.required_scopes)
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions",
                )
            
            # Check rate limits
            rate_limit_result = await api_key_service.check_rate_limit(api_key, client_ip)
            if not rate_limit_result.get("allowed", True):
                logger.warning("Rate limit exceeded", 
                             api_key_id=str(api_key.id),
                             daily_usage=rate_limit_result.get("daily_usage"),
                             burst_usage=rate_limit_result.get("burst_usage"))
                
                # Determine which limit was exceeded
                if rate_limit_result.get("daily_exceeded"):
                    detail = f"Daily quota exceeded. Limit: {rate_limit_result.get('daily_limit')}"
                    retry_after = str(int((rate_limit_result.get("reset_time") - 
                                         request.state.start_time).total_seconds()))
                else:
                    detail = f"Rate limit exceeded. Limit: {rate_limit_result.get('burst_limit')} requests per minute"
                    retry_after = "60"
                
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=detail,
                    headers={
                        "Retry-After": retry_after,
                        "X-RateLimit-Limit": str(rate_limit_result.get("daily_limit", 0)),
                        "X-RateLimit-Remaining": str(rate_limit_result.get("daily_remaining", 0)),
                        "X-RateLimit-Reset": str(int(rate_limit_result.get("reset_time").timestamp())),
                    },
                )
            
            # Add rate limit headers to response
            request.state.rate_limit_headers = {
                "X-RateLimit-Limit": str(rate_limit_result.get("daily_limit", 0)),
                "X-RateLimit-Remaining": str(rate_limit_result.get("daily_remaining", 0)),
                "X-RateLimit-Reset": str(int(rate_limit_result.get("reset_time").timestamp())),
            }
            
            # Store API key and user in request state for usage tracking
            request.state.api_key = api_key
            request.state.authenticated_user = user
            
            logger.info("API key authenticated successfully", 
                       api_key_id=str(api_key.id),
                       user_id=str(user.id),
                       client_ip=client_ip)
            
            return api_key, user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("API key authentication error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service error",
            )
    
    @staticmethod
    def _is_valid_api_key_format(api_key: str) -> bool:
        """Check if API key has valid format."""
        # TDS API keys should start with 'tds_' and be followed by base64url characters
        pattern = r'^tds_[A-Za-z0-9_-]{32,}$'
        return bool(re.match(pattern, api_key))
    
    @staticmethod
    def _get_client_ip(request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind proxy/load balancer)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection IP
        return request.client.host if request.client else "unknown"
    
    @staticmethod
    def _check_ip_allowed(api_key: APIKey, client_ip: str) -> bool:
        """Check if client IP is allowed for this API key."""
        allowed_ips = api_key.get_allowed_ips()
        if not allowed_ips:
            return True  # No IP restrictions
        
        # Check if client IP matches any allowed IP/CIDR
        import ipaddress
        
        try:
            client_addr = ipaddress.ip_address(client_ip)
            
            for allowed_ip in allowed_ips:
                try:
                    # Try as network (CIDR notation)
                    if "/" in allowed_ip:
                        network = ipaddress.ip_network(allowed_ip, strict=False)
                        if client_addr in network:
                            return True
                    else:
                        # Try as single IP
                        allowed_addr = ipaddress.ip_address(allowed_ip)
                        if client_addr == allowed_addr:
                            return True
                except ValueError:
                    # Invalid IP format, skip
                    continue
            
            return False
            
        except ValueError:
            # Invalid client IP, deny access
            return False
    
    @staticmethod
    def _check_scopes(api_key: APIKey, required_scopes: list) -> bool:
        """Check if API key has required scopes."""
        if not required_scopes:
            return True  # No scope requirements
        
        api_key_scopes = api_key.get_scopes()
        if not api_key_scopes:
            return False  # API key has no scopes but scopes are required
        
        # Check if API key has all required scopes
        return all(scope in api_key_scopes for scope in required_scopes)


# Convenience functions for different scope requirements
def require_api_key(scopes: Optional[list] = None):
    """Dependency that requires a valid API key with optional scopes."""
    return APIKeyAuth(required_scopes=scopes)


def require_completion_scope():
    """Dependency that requires API key with completion scope."""
    return APIKeyAuth(required_scopes=["completions"])


def require_chat_scope():
    """Dependency that requires API key with chat scope."""
    return APIKeyAuth(required_scopes=["chat"])


def require_admin_scope():
    """Dependency that requires API key with admin scope."""
    return APIKeyAuth(required_scopes=["admin"])
