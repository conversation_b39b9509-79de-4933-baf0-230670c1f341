"""
AI model service for code completion and chat functionality.
Supports multiple AI providers (OpenAI, Anthropic) with fallback mechanisms.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, AsyncGenerator, Any, Tuple
from enum import Enum
import tiktoken
import openai
import anthropic
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.logging import get_logger
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionTier
from app.models.usage import Usage
from app.services.cache import CacheService

logger = get_logger(__name__)


class ModelProvider(str, Enum):
    """AI model providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    MISTRAL = "mistral"
    DEEPSEEK = "deepseek"
    GOOGLE = "google"
    COHERE = "cohere"
    META = "meta"
    QWEN = "qwen"
    STARCODER = "starcoder"
    LOCAL = "local"


class CompletionType(str, Enum):
    """Types of code completion."""
    INLINE = "inline"
    FUNCTION = "function"
    CLASS = "class"
    COMMENT = "comment"
    DOCSTRING = "docstring"
    CHAT = "chat"


class AIModelService:
    """Service for AI model interactions and code completion."""
    
    def __init__(self, db: AsyncSession, cache_service: Optional[CacheService] = None):
        self.db = db
        self.cache_service = cache_service
        
        # Initialize AI clients
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.ANTHROPIC_API_KEY) if settings.ANTHROPIC_API_KEY else None

        # Initialize additional provider clients
        self._init_additional_clients()

        # Model configurations with all supported providers
        self.model_configs = self._get_model_configurations()

        # Initialize intelligent model selector
        self.model_selector = None  # Will be initialized when needed
    
    async def get_available_models(self, user: User) -> List[str]:
        """Get available models based on user's subscription tier."""
        try:
            # Get user's subscription
            stmt = select(Subscription).where(
                Subscription.user_id == user.id,
                Subscription.is_deleted == False
            )
            result = await self.db.execute(stmt)
            subscription = result.scalar_one_or_none()
            
            if not subscription:
                return settings.FREE_TIER_MODELS
            
            # Return models based on tier
            tier_models = {
                SubscriptionTier.FREE: settings.FREE_TIER_MODELS,
                SubscriptionTier.SOLO: settings.SOLO_TIER_MODELS,
                SubscriptionTier.TEAM: settings.TEAM_TIER_MODELS,
                SubscriptionTier.ENTERPRISE: settings.ENTERPRISE_TIER_MODELS,
            }
            
            return tier_models.get(subscription.tier, settings.FREE_TIER_MODELS)
            
        except Exception as e:
            logger.error("Failed to get available models", error=str(e), user_id=str(user.id))
            return settings.FREE_TIER_MODELS
    
    async def create_completion(
        self,
        user: User,
        prompt: str,
        language: str = "python",
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        completion_type: CompletionType = CompletionType.INLINE,
        context: Optional[Dict[str, Any]] = None,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a code completion using the specified AI model.
        
        Args:
            user: User making the request
            prompt: Code prompt for completion
            language: Programming language
            model: AI model to use (defaults to user's tier default)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            completion_type: Type of completion
            context: Additional context (file path, surrounding code, etc.)
            stream: Whether to stream the response
            
        Returns:
            Completion response with generated code
        """
        start_time = time.time()
        
        try:
            # Validate model availability
            available_models = await self.get_available_models(user)
            if model and model not in available_models:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            elif not model:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            
            # Set defaults
            max_tokens = max_tokens or settings.MAX_TOKENS_DEFAULT
            temperature = temperature or settings.TEMPERATURE_DEFAULT
            
            # Check cache first
            cache_key = self._generate_cache_key(prompt, language, model, max_tokens, temperature)
            if self.cache_service and not stream:
                cached_result = await self.cache_service.get(cache_key)
                if cached_result:
                    logger.info("Cache hit for completion", user_id=str(user.id), model=model)
                    return json.loads(cached_result)
            
            # Prepare context-aware prompt
            enhanced_prompt = self._enhance_prompt_with_context(prompt, language, context, completion_type)
            
            # Get model configuration
            model_config = self.model_configs.get(model, self.model_configs[settings.DEFAULT_MODEL])
            
            # Generate completion based on provider
            provider = model_config["provider"]
            if provider == ModelProvider.OPENAI:
                result = await self._create_openai_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.ANTHROPIC:
                result = await self._create_anthropic_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.MISTRAL:
                result = await self._create_mistral_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.DEEPSEEK:
                result = await self._create_deepseek_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.GOOGLE:
                result = await self._create_google_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.COHERE:
                result = await self._create_cohere_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.META:
                result = await self._create_meta_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.QWEN:
                result = await self._create_qwen_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif provider == ModelProvider.STARCODER:
                result = await self._create_starcoder_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            else:
                raise ValueError(f"Unsupported model provider: {provider}")
            
            # Calculate metrics
            response_time_ms = int((time.time() - start_time) * 1000)
            
            # Estimate token usage and cost
            input_tokens = self._estimate_tokens(enhanced_prompt, model)
            output_tokens = self._estimate_tokens(result.get("text", ""), model)
            cost_cents = self._calculate_cost(input_tokens, output_tokens, model_config)
            
            # Add metadata to result
            result.update({
                "model": model,
                "language": language,
                "completion_type": completion_type,
                "response_time_ms": response_time_ms,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": input_tokens + output_tokens,
                "cost_cents": cost_cents,
            })
            
            # Cache the result
            if self.cache_service and not stream:
                await self.cache_service.set(
                    cache_key, 
                    json.dumps(result), 
                    expire=3600  # Cache for 1 hour
                )
            
            # Track usage
            await self._track_usage(
                user=user,
                model=model,
                completion_type=completion_type,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                cost_cents=cost_cents,
                response_time_ms=response_time_ms,
                language=language,
            )
            
            logger.info("Completion generated successfully", 
                       user_id=str(user.id),
                       model=model,
                       tokens=input_tokens + output_tokens,
                       response_time_ms=response_time_ms)
            
            return result
            
        except Exception as e:
            logger.error("Failed to create completion", 
                        error=str(e), 
                        user_id=str(user.id),
                        model=model)
            
            # Try fallback model
            if model != settings.DEFAULT_MODEL:
                logger.info("Attempting fallback to default model", 
                           original_model=model,
                           fallback_model=settings.DEFAULT_MODEL)
                return await self.create_completion(
                    user=user,
                    prompt=prompt,
                    language=language,
                    model=settings.DEFAULT_MODEL,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    completion_type=completion_type,
                    context=context,
                    stream=stream,
                )
            
            raise
    
    async def create_streaming_completion(
        self,
        user: User,
        prompt: str,
        language: str = "python",
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        completion_type: CompletionType = CompletionType.INLINE,
        context: Optional[Dict[str, Any]] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Create a streaming code completion."""
        try:
            # Validate model availability
            available_models = await self.get_available_models(user)
            if model and model not in available_models:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            elif not model:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            
            # Set defaults
            max_tokens = max_tokens or settings.MAX_TOKENS_DEFAULT
            temperature = temperature or settings.TEMPERATURE_DEFAULT
            
            # Prepare context-aware prompt
            enhanced_prompt = self._enhance_prompt_with_context(prompt, language, context, completion_type)
            
            # Get model configuration
            model_config = self.model_configs.get(model, self.model_configs[settings.DEFAULT_MODEL])
            
            # Stream completion based on provider
            provider = model_config["provider"]
            if provider == ModelProvider.OPENAI:
                async for chunk in self._stream_openai_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.ANTHROPIC:
                async for chunk in self._stream_anthropic_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.MISTRAL:
                async for chunk in self._stream_mistral_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.DEEPSEEK:
                async for chunk in self._stream_deepseek_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.GOOGLE:
                async for chunk in self._stream_google_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.COHERE:
                async for chunk in self._stream_cohere_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.META:
                async for chunk in self._stream_meta_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.QWEN:
                async for chunk in self._stream_qwen_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif provider == ModelProvider.STARCODER:
                async for chunk in self._stream_starcoder_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            else:
                raise ValueError(f"Unsupported model provider: {provider}")
                
        except Exception as e:
            logger.error("Failed to create streaming completion", 
                        error=str(e), 
                        user_id=str(user.id),
                        model=model)
            yield {"error": str(e), "type": "completion_error"}
    
    def _enhance_prompt_with_context(
        self, 
        prompt: str, 
        language: str, 
        context: Optional[Dict[str, Any]], 
        completion_type: CompletionType
    ) -> str:
        """Enhance the prompt with context and language-specific instructions."""
        
        # Base system prompt based on completion type
        system_prompts = {
            CompletionType.INLINE: f"Complete the following {language} code. Provide only the completion without explanations:",
            CompletionType.FUNCTION: f"Generate a complete {language} function based on the following prompt:",
            CompletionType.CLASS: f"Generate a complete {language} class based on the following prompt:",
            CompletionType.COMMENT: f"Generate appropriate comments for the following {language} code:",
            CompletionType.DOCSTRING: f"Generate a comprehensive docstring for the following {language} code:",
            CompletionType.CHAT: f"You are an expert {language} developer. Help with the following question:",
        }
        
        enhanced_prompt = system_prompts.get(completion_type, system_prompts[CompletionType.INLINE])
        enhanced_prompt += f"\n\nLanguage: {language}\n"
        
        # Add context if available
        if context:
            if context.get("file_path"):
                enhanced_prompt += f"File: {context['file_path']}\n"
            if context.get("surrounding_code"):
                enhanced_prompt += f"Surrounding code:\n{context['surrounding_code']}\n"
            if context.get("project_info"):
                enhanced_prompt += f"Project context: {context['project_info']}\n"
        
        enhanced_prompt += f"\nCode to complete:\n{prompt}"
        
        return enhanced_prompt

    async def _create_openai_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using OpenAI API."""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream,
            )

            if stream:
                return response

            return {
                "text": response.choices[0].message.content,
                "finish_reason": response.choices[0].finish_reason,
                "provider": ModelProvider.OPENAI,
            }

        except Exception as e:
            logger.error("OpenAI completion failed", error=str(e), model=model)
            raise

    async def _create_anthropic_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Anthropic API."""
        if not self.anthropic_client:
            raise ValueError("Anthropic client not configured")

        try:
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}],
                stream=stream,
            )

            if stream:
                return response

            return {
                "text": response.content[0].text,
                "finish_reason": "stop",
                "provider": ModelProvider.ANTHROPIC,
            }

        except Exception as e:
            logger.error("Anthropic completion failed", error=str(e), model=model)
            raise

    async def _stream_openai_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using OpenAI API."""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        try:
            stream = await self.openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield {
                        "text": chunk.choices[0].delta.content,
                        "finish_reason": chunk.choices[0].finish_reason,
                        "provider": ModelProvider.OPENAI,
                    }

        except Exception as e:
            logger.error("OpenAI streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}

    async def _stream_anthropic_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Anthropic API."""
        if not self.anthropic_client:
            raise ValueError("Anthropic client not configured")

        try:
            async with self.anthropic_client.messages.stream(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}],
            ) as stream:
                async for text in stream.text_stream:
                    yield {
                        "text": text,
                        "finish_reason": None,
                        "provider": ModelProvider.ANTHROPIC,
                    }

        except Exception as e:
            logger.error("Anthropic streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}

    # Mistral AI completion methods
    async def _create_mistral_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Mistral AI API."""
        if not self.mistral_client:
            raise ValueError("Mistral client not configured")

        try:
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }

            response = await self.mistral_client.post("/chat/completions", json=payload)
            response.raise_for_status()
            data = response.json()

            if stream:
                return data

            return {
                "text": data["choices"][0]["message"]["content"],
                "finish_reason": data["choices"][0]["finish_reason"],
                "provider": ModelProvider.MISTRAL,
            }

        except Exception as e:
            logger.error("Mistral completion failed", error=str(e), model=model)
            raise

    async def _stream_mistral_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Mistral AI API."""
        if not self.mistral_client:
            raise ValueError("Mistral client not configured")

        try:
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True,
            }

            async with self.mistral_client.stream("POST", "/chat/completions", json=payload) as response:
                response.raise_for_status()
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if data["choices"][0]["delta"].get("content"):
                                yield {
                                    "text": data["choices"][0]["delta"]["content"],
                                    "finish_reason": data["choices"][0]["finish_reason"],
                                    "provider": ModelProvider.MISTRAL,
                                }
                        except json.JSONDecodeError:
                            continue

        except Exception as e:
            logger.error("Mistral streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}

    # DeepSeek completion methods
    async def _create_deepseek_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using DeepSeek API."""
        if not self.deepseek_client:
            raise ValueError("DeepSeek client not configured")

        try:
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }

            response = await self.deepseek_client.post("/chat/completions", json=payload)
            response.raise_for_status()
            data = response.json()

            if stream:
                return data

            return {
                "text": data["choices"][0]["message"]["content"],
                "finish_reason": data["choices"][0]["finish_reason"],
                "provider": ModelProvider.DEEPSEEK,
            }

        except Exception as e:
            logger.error("DeepSeek completion failed", error=str(e), model=model)
            raise

    async def _stream_deepseek_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using DeepSeek API."""
        if not self.deepseek_client:
            raise ValueError("DeepSeek client not configured")

        try:
            payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True,
            }

            async with self.deepseek_client.stream("POST", "/chat/completions", json=payload) as response:
                response.raise_for_status()
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if data["choices"][0]["delta"].get("content"):
                                yield {
                                    "text": data["choices"][0]["delta"]["content"],
                                    "finish_reason": data["choices"][0]["finish_reason"],
                                    "provider": ModelProvider.DEEPSEEK,
                                }
                        except json.JSONDecodeError:
                            continue

        except Exception as e:
            logger.error("DeepSeek streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}

    # Google AI completion methods
    async def _create_google_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Google AI API."""
        if not self.google_client:
            raise ValueError("Google AI client not configured")

        from app.services.ai_providers import GoogleAIProvider
        provider = GoogleAIProvider(self.google_client)
        return await provider.create_completion(prompt, model, max_tokens, temperature, stream)

    async def _stream_google_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Google AI API."""
        if not self.google_client:
            raise ValueError("Google AI client not configured")

        from app.services.ai_providers import GoogleAIProvider
        provider = GoogleAIProvider(self.google_client)
        async for chunk in provider.stream_completion(prompt, model, max_tokens, temperature):
            yield chunk

    # Cohere completion methods
    async def _create_cohere_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Cohere API."""
        if not self.cohere_client:
            raise ValueError("Cohere client not configured")

        from app.services.ai_providers import CohereProvider
        provider = CohereProvider(self.cohere_client)
        return await provider.create_completion(prompt, model, max_tokens, temperature, stream)

    async def _stream_cohere_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Cohere API."""
        if not self.cohere_client:
            raise ValueError("Cohere client not configured")

        from app.services.ai_providers import CohereProvider
        provider = CohereProvider(self.cohere_client)
        async for chunk in provider.stream_completion(prompt, model, max_tokens, temperature):
            yield chunk

    # Meta completion methods
    async def _create_meta_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Meta/Llama models."""
        if not self.meta_client:
            raise ValueError("Meta client not configured")

        from app.services.ai_providers import MetaProvider
        provider = MetaProvider(self.meta_client)
        return await provider.create_completion(prompt, model, max_tokens, temperature, stream)

    async def _stream_meta_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Meta/Llama models."""
        if not self.meta_client:
            raise ValueError("Meta client not configured")

        from app.services.ai_providers import MetaProvider
        provider = MetaProvider(self.meta_client)
        async for chunk in provider.stream_completion(prompt, model, max_tokens, temperature):
            yield chunk

    # Qwen completion methods
    async def _create_qwen_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Qwen models."""
        if not self.qwen_client:
            raise ValueError("Qwen client not configured")

        from app.services.ai_providers import QwenProvider
        provider = QwenProvider(self.qwen_client)
        return await provider.create_completion(prompt, model, max_tokens, temperature, stream)

    async def _stream_qwen_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Qwen models."""
        if not self.qwen_client:
            raise ValueError("Qwen client not configured")

        from app.services.ai_providers import QwenProvider
        provider = QwenProvider(self.qwen_client)
        async for chunk in provider.stream_completion(prompt, model, max_tokens, temperature):
            yield chunk

    # StarCoder completion methods
    async def _create_starcoder_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using StarCoder models."""
        if not self.starcoder_client:
            raise ValueError("StarCoder client not configured")

        from app.services.ai_providers import StarCoderProvider
        provider = StarCoderProvider(self.starcoder_client)
        return await provider.create_completion(prompt, model, max_tokens, temperature, stream)

    async def _stream_starcoder_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using StarCoder models."""
        if not self.starcoder_client:
            raise ValueError("StarCoder client not configured")

        from app.services.ai_providers import StarCoderProvider
        provider = StarCoderProvider(self.starcoder_client)
        async for chunk in provider.stream_completion(prompt, model, max_tokens, temperature):
            yield chunk

    def _estimate_tokens(self, text: str, model: str) -> int:
        """Estimate token count for text using tiktoken."""
        try:
            # Map models to tiktoken encodings
            encoding_map = {
                "gpt-3.5-turbo": "cl100k_base",
                "gpt-4": "cl100k_base",
                "gpt-4-turbo": "cl100k_base",
            }

            encoding_name = encoding_map.get(model, "cl100k_base")
            encoding = tiktoken.get_encoding(encoding_name)
            return len(encoding.encode(text))

        except Exception:
            # Fallback estimation: ~4 characters per token
            return len(text) // 4

    def _calculate_cost(self, input_tokens: int, output_tokens: int, model_config: Dict[str, Any]) -> int:
        """Calculate cost in cents for token usage."""
        cost_per_1k = model_config.get("cost_per_1k_tokens", 0.002)
        total_tokens = input_tokens + output_tokens
        return int((total_tokens / 1000) * cost_per_1k * 100)  # Convert to cents

    def _generate_cache_key(
        self,
        prompt: str,
        language: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> str:
        """Generate cache key for completion request."""
        import hashlib

        key_data = f"{prompt}:{language}:{model}:{max_tokens}:{temperature}"
        return f"completion:{hashlib.md5(key_data.encode()).hexdigest()}"

    async def _track_usage(
        self,
        user: User,
        model: str,
        completion_type: CompletionType,
        input_tokens: int,
        output_tokens: int,
        cost_cents: int,
        response_time_ms: int,
        language: str,
    ) -> None:
        """Track usage for analytics and billing."""
        try:
            from datetime import datetime

            usage = Usage(
                user_id=user.id,
                endpoint="/v1/completions",
                method="POST",
                timestamp=datetime.utcnow(),
                response_time_ms=response_time_ms,
                status_code=200,
                model_used=model,
                tokens_input=input_tokens,
                tokens_output=output_tokens,
                completion_type=completion_type.value,
                cost_cents=cost_cents,
                metadata=json.dumps({
                    "language": language,
                    "model": model,
                    "completion_type": completion_type.value,
                })
            )

            self.db.add(usage)
            await self.db.commit()

        except Exception as e:
            logger.error("Failed to track usage", error=str(e), user_id=str(user.id))

    def _get_model_selector(self):
        """Get or create intelligent model selector."""
        if self.model_selector is None:
            from app.services.model_selection import IntelligentModelSelector
            self.model_selector = IntelligentModelSelector(self.db, self)
        return self.model_selector

    async def select_optimal_model_for_task(
        self,
        user: User,
        task_type: str = "general",
        context_length: int = 0,
        preferred_model: Optional[str] = None
    ) -> str:
        """Select the optimal model for a specific task."""
        try:
            if preferred_model and preferred_model in await self.get_available_models(user):
                return preferred_model

            selector = self._get_model_selector()
            selected_model, reasoning = await selector.select_optimal_model(
                user=user,
                task_type=task_type,
                context_length=context_length
            )

            logger.info("Optimal model selected",
                       user_id=str(user.id),
                       selected_model=selected_model,
                       reasoning=reasoning)

            return selected_model

        except Exception as e:
            logger.error("Optimal model selection failed",
                        error=str(e),
                        user_id=str(user.id))
            return settings.DEFAULT_MODEL

    def _init_additional_clients(self):
        """Initialize clients for additional AI providers."""
        import httpx

        # Mistral AI client
        self.mistral_client = None
        if hasattr(settings, 'MISTRAL_API_KEY') and settings.MISTRAL_API_KEY:
            self.mistral_client = httpx.AsyncClient(
                base_url="https://api.mistral.ai/v1",
                headers={"Authorization": f"Bearer {settings.MISTRAL_API_KEY}"}
            )

        # DeepSeek client
        self.deepseek_client = None
        if hasattr(settings, 'DEEPSEEK_API_KEY') and settings.DEEPSEEK_API_KEY:
            self.deepseek_client = httpx.AsyncClient(
                base_url="https://api.deepseek.com/v1",
                headers={"Authorization": f"Bearer {settings.DEEPSEEK_API_KEY}"}
            )

        # Google AI client
        self.google_client = None
        if hasattr(settings, 'GOOGLE_AI_API_KEY') and settings.GOOGLE_AI_API_KEY:
            self.google_client = httpx.AsyncClient(
                base_url="https://generativelanguage.googleapis.com/v1beta",
                headers={"x-goog-api-key": settings.GOOGLE_AI_API_KEY}
            )

        # Cohere client
        self.cohere_client = None
        if hasattr(settings, 'COHERE_API_KEY') and settings.COHERE_API_KEY:
            self.cohere_client = httpx.AsyncClient(
                base_url="https://api.cohere.ai/v1",
                headers={"Authorization": f"Bearer {settings.COHERE_API_KEY}"}
            )

        # Meta/Llama client (via Replicate or Together AI)
        self.meta_client = None
        if hasattr(settings, 'META_API_KEY') and settings.META_API_KEY:
            self.meta_client = httpx.AsyncClient(
                base_url="https://api.together.xyz/v1",
                headers={"Authorization": f"Bearer {settings.META_API_KEY}"}
            )

        # Qwen client (via DashScope)
        self.qwen_client = None
        if hasattr(settings, 'QWEN_API_KEY') and settings.QWEN_API_KEY:
            self.qwen_client = httpx.AsyncClient(
                base_url="https://dashscope.aliyuncs.com/api/v1",
                headers={"Authorization": f"Bearer {settings.QWEN_API_KEY}"}
            )

        # StarCoder client (via Hugging Face)
        self.starcoder_client = None
        if hasattr(settings, 'HUGGINGFACE_API_KEY') and settings.HUGGINGFACE_API_KEY:
            self.starcoder_client = httpx.AsyncClient(
                base_url="https://api-inference.huggingface.co",
                headers={"Authorization": f"Bearer {settings.HUGGINGFACE_API_KEY}"}
            )

    def _get_model_configurations(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive model configurations for all supported providers."""
        return {
            # OpenAI Models
            "gpt-3.5-turbo": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 4096,
                "context_window": 16385,
                "cost_per_1k_input_tokens": 0.0015,
                "cost_per_1k_output_tokens": 0.002,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["general", "chat", "quick_completions"],
            },
            "gpt-4": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 8192,
                "context_window": 8192,
                "cost_per_1k_input_tokens": 0.03,
                "cost_per_1k_output_tokens": 0.06,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["complex_reasoning", "code_review", "architecture"],
            },
            "gpt-4-turbo": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 4096,
                "context_window": 128000,
                "cost_per_1k_input_tokens": 0.01,
                "cost_per_1k_output_tokens": 0.03,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["large_context", "document_analysis", "refactoring"],
            },
            "gpt-4o": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 4096,
                "context_window": 128000,
                "cost_per_1k_input_tokens": 0.005,
                "cost_per_1k_output_tokens": 0.015,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["balanced_performance", "cost_efficiency", "general_coding"],
            },

            # Anthropic Models
            "claude-3-haiku": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 4096,
                "context_window": 200000,
                "cost_per_1k_input_tokens": 0.00025,
                "cost_per_1k_output_tokens": 0.00125,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["fast_completions", "simple_tasks", "cost_efficiency"],
            },
            "claude-3-sonnet": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 4096,
                "context_window": 200000,
                "cost_per_1k_input_tokens": 0.003,
                "cost_per_1k_output_tokens": 0.015,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["balanced_performance", "code_analysis", "documentation"],
            },
            "claude-3-opus": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 4096,
                "context_window": 200000,
                "cost_per_1k_input_tokens": 0.015,
                "cost_per_1k_output_tokens": 0.075,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["complex_reasoning", "code_architecture", "advanced_analysis"],
            },
            "claude-3.5-sonnet": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 8192,
                "context_window": 200000,
                "cost_per_1k_input_tokens": 0.003,
                "cost_per_1k_output_tokens": 0.015,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["coding", "reasoning", "analysis"],
            },

            # Mistral AI Models
            "codestral": {
                "provider": ModelProvider.MISTRAL,
                "max_tokens": 32768,
                "context_window": 32768,
                "cost_per_1k_input_tokens": 0.001,
                "cost_per_1k_output_tokens": 0.003,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["code_completion", "code_generation", "programming_tasks"],
            },
            "mistral-large-2": {
                "provider": ModelProvider.MISTRAL,
                "max_tokens": 8192,
                "context_window": 128000,
                "cost_per_1k_input_tokens": 0.004,
                "cost_per_1k_output_tokens": 0.012,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["complex_reasoning", "large_context", "advanced_coding"],
            },
            "mistral-small": {
                "provider": ModelProvider.MISTRAL,
                "max_tokens": 8192,
                "context_window": 32768,
                "cost_per_1k_input_tokens": 0.001,
                "cost_per_1k_output_tokens": 0.003,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["fast_completions", "simple_tasks", "cost_efficiency"],
            },

            # DeepSeek Models
            "deepseek-coder-v3": {
                "provider": ModelProvider.DEEPSEEK,
                "max_tokens": 8192,
                "context_window": 64000,
                "cost_per_1k_input_tokens": 0.00014,
                "cost_per_1k_output_tokens": 0.00028,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["code_completion", "debugging", "code_optimization"],
            },
            "deepseek-coder-v2": {
                "provider": ModelProvider.DEEPSEEK,
                "max_tokens": 4096,
                "context_window": 32000,
                "cost_per_1k_input_tokens": 0.00014,
                "cost_per_1k_output_tokens": 0.00028,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["code_generation", "programming_assistance", "refactoring"],
            },
            "deepseek-chat": {
                "provider": ModelProvider.DEEPSEEK,
                "max_tokens": 4096,
                "context_window": 32000,
                "cost_per_1k_input_tokens": 0.00014,
                "cost_per_1k_output_tokens": 0.00028,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["conversational_coding", "explanations", "learning"],
            },

            # Google AI Models
            "gemini-1.5-pro": {
                "provider": ModelProvider.GOOGLE,
                "max_tokens": 8192,
                "context_window": 2000000,  # 2M tokens
                "cost_per_1k_input_tokens": 0.00125,
                "cost_per_1k_output_tokens": 0.005,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["large_context", "document_analysis", "complex_reasoning"],
            },
            "gemini-1.5-flash": {
                "provider": ModelProvider.GOOGLE,
                "max_tokens": 8192,
                "context_window": 1000000,  # 1M tokens
                "cost_per_1k_input_tokens": 0.000075,
                "cost_per_1k_output_tokens": 0.0003,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["fast_completions", "cost_efficiency", "high_throughput"],
            },
            "codegemma-7b": {
                "provider": ModelProvider.GOOGLE,
                "max_tokens": 8192,
                "context_window": 8192,
                "cost_per_1k_input_tokens": 0.0001,
                "cost_per_1k_output_tokens": 0.0002,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["code_completion", "programming_tasks", "lightweight_coding"],
            },

            # Cohere Models
            "command-r-plus": {
                "provider": ModelProvider.COHERE,
                "max_tokens": 4096,
                "context_window": 128000,
                "cost_per_1k_input_tokens": 0.003,
                "cost_per_1k_output_tokens": 0.015,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["reasoning", "analysis", "complex_tasks"],
            },
            "command-r": {
                "provider": ModelProvider.COHERE,
                "max_tokens": 4096,
                "context_window": 128000,
                "cost_per_1k_input_tokens": 0.0005,
                "cost_per_1k_output_tokens": 0.0015,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["general_tasks", "cost_efficiency", "balanced_performance"],
            },

            # Meta/Llama Models
            "llama-3-70b-instruct": {
                "provider": ModelProvider.META,
                "max_tokens": 4096,
                "context_window": 8192,
                "cost_per_1k_input_tokens": 0.0009,
                "cost_per_1k_output_tokens": 0.0009,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["open_source", "general_coding", "instruction_following"],
            },
            "code-llama-34b": {
                "provider": ModelProvider.META,
                "max_tokens": 4096,
                "context_window": 16384,
                "cost_per_1k_input_tokens": 0.0008,
                "cost_per_1k_output_tokens": 0.0008,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["code_generation", "code_completion", "programming_assistance"],
            },

            # Qwen Models
            "qwen2.5-coder-32b": {
                "provider": ModelProvider.QWEN,
                "max_tokens": 8192,
                "context_window": 32768,
                "cost_per_1k_input_tokens": 0.0002,
                "cost_per_1k_output_tokens": 0.0006,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["code_generation", "multilingual_coding", "cost_efficiency"],
            },
            "qwen2.5-coder-7b": {
                "provider": ModelProvider.QWEN,
                "max_tokens": 8192,
                "context_window": 32768,
                "cost_per_1k_input_tokens": 0.0001,
                "cost_per_1k_output_tokens": 0.0002,
                "supports_streaming": True,
                "supports_function_calling": True,
                "best_for": ["lightweight_coding", "fast_completions", "resource_efficiency"],
            },

            # StarCoder Models
            "starcoder2-15b": {
                "provider": ModelProvider.STARCODER,
                "max_tokens": 8192,
                "context_window": 16384,
                "cost_per_1k_input_tokens": 0.0001,
                "cost_per_1k_output_tokens": 0.0002,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["code_completion", "open_source", "specialized_coding"],
            },
            "starcoder2-7b": {
                "provider": ModelProvider.STARCODER,
                "max_tokens": 8192,
                "context_window": 16384,
                "cost_per_1k_input_tokens": 0.00005,
                "cost_per_1k_output_tokens": 0.0001,
                "supports_streaming": True,
                "supports_function_calling": False,
                "best_for": ["lightweight_coding", "fast_inference", "resource_constrained"],
            },
        }
