"""
AI model service for code completion and chat functionality.
Supports multiple AI providers (OpenAI, Anthropic) with fallback mechanisms.
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, AsyncGenerator, Any, Tuple
from enum import Enum
import tiktoken
import openai
import anthropic
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.logging import get_logger
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionTier
from app.models.usage import Usage
from app.services.cache import CacheService

logger = get_logger(__name__)


class ModelProvider(str, Enum):
    """AI model providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"


class CompletionType(str, Enum):
    """Types of code completion."""
    INLINE = "inline"
    FUNCTION = "function"
    CLASS = "class"
    COMMENT = "comment"
    DOCSTRING = "docstring"
    CHAT = "chat"


class AIModelService:
    """Service for AI model interactions and code completion."""
    
    def __init__(self, db: AsyncSession, cache_service: Optional[CacheService] = None):
        self.db = db
        self.cache_service = cache_service
        
        # Initialize AI clients
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        self.anthropic_client = anthropic.AsyncAnthropic(api_key=settings.ANTHROPIC_API_KEY) if settings.ANTHROPIC_API_KEY else None
        
        # Model configurations
        self.model_configs = {
            "gpt-3.5-turbo": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 4096,
                "context_window": 16385,
                "cost_per_1k_tokens": 0.002,
            },
            "gpt-4": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 8192,
                "context_window": 8192,
                "cost_per_1k_tokens": 0.03,
            },
            "gpt-4-turbo": {
                "provider": ModelProvider.OPENAI,
                "max_tokens": 4096,
                "context_window": 128000,
                "cost_per_1k_tokens": 0.01,
            },
            "claude-3-haiku": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 4096,
                "context_window": 200000,
                "cost_per_1k_tokens": 0.00025,
            },
            "claude-3-sonnet": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 4096,
                "context_window": 200000,
                "cost_per_1k_tokens": 0.003,
            },
            "claude-3-opus": {
                "provider": ModelProvider.ANTHROPIC,
                "max_tokens": 4096,
                "context_window": 200000,
                "cost_per_1k_tokens": 0.015,
            },
        }
    
    async def get_available_models(self, user: User) -> List[str]:
        """Get available models based on user's subscription tier."""
        try:
            # Get user's subscription
            stmt = select(Subscription).where(
                Subscription.user_id == user.id,
                Subscription.is_deleted == False
            )
            result = await self.db.execute(stmt)
            subscription = result.scalar_one_or_none()
            
            if not subscription:
                return settings.FREE_TIER_MODELS
            
            # Return models based on tier
            tier_models = {
                SubscriptionTier.FREE: settings.FREE_TIER_MODELS,
                SubscriptionTier.SOLO: settings.SOLO_TIER_MODELS,
                SubscriptionTier.TEAM: settings.TEAM_TIER_MODELS,
                SubscriptionTier.ENTERPRISE: settings.ENTERPRISE_TIER_MODELS,
            }
            
            return tier_models.get(subscription.tier, settings.FREE_TIER_MODELS)
            
        except Exception as e:
            logger.error("Failed to get available models", error=str(e), user_id=str(user.id))
            return settings.FREE_TIER_MODELS
    
    async def create_completion(
        self,
        user: User,
        prompt: str,
        language: str = "python",
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        completion_type: CompletionType = CompletionType.INLINE,
        context: Optional[Dict[str, Any]] = None,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        Create a code completion using the specified AI model.
        
        Args:
            user: User making the request
            prompt: Code prompt for completion
            language: Programming language
            model: AI model to use (defaults to user's tier default)
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            completion_type: Type of completion
            context: Additional context (file path, surrounding code, etc.)
            stream: Whether to stream the response
            
        Returns:
            Completion response with generated code
        """
        start_time = time.time()
        
        try:
            # Validate model availability
            available_models = await self.get_available_models(user)
            if model and model not in available_models:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            elif not model:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            
            # Set defaults
            max_tokens = max_tokens or settings.MAX_TOKENS_DEFAULT
            temperature = temperature or settings.TEMPERATURE_DEFAULT
            
            # Check cache first
            cache_key = self._generate_cache_key(prompt, language, model, max_tokens, temperature)
            if self.cache_service and not stream:
                cached_result = await self.cache_service.get(cache_key)
                if cached_result:
                    logger.info("Cache hit for completion", user_id=str(user.id), model=model)
                    return json.loads(cached_result)
            
            # Prepare context-aware prompt
            enhanced_prompt = self._enhance_prompt_with_context(prompt, language, context, completion_type)
            
            # Get model configuration
            model_config = self.model_configs.get(model, self.model_configs[settings.DEFAULT_MODEL])
            
            # Generate completion based on provider
            if model_config["provider"] == ModelProvider.OPENAI:
                result = await self._create_openai_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            elif model_config["provider"] == ModelProvider.ANTHROPIC:
                result = await self._create_anthropic_completion(
                    enhanced_prompt, model, max_tokens, temperature, stream
                )
            else:
                raise ValueError(f"Unsupported model provider: {model_config['provider']}")
            
            # Calculate metrics
            response_time_ms = int((time.time() - start_time) * 1000)
            
            # Estimate token usage and cost
            input_tokens = self._estimate_tokens(enhanced_prompt, model)
            output_tokens = self._estimate_tokens(result.get("text", ""), model)
            cost_cents = self._calculate_cost(input_tokens, output_tokens, model_config)
            
            # Add metadata to result
            result.update({
                "model": model,
                "language": language,
                "completion_type": completion_type,
                "response_time_ms": response_time_ms,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": input_tokens + output_tokens,
                "cost_cents": cost_cents,
            })
            
            # Cache the result
            if self.cache_service and not stream:
                await self.cache_service.set(
                    cache_key, 
                    json.dumps(result), 
                    expire=3600  # Cache for 1 hour
                )
            
            # Track usage
            await self._track_usage(
                user=user,
                model=model,
                completion_type=completion_type,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                cost_cents=cost_cents,
                response_time_ms=response_time_ms,
                language=language,
            )
            
            logger.info("Completion generated successfully", 
                       user_id=str(user.id),
                       model=model,
                       tokens=input_tokens + output_tokens,
                       response_time_ms=response_time_ms)
            
            return result
            
        except Exception as e:
            logger.error("Failed to create completion", 
                        error=str(e), 
                        user_id=str(user.id),
                        model=model)
            
            # Try fallback model
            if model != settings.DEFAULT_MODEL:
                logger.info("Attempting fallback to default model", 
                           original_model=model,
                           fallback_model=settings.DEFAULT_MODEL)
                return await self.create_completion(
                    user=user,
                    prompt=prompt,
                    language=language,
                    model=settings.DEFAULT_MODEL,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    completion_type=completion_type,
                    context=context,
                    stream=stream,
                )
            
            raise
    
    async def create_streaming_completion(
        self,
        user: User,
        prompt: str,
        language: str = "python",
        model: Optional[str] = None,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        completion_type: CompletionType = CompletionType.INLINE,
        context: Optional[Dict[str, Any]] = None,
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Create a streaming code completion."""
        try:
            # Validate model availability
            available_models = await self.get_available_models(user)
            if model and model not in available_models:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            elif not model:
                model = available_models[0] if available_models else settings.DEFAULT_MODEL
            
            # Set defaults
            max_tokens = max_tokens or settings.MAX_TOKENS_DEFAULT
            temperature = temperature or settings.TEMPERATURE_DEFAULT
            
            # Prepare context-aware prompt
            enhanced_prompt = self._enhance_prompt_with_context(prompt, language, context, completion_type)
            
            # Get model configuration
            model_config = self.model_configs.get(model, self.model_configs[settings.DEFAULT_MODEL])
            
            # Stream completion based on provider
            if model_config["provider"] == ModelProvider.OPENAI:
                async for chunk in self._stream_openai_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            elif model_config["provider"] == ModelProvider.ANTHROPIC:
                async for chunk in self._stream_anthropic_completion(
                    enhanced_prompt, model, max_tokens, temperature
                ):
                    yield chunk
            else:
                raise ValueError(f"Unsupported model provider: {model_config['provider']}")
                
        except Exception as e:
            logger.error("Failed to create streaming completion", 
                        error=str(e), 
                        user_id=str(user.id),
                        model=model)
            yield {"error": str(e), "type": "completion_error"}
    
    def _enhance_prompt_with_context(
        self, 
        prompt: str, 
        language: str, 
        context: Optional[Dict[str, Any]], 
        completion_type: CompletionType
    ) -> str:
        """Enhance the prompt with context and language-specific instructions."""
        
        # Base system prompt based on completion type
        system_prompts = {
            CompletionType.INLINE: f"Complete the following {language} code. Provide only the completion without explanations:",
            CompletionType.FUNCTION: f"Generate a complete {language} function based on the following prompt:",
            CompletionType.CLASS: f"Generate a complete {language} class based on the following prompt:",
            CompletionType.COMMENT: f"Generate appropriate comments for the following {language} code:",
            CompletionType.DOCSTRING: f"Generate a comprehensive docstring for the following {language} code:",
            CompletionType.CHAT: f"You are an expert {language} developer. Help with the following question:",
        }
        
        enhanced_prompt = system_prompts.get(completion_type, system_prompts[CompletionType.INLINE])
        enhanced_prompt += f"\n\nLanguage: {language}\n"
        
        # Add context if available
        if context:
            if context.get("file_path"):
                enhanced_prompt += f"File: {context['file_path']}\n"
            if context.get("surrounding_code"):
                enhanced_prompt += f"Surrounding code:\n{context['surrounding_code']}\n"
            if context.get("project_info"):
                enhanced_prompt += f"Project context: {context['project_info']}\n"
        
        enhanced_prompt += f"\nCode to complete:\n{prompt}"
        
        return enhanced_prompt

    async def _create_openai_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using OpenAI API."""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        try:
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                stream=stream,
            )

            if stream:
                return response

            return {
                "text": response.choices[0].message.content,
                "finish_reason": response.choices[0].finish_reason,
                "provider": ModelProvider.OPENAI,
            }

        except Exception as e:
            logger.error("OpenAI completion failed", error=str(e), model=model)
            raise

    async def _create_anthropic_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Anthropic API."""
        if not self.anthropic_client:
            raise ValueError("Anthropic client not configured")

        try:
            response = await self.anthropic_client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}],
                stream=stream,
            )

            if stream:
                return response

            return {
                "text": response.content[0].text,
                "finish_reason": "stop",
                "provider": ModelProvider.ANTHROPIC,
            }

        except Exception as e:
            logger.error("Anthropic completion failed", error=str(e), model=model)
            raise

    async def _stream_openai_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using OpenAI API."""
        if not self.openai_client:
            raise ValueError("OpenAI client not configured")

        try:
            stream = await self.openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True,
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield {
                        "text": chunk.choices[0].delta.content,
                        "finish_reason": chunk.choices[0].finish_reason,
                        "provider": ModelProvider.OPENAI,
                    }

        except Exception as e:
            logger.error("OpenAI streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}

    async def _stream_anthropic_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Anthropic API."""
        if not self.anthropic_client:
            raise ValueError("Anthropic client not configured")

        try:
            async with self.anthropic_client.messages.stream(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}],
            ) as stream:
                async for text in stream.text_stream:
                    yield {
                        "text": text,
                        "finish_reason": None,
                        "provider": ModelProvider.ANTHROPIC,
                    }

        except Exception as e:
            logger.error("Anthropic streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}

    def _estimate_tokens(self, text: str, model: str) -> int:
        """Estimate token count for text using tiktoken."""
        try:
            # Map models to tiktoken encodings
            encoding_map = {
                "gpt-3.5-turbo": "cl100k_base",
                "gpt-4": "cl100k_base",
                "gpt-4-turbo": "cl100k_base",
            }

            encoding_name = encoding_map.get(model, "cl100k_base")
            encoding = tiktoken.get_encoding(encoding_name)
            return len(encoding.encode(text))

        except Exception:
            # Fallback estimation: ~4 characters per token
            return len(text) // 4

    def _calculate_cost(self, input_tokens: int, output_tokens: int, model_config: Dict[str, Any]) -> int:
        """Calculate cost in cents for token usage."""
        cost_per_1k = model_config.get("cost_per_1k_tokens", 0.002)
        total_tokens = input_tokens + output_tokens
        return int((total_tokens / 1000) * cost_per_1k * 100)  # Convert to cents

    def _generate_cache_key(
        self,
        prompt: str,
        language: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> str:
        """Generate cache key for completion request."""
        import hashlib

        key_data = f"{prompt}:{language}:{model}:{max_tokens}:{temperature}"
        return f"completion:{hashlib.md5(key_data.encode()).hexdigest()}"

    async def _track_usage(
        self,
        user: User,
        model: str,
        completion_type: CompletionType,
        input_tokens: int,
        output_tokens: int,
        cost_cents: int,
        response_time_ms: int,
        language: str,
    ) -> None:
        """Track usage for analytics and billing."""
        try:
            from datetime import datetime

            usage = Usage(
                user_id=user.id,
                endpoint="/v1/completions",
                method="POST",
                timestamp=datetime.utcnow(),
                response_time_ms=response_time_ms,
                status_code=200,
                model_used=model,
                tokens_input=input_tokens,
                tokens_output=output_tokens,
                completion_type=completion_type.value,
                cost_cents=cost_cents,
                metadata=json.dumps({
                    "language": language,
                    "model": model,
                    "completion_type": completion_type.value,
                })
            )

            self.db.add(usage)
            await self.db.commit()

        except Exception as e:
            logger.error("Failed to track usage", error=str(e), user_id=str(user.id))
