import * as vscode from 'vscode';
import { AuthenticationManager } from '../auth/authManager';
import { ConfigurationManager } from '../config/configManager';
import { TelemetryManager } from '../telemetry/telemetryManager';
import { TDSCoderChatProvider } from './chatProvider';

export class TDSCoderViewProvider implements vscode.WebviewViewProvider {
    private view?: vscode.WebviewView;
    private chatProvider: TDSCoderChatProvider;

    constructor(
        private context: vscode.ExtensionContext,
        private authManager: AuthenticationManager,
        private configManager: ConfigurationManager,
        private telemetryManager: TelemetryManager
    ) {
        this.chatProvider = new TDSCoderChatProvider(
            authManager,
            configManager,
            telemetryManager
        );
    }

    resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        token: vscode.CancellationToken
    ): void {
        this.view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };

        webviewView.webview.html = this.getWebviewContent();

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(async (message) => {
            switch (message.type) {
                case 'sendMessage':
                    await this.handleChatMessage(message.text);
                    break;
                case 'authenticate':
                    await this.authManager.authenticate();
                    this.updateAuthStatus();
                    break;
                case 'getAuthStatus':
                    this.updateAuthStatus();
                    break;
            }
        });

        // Update auth status on load
        this.updateAuthStatus();
    }

    private async handleChatMessage(message: string): Promise<void> {
        if (!this.view) {
            return;
        }

        try {
            // Show loading state
            this.view.webview.postMessage({
                type: 'messageStatus',
                status: 'loading'
            });

            const response = await this.chatProvider.sendChatMessage(message);

            // Send response
            this.view.webview.postMessage({
                type: 'chatResponse',
                message: response
            });

        } catch (error) {
            this.view.webview.postMessage({
                type: 'chatError',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    private updateAuthStatus(): void {
        if (!this.view) {
            return;
        }

        const isAuthenticated = this.authManager.isAuthenticated();
        const userInfo = this.authManager.getUserInfo();

        this.view.webview.postMessage({
            type: 'authStatus',
            isAuthenticated,
            userInfo
        });
    }

    private getWebviewContent(): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>TDS Coder</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 10px;
                }
                .container {
                    display: flex;
                    flex-direction: column;
                    height: 100vh;
                }
                .auth-section {
                    padding: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                    margin-bottom: 10px;
                }
                .chat-container {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }
                .messages {
                    flex: 1;
                    overflow-y: auto;
                    padding: 10px;
                    border: 1px solid var(--vscode-panel-border);
                    margin-bottom: 10px;
                }
                .message {
                    margin-bottom: 10px;
                    padding: 8px;
                    border-radius: 4px;
                }
                .user-message {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    text-align: right;
                }
                .assistant-message {
                    background-color: var(--vscode-editor-selectionBackground);
                    border-left: 3px solid var(--vscode-button-background);
                }
                .input-container {
                    display: flex;
                    gap: 5px;
                }
                .message-input {
                    flex: 1;
                    padding: 8px;
                    border: 1px solid var(--vscode-input-border);
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border-radius: 2px;
                }
                .send-button, .auth-button {
                    padding: 8px 12px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 2px;
                    cursor: pointer;
                }
                .send-button:hover, .auth-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .send-button:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                .loading {
                    opacity: 0.7;
                    font-style: italic;
                }
                .error {
                    color: var(--vscode-errorForeground);
                    background-color: var(--vscode-inputValidation-errorBackground);
                    border: 1px solid var(--vscode-inputValidation-errorBorder);
                    padding: 8px;
                    border-radius: 2px;
                    margin-bottom: 10px;
                }
                .user-info {
                    font-size: 0.9em;
                    color: var(--vscode-descriptionForeground);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="auth-section">
                    <div id="authStatus">Checking authentication...</div>
                    <button id="authButton" class="auth-button" style="display: none;">Authenticate</button>
                </div>
                
                <div class="chat-container" id="chatContainer" style="display: none;">
                    <div class="messages" id="messages">
                        <div class="assistant-message">
                            Hello! I'm TDS Coder, your AI coding assistant. Ask me anything about programming, code review, debugging, or get help with your code.
                        </div>
                    </div>
                    
                    <div class="input-container">
                        <input type="text" id="messageInput" class="message-input" placeholder="Ask me anything about coding..." />
                        <button id="sendButton" class="send-button">Send</button>
                    </div>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                const authStatus = document.getElementById('authStatus');
                const authButton = document.getElementById('authButton');
                const chatContainer = document.getElementById('chatContainer');
                const messages = document.getElementById('messages');
                const messageInput = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');

                // Request auth status on load
                vscode.postMessage({ type: 'getAuthStatus' });

                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    
                    switch (message.type) {
                        case 'authStatus':
                            updateAuthStatus(message.isAuthenticated, message.userInfo);
                            break;
                        case 'chatResponse':
                            addMessage(message.message, 'assistant');
                            setSendButtonState(false);
                            break;
                        case 'chatError':
                            showError(message.error);
                            setSendButtonState(false);
                            break;
                        case 'messageStatus':
                            if (message.status === 'loading') {
                                setSendButtonState(true);
                            }
                            break;
                    }
                });

                function updateAuthStatus(isAuthenticated, userInfo) {
                    if (isAuthenticated) {
                        authStatus.textContent = userInfo ? 
                            \`Authenticated as \${userInfo.email || userInfo.full_name || 'User'}\` : 
                            'Authenticated';
                        authButton.style.display = 'none';
                        chatContainer.style.display = 'flex';
                    } else {
                        authStatus.textContent = 'Not authenticated';
                        authButton.style.display = 'block';
                        chatContainer.style.display = 'none';
                    }
                }

                function addMessage(text, sender) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = \`message \${sender}-message\`;
                    messageDiv.textContent = text;
                    messages.appendChild(messageDiv);
                    messages.scrollTop = messages.scrollHeight;
                }

                function showError(error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error';
                    errorDiv.textContent = \`Error: \${error}\`;
                    messages.appendChild(errorDiv);
                    messages.scrollTop = messages.scrollHeight;
                }

                function setSendButtonState(loading) {
                    sendButton.disabled = loading;
                    sendButton.textContent = loading ? 'Sending...' : 'Send';
                }

                function sendMessage() {
                    const text = messageInput.value.trim();
                    if (!text) return;

                    addMessage(text, 'user');
                    messageInput.value = '';
                    
                    vscode.postMessage({
                        type: 'sendMessage',
                        text: text
                    });
                }

                // Event listeners
                authButton.addEventListener('click', () => {
                    vscode.postMessage({ type: 'authenticate' });
                });

                sendButton.addEventListener('click', sendMessage);

                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            </script>
        </body>
        </html>
        `;
    }
}
