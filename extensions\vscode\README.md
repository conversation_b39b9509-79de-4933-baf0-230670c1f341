# TDS Coder - VS Code Extension

AI-powered code completion and assistance for developers. Get intelligent code suggestions, chat with AI about your code, and boost your productivity with advanced AI models.

## Features

### 🚀 Intelligent Code Completion
- **Real-time suggestions**: Get AI-powered code completions as you type
- **Context-aware**: Understands your project structure and coding patterns
- **Multi-language support**: Works with 50+ programming languages
- **Multiple AI models**: Choose from GPT-4, <PERSON>, and more based on your subscription

### 💬 AI Chat Assistant
- **Code explanations**: Ask questions about your code
- **Debugging help**: Get assistance with errors and bugs
- **Code review**: Get suggestions for improving your code
- **Learning support**: Learn new concepts and best practices

### ⚡ Performance & Reliability
- **Offline mode**: Cached completions work without internet
- **Fast responses**: Sub-100ms completion times
- **Smart caching**: Reduces API calls and improves speed
- **Rate limiting**: Respects your subscription limits

### 🔒 Security & Privacy
- **Secure authentication**: API keys stored securely in VS Code
- **No data retention**: Your code stays private
- **Configurable**: Control what data is sent to the service

## Installation

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "TDS Coder"
4. Click Install
5. Reload VS Code

## Setup

1. **Get an API Key**:
   - Visit [TDS Coder Dashboard](https://tdscoder.com/dashboard)
   - Create an account or sign in
   - Generate a new API key

2. **Configure the Extension**:
   - Open VS Code settings (Ctrl+,)
   - Search for "TDS Coder"
   - Enter your API key
   - Configure other preferences

3. **Start Coding**:
   - Open any code file
   - Start typing and see AI suggestions appear
   - Use the chat panel for questions and help

## Configuration

### Basic Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `tdsCoder.apiKey` | Your TDS Coder API key | "" |
| `tdsCoder.apiUrl` | TDS Coder API URL | "https://api.tdscoder.com" |
| `tdsCoder.model` | AI model to use | "auto" |
| `tdsCoder.enableInlineCompletion` | Enable inline code completion | true |
| `tdsCoder.enableChat` | Enable chat assistance | true |

### Advanced Settings

| Setting | Description | Default |
|---------|-------------|---------|
| `tdsCoder.maxTokens` | Maximum tokens for completion | 150 |
| `tdsCoder.temperature` | Sampling temperature (0-2) | 0.2 |
| `tdsCoder.completionDelay` | Delay before triggering completion (ms) | 300 |
| `tdsCoder.enableOfflineMode` | Enable offline mode with caching | false |
| `tdsCoder.cacheSize` | Maximum cached completions | 1000 |
| `tdsCoder.enableTelemetry` | Enable usage telemetry | true |

## Commands

Access these commands via the Command Palette (Ctrl+Shift+P):

- `TDS Coder: Authenticate` - Set up your API key
- `TDS Coder: Logout` - Clear authentication
- `TDS Coder: Open Settings` - Open extension settings
- `TDS Coder: Toggle Inline Completion` - Enable/disable completions
- `TDS Coder: Show Usage Statistics` - View your usage stats
- `TDS Coder: Clear Cache` - Clear cached completions

## Keyboard Shortcuts

- `Ctrl+Alt+T` (Cmd+Alt+T on Mac) - Toggle inline completion

## Supported Languages

TDS Coder works with all major programming languages including:

- **Web**: JavaScript, TypeScript, HTML, CSS, React, Vue, Angular
- **Backend**: Python, Java, C#, Go, Rust, PHP, Ruby
- **Mobile**: Swift, Kotlin, Dart (Flutter)
- **Data**: SQL, R, MATLAB, Jupyter Notebooks
- **DevOps**: Bash, PowerShell, Dockerfile, YAML
- **And many more...**

## Subscription Plans

### Free Tier
- 100 completions per day
- Basic AI models
- Community support

### Solo Plan ($19/month)
- 10,000 completions per day
- Advanced AI models (GPT-4, Claude)
- Priority support
- Offline mode

### Team Plan ($49/month)
- 50,000 completions per day
- Team management features
- Shared snippets
- Advanced analytics

### Enterprise
- Unlimited completions
- On-premise deployment
- Custom models
- Dedicated support

## Troubleshooting

### Common Issues

**Completions not working?**
- Check your API key in settings
- Verify your internet connection
- Check your subscription quota
- Try reloading VS Code

**Slow completions?**
- Reduce completion delay in settings
- Enable offline mode for caching
- Check your internet speed
- Try a different AI model

**Authentication errors?**
- Verify your API key is correct
- Check if your subscription is active
- Try logging out and back in
- Contact support if issues persist

### Getting Help

- **Documentation**: [docs.tdscoder.com](https://docs.tdscoder.com)
- **Support**: [<EMAIL>](mailto:<EMAIL>)
- **Discord**: [discord.gg/tdscoder](https://discord.gg/tdscoder)
- **GitHub**: [github.com/tdscoder/vscode-extension](https://github.com/tdscoder/vscode-extension)

## Privacy & Security

- Your code is never stored on our servers
- API keys are encrypted and stored securely
- All communication uses HTTPS encryption
- Telemetry can be disabled in settings
- We're SOC 2 Type II compliant

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

This extension is licensed under the [MIT License](LICENSE).

---

**Happy Coding with TDS Coder! 🚀**
