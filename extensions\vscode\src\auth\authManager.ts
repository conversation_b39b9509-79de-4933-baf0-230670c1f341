import * as vscode from 'vscode';
import axios from 'axios';
import { ConfigurationManager } from '../config/configManager';

export class AuthenticationManager {
    private static readonly API_KEY_SECRET_KEY = 'tdsCoder.apiKey';
    private static readonly USER_INFO_KEY = 'tdsCoder.userInfo';
    
    private apiKey: string | undefined;
    private userInfo: any;

    constructor(
        private context: vscode.ExtensionContext,
        private configManager: ConfigurationManager
    ) {
        this.loadStoredCredentials();
    }

    async authenticate(): Promise<boolean> {
        try {
            // First, try to get API key from configuration
            let apiKey = this.configManager.getApiKey();
            
            if (!apiKey) {
                // Prompt user for API key
                apiKey = await vscode.window.showInputBox({
                    prompt: 'Enter your TDS Coder API key',
                    password: true,
                    placeHolder: 'tds_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                    validateInput: (value) => {
                        if (!value) {
                            return 'API key is required';
                        }
                        if (!value.startsWith('tds_')) {
                            return 'API key should start with "tds_"';
                        }
                        if (value.length < 40) {
                            return 'API key appears to be too short';
                        }
                        return null;
                    }
                });

                if (!apiKey) {
                    return false;
                }
            }

            // Validate API key with server
            const isValid = await this.validateApiKey(apiKey);
            
            if (isValid) {
                this.apiKey = apiKey;
                await this.storeCredentials();
                
                // Update configuration if it was entered manually
                if (!this.configManager.getApiKey()) {
                    await this.configManager.updateSetting('apiKey', apiKey);
                }
                
                vscode.window.showInformationMessage('Successfully authenticated with TDS Coder!');
                return true;
            } else {
                vscode.window.showErrorMessage('Invalid API key. Please check your credentials.');
                return false;
            }

        } catch (error) {
            console.error('Authentication error:', error);
            vscode.window.showErrorMessage('Authentication failed. Please try again.');
            return false;
        }
    }

    async logout(): Promise<void> {
        this.apiKey = undefined;
        this.userInfo = undefined;
        
        // Clear stored credentials
        await this.context.secrets.delete(AuthenticationManager.API_KEY_SECRET_KEY);
        await this.context.globalState.update(AuthenticationManager.USER_INFO_KEY, undefined);
        
        // Clear configuration
        await this.configManager.updateSetting('apiKey', '');
    }

    isAuthenticated(): boolean {
        return !!this.apiKey;
    }

    getApiKey(): string | undefined {
        return this.apiKey;
    }

    getUserInfo(): any {
        return this.userInfo;
    }

    clearAuthentication(): void {
        this.apiKey = undefined;
        this.userInfo = undefined;
    }

    private async validateApiKey(apiKey: string): Promise<boolean> {
        try {
            const apiUrl = this.configManager.getApiUrl();
            
            const response = await axios.get(`${apiUrl}/api/v1/v1/models`, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.status === 200) {
                // Also fetch user info
                try {
                    const userResponse = await axios.get(`${apiUrl}/api/v1/users/me`, {
                        headers: {
                            'Authorization': `Bearer ${apiKey}`,
                            'Content-Type': 'application/json'
                        },
                        timeout: 5000
                    });
                    
                    if (userResponse.status === 200) {
                        this.userInfo = userResponse.data;
                    }
                } catch (userError) {
                    console.warn('Failed to fetch user info:', userError);
                    // Don't fail authentication if user info fetch fails
                }
                
                return true;
            }

            return false;

        } catch (error) {
            console.error('API key validation error:', error);
            
            if (axios.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    return false; // Invalid API key
                } else if (error.response?.status === 403) {
                    vscode.window.showErrorMessage('TDS Coder: Access forbidden. Please check your subscription.');
                    return false;
                } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
                    vscode.window.showErrorMessage('TDS Coder: Cannot connect to server. Please check your internet connection.');
                    return false;
                }
            }
            
            throw error;
        }
    }

    private async loadStoredCredentials(): Promise<void> {
        try {
            // Load API key from secrets
            this.apiKey = await this.context.secrets.get(AuthenticationManager.API_KEY_SECRET_KEY);
            
            // If not in secrets, try configuration
            if (!this.apiKey) {
                this.apiKey = this.configManager.getApiKey();
            }
            
            // Load user info from global state
            this.userInfo = this.context.globalState.get(AuthenticationManager.USER_INFO_KEY);
            
            // Validate stored API key
            if (this.apiKey) {
                const isValid = await this.validateApiKey(this.apiKey);
                if (!isValid) {
                    console.warn('Stored API key is invalid, clearing credentials');
                    await this.logout();
                }
            }

        } catch (error) {
            console.error('Failed to load stored credentials:', error);
            // Clear potentially corrupted credentials
            await this.logout();
        }
    }

    private async storeCredentials(): Promise<void> {
        try {
            if (this.apiKey) {
                // Store API key in secrets (encrypted)
                await this.context.secrets.store(AuthenticationManager.API_KEY_SECRET_KEY, this.apiKey);
            }
            
            if (this.userInfo) {
                // Store user info in global state
                await this.context.globalState.update(AuthenticationManager.USER_INFO_KEY, this.userInfo);
            }

        } catch (error) {
            console.error('Failed to store credentials:', error);
            vscode.window.showWarningMessage('Failed to securely store credentials. You may need to re-authenticate after restarting VS Code.');
        }
    }

    async refreshUserInfo(): Promise<void> {
        if (!this.apiKey) {
            return;
        }

        try {
            const apiUrl = this.configManager.getApiUrl();
            const response = await axios.get(`${apiUrl}/api/v1/users/me`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            
            if (response.status === 200) {
                this.userInfo = response.data;
                await this.context.globalState.update(AuthenticationManager.USER_INFO_KEY, this.userInfo);
            }

        } catch (error) {
            console.error('Failed to refresh user info:', error);
        }
    }

    async checkQuotaStatus(): Promise<{ hasQuota: boolean; usage?: any }> {
        if (!this.apiKey) {
            return { hasQuota: false };
        }

        try {
            const apiUrl = this.configManager.getApiUrl();
            const response = await axios.get(`${apiUrl}/api/v1/usage/quota`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });
            
            if (response.status === 200) {
                const usage = response.data;
                return {
                    hasQuota: !usage.quota_exceeded,
                    usage
                };
            }

            return { hasQuota: true };

        } catch (error) {
            console.error('Failed to check quota status:', error);
            return { hasQuota: true }; // Assume quota is available on error
        }
    }
}
