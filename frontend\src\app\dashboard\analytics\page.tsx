'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'
import { 
  ChartBarIcon, 
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  DocumentArrowDownIcon,
  CalendarIcon,
  ClockIcon,
  CpuChipIcon,
  CurrencyDollarIcon,
  CodeBracketIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@/lib/api'
import toast from 'react-hot-toast'

interface ProductivityMetrics {
  period: {
    start_date: string
    end_date: string
    days: number
  }
  completion_metrics: {
    total_completions: number
    successful_completions: number
    failed_completions: number
    acceptance_rate: number
    error_rate: number
  }
  token_metrics: {
    total_tokens_input: number
    total_tokens_output: number
    total_tokens: number
    avg_tokens_per_completion: number
  }
  performance_metrics: {
    avg_response_time_ms: number
    estimated_time_saved_seconds: number
    estimated_time_saved_hours: number
  }
  cost_metrics: {
    total_cost_cents: number
    total_cost_dollars: number
    avg_cost_per_completion: number
  }
  language_breakdown: Record<string, number>
  model_usage: Record<string, number>
  completion_types: Record<string, number>
  daily_metrics: Array<{
    date: string
    completions: number
    successful_completions: number
    tokens: number
    cost_cents: number
  }>
  trends: {
    completions_change: number
    acceptance_rate_change: number
    tokens_change: number
    cost_change: number
  }
}

export default function AnalyticsPage() {
  const { user } = useAuth()
  const [metrics, setMetrics] = useState<ProductivityMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState({
    start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end_date: new Date().toISOString().split('T')[0] // today
  })

  useEffect(() => {
    fetchMetrics()
  }, [dateRange])

  const fetchMetrics = async () => {
    try {
      setLoading(true)
      const data = await apiHelpers.analytics.getUserProductivity(
        dateRange.start_date,
        dateRange.end_date
      )
      setMetrics(data)
    } catch (error) {
      console.error('Failed to fetch metrics:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const exportReport = async (format: 'pdf' | 'csv') => {
    try {
      const blob = await apiHelpers.analytics.exportUserReport(
        dateRange.start_date,
        dateRange.end_date,
        format
      )
      
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `tds_coder_report_${dateRange.start_date}_${dateRange.end_date}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast.success(`Report exported as ${format.toUpperCase()}`)
    } catch (error) {
      console.error('Failed to export report:', error)
      toast.error('Failed to export report')
    }
  }

  const formatTrend = (value: number) => {
    const isPositive = value >= 0
    const Icon = isPositive ? ArrowTrendingUpIcon : ArrowTrendingDownIcon
    const colorClass = isPositive ? 'text-green-600' : 'text-red-600'
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="h-4 w-4 mr-1" />
        <span>{Math.abs(value).toFixed(1)}%</span>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="p-8">
        <div className="text-center py-12">
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No analytics data
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Start using TDS Coder to see your productivity metrics.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Track your coding productivity and AI assistance usage
            </p>
          </div>
          
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-4">
            {/* Date Range Selector */}
            <div className="flex gap-2">
              <input
                type="date"
                value={dateRange.start_date}
                onChange={(e) => setDateRange(prev => ({ ...prev, start_date: e.target.value }))}
                className="form-input text-sm"
              />
              <input
                type="date"
                value={dateRange.end_date}
                onChange={(e) => setDateRange(prev => ({ ...prev, end_date: e.target.value }))}
                className="form-input text-sm"
              />
            </div>
            
            {/* Export Buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => exportReport('pdf')}
                className="btn-secondary px-3 py-2 text-sm rounded-md flex items-center"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                PDF
              </button>
              <button
                onClick={() => exportReport('csv')}
                className="btn-secondary px-3 py-2 text-sm rounded-md flex items-center"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                CSV
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Completions
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {metrics.completion_metrics.total_completions.toLocaleString()}
                </p>
                {formatTrend(metrics.trends.completions_change)}
              </div>
              <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                <SparklesIcon className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Acceptance Rate
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {metrics.completion_metrics.acceptance_rate.toFixed(1)}%
                </p>
                {formatTrend(metrics.trends.acceptance_rate_change)}
              </div>
              <div className="h-12 w-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <ChartBarIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Time Saved
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {metrics.performance_metrics.estimated_time_saved_hours.toFixed(1)}h
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Estimated
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <ClockIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Cost
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ${metrics.cost_metrics.total_cost_dollars.toFixed(2)}
                </p>
                {formatTrend(metrics.trends.cost_change)}
              </div>
              <div className="h-12 w-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Breakdowns */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Language Breakdown */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <CodeBracketIcon className="h-5 w-5 mr-2" />
              Language Usage
            </h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {Object.entries(metrics.language_breakdown)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([language, count]) => {
                  const percentage = (count / metrics.completion_metrics.total_completions * 100)
                  return (
                    <div key={language} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                        {language}
                      </span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-indigo-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400 w-12 text-right">
                          {count}
                        </span>
                      </div>
                    </div>
                  )
                })}
            </div>
          </div>
        </div>

        {/* Model Usage */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <CpuChipIcon className="h-5 w-5 mr-2" />
              Model Usage
            </h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {Object.entries(metrics.model_usage)
                .sort(([,a], [,b]) => b - a)
                .map(([model, count]) => {
                  const percentage = (count / metrics.completion_metrics.total_completions * 100)
                  return (
                    <div key={model} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {model}
                      </span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-green-600 h-2 rounded-full" 
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400 w-12 text-right">
                          {count}
                        </span>
                      </div>
                    </div>
                  )
                })}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Token Usage
            </h3>
          </div>
          <div className="card-content">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Input Tokens:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {metrics.token_metrics.total_tokens_input.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Output Tokens:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {metrics.token_metrics.total_tokens_output.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-sm font-medium text-gray-900 dark:text-white">Total:</span>
                <span className="text-sm font-bold text-gray-900 dark:text-white">
                  {metrics.token_metrics.total_tokens.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Performance
            </h3>
          </div>
          <div className="card-content">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Avg Response:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {metrics.performance_metrics.avg_response_time_ms.toFixed(0)}ms
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Success Rate:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {metrics.completion_metrics.acceptance_rate.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Error Rate:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {metrics.completion_metrics.error_rate.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Cost Analysis
            </h3>
          </div>
          <div className="card-content">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Total Cost:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  ${metrics.cost_metrics.total_cost_dollars.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Avg per Completion:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  ${(metrics.cost_metrics.avg_cost_per_completion / 100).toFixed(3)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Daily Average:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  ${(metrics.cost_metrics.total_cost_dollars / metrics.period.days).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
