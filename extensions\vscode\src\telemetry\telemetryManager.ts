import * as vscode from 'vscode';
import { ConfigurationManager } from '../config/configManager';

interface TelemetryEvent {
    event: string;
    properties: any;
    timestamp: number;
}

interface UsageStats {
    completions: number;
    chatMessages: number;
    tokensUsed: number;
    errorsCount: number;
    lastUsed: number;
}

export class TelemetryManager {
    private events: TelemetryEvent[] = [];
    private usageStats: UsageStats = {
        completions: 0,
        chatMessages: 0,
        tokensUsed: 0,
        errorsCount: 0,
        lastUsed: 0
    };

    constructor(private configManager: ConfigurationManager) {}

    trackEvent(event: string, properties: any = {}): void {
        if (!this.configManager.getEnableTelemetry()) {
            return;
        }

        const telemetryEvent: TelemetryEvent = {
            event,
            properties: {
                ...properties,
                timestamp: Date.now(),
                vscodeVersion: vscode.version,
                extensionVersion: vscode.extensions.getExtension('tdscoder.tds-coder')?.packageJSON.version
            },
            timestamp: Date.now()
        };

        this.events.push(telemetryEvent);

        // Update usage stats
        this.updateUsageStats(event, properties);

        // Log if debugging is enabled
        if (this.configManager.getEnableLogging()) {
            console.log('TDS Coder Telemetry:', telemetryEvent);
        }

        // Keep only last 1000 events
        if (this.events.length > 1000) {
            this.events = this.events.slice(-1000);
        }
    }

    trackTextChange(event: vscode.TextDocumentChangeEvent): void {
        if (!this.configManager.getEnableTelemetry()) {
            return;
        }

        // Track basic text change metrics
        const changeCount = event.contentChanges.length;
        const totalCharsChanged = event.contentChanges.reduce(
            (total, change) => total + change.text.length,
            0
        );

        this.trackEvent('text_changed', {
            language: event.document.languageId,
            changeCount,
            totalCharsChanged,
            documentLength: event.document.getText().length
        });
    }

    getUsageStats(): UsageStats {
        return { ...this.usageStats };
    }

    getRecentEvents(limit: number = 100): TelemetryEvent[] {
        return this.events.slice(-limit);
    }

    clearTelemetryData(): void {
        this.events = [];
        this.usageStats = {
            completions: 0,
            chatMessages: 0,
            tokensUsed: 0,
            errorsCount: 0,
            lastUsed: 0
        };
    }

    private updateUsageStats(event: string, properties: any): void {
        this.usageStats.lastUsed = Date.now();

        switch (event) {
            case 'completion_generated':
                this.usageStats.completions++;
                if (properties.usage && properties.usage.total_tokens) {
                    this.usageStats.tokensUsed += properties.usage.total_tokens;
                }
                break;

            case 'chat_message_sent':
                this.usageStats.chatMessages++;
                if (properties.tokens) {
                    this.usageStats.tokensUsed += properties.tokens;
                }
                break;

            case 'completion_error':
            case 'chat_error':
            case 'api_error':
                this.usageStats.errorsCount++;
                break;
        }
    }

    dispose(): void {
        // Could send final telemetry batch here if needed
        this.events = [];
    }
}
