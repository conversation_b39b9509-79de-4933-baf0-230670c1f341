"""
Redis-based caching service for AI completions and other data.
"""

import json
import pickle
from typing import Any, Optional, Union
import redis.asyncio as redis
from redis.asyncio import Redis

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class CacheService:
    """Redis-based caching service with JSON and pickle serialization support."""
    
    def __init__(self, redis_client: Optional[Redis] = None):
        self.redis = redis_client or redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=False,  # We'll handle encoding ourselves
        )
    
    async def get(self, key: str, use_pickle: bool = False) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            use_pickle: Whether to use pickle for deserialization
            
        Returns:
            Cached value or None if not found
        """
        try:
            value = await self.redis.get(key)
            if value is None:
                return None
            
            if use_pickle:
                return pickle.loads(value)
            else:
                # Try JSON first, fallback to string
                try:
                    return json.loads(value.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return value.decode('utf-8')
                    
        except Exception as e:
            logger.error("Cache get failed", error=str(e), key=key)
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[int] = None,
        use_pickle: bool = False
    ) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            expire: Expiration time in seconds
            use_pickle: Whether to use pickle for serialization
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if use_pickle:
                serialized_value = pickle.dumps(value)
            else:
                if isinstance(value, (dict, list)):
                    serialized_value = json.dumps(value).encode('utf-8')
                elif isinstance(value, str):
                    serialized_value = value.encode('utf-8')
                else:
                    serialized_value = str(value).encode('utf-8')
            
            await self.redis.set(key, serialized_value, ex=expire)
            return True
            
        except Exception as e:
            logger.error("Cache set failed", error=str(e), key=key)
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            logger.error("Cache delete failed", error=str(e), key=key)
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            result = await self.redis.exists(key)
            return result > 0
        except Exception as e:
            logger.error("Cache exists check failed", error=str(e), key=key)
            return False
    
    async def increment(self, key: str, amount: int = 1, expire: Optional[int] = None) -> Optional[int]:
        """
        Increment a counter in cache.
        
        Args:
            key: Cache key
            amount: Amount to increment by
            expire: Expiration time in seconds (only set if key doesn't exist)
            
        Returns:
            New value after increment, or None if failed
        """
        try:
            # Use pipeline for atomic operations
            pipe = self.redis.pipeline()
            pipe.incrby(key, amount)
            
            # Set expiration only if key doesn't exist
            if expire and not await self.exists(key):
                pipe.expire(key, expire)
            
            results = await pipe.execute()
            return results[0]
            
        except Exception as e:
            logger.error("Cache increment failed", error=str(e), key=key)
            return None
    
    async def get_many(self, keys: list[str], use_pickle: bool = False) -> dict[str, Any]:
        """Get multiple values from cache."""
        try:
            values = await self.redis.mget(keys)
            result = {}
            
            for key, value in zip(keys, values):
                if value is not None:
                    if use_pickle:
                        result[key] = pickle.loads(value)
                    else:
                        try:
                            result[key] = json.loads(value.decode('utf-8'))
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            result[key] = value.decode('utf-8')
            
            return result
            
        except Exception as e:
            logger.error("Cache get_many failed", error=str(e), keys=keys)
            return {}
    
    async def set_many(
        self, 
        mapping: dict[str, Any], 
        expire: Optional[int] = None,
        use_pickle: bool = False
    ) -> bool:
        """Set multiple values in cache."""
        try:
            pipe = self.redis.pipeline()
            
            for key, value in mapping.items():
                if use_pickle:
                    serialized_value = pickle.dumps(value)
                else:
                    if isinstance(value, (dict, list)):
                        serialized_value = json.dumps(value).encode('utf-8')
                    elif isinstance(value, str):
                        serialized_value = value.encode('utf-8')
                    else:
                        serialized_value = str(value).encode('utf-8')
                
                pipe.set(key, serialized_value, ex=expire)
            
            await pipe.execute()
            return True
            
        except Exception as e:
            logger.error("Cache set_many failed", error=str(e))
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching a pattern."""
        try:
            keys = []
            async for key in self.redis.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                return await self.redis.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error("Cache delete_pattern failed", error=str(e), pattern=pattern)
            return 0
    
    async def get_ttl(self, key: str) -> Optional[int]:
        """Get time to live for a key in seconds."""
        try:
            ttl = await self.redis.ttl(key)
            return ttl if ttl >= 0 else None
        except Exception as e:
            logger.error("Cache get_ttl failed", error=str(e), key=key)
            return None
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration time for a key."""
        try:
            result = await self.redis.expire(key, seconds)
            return result
        except Exception as e:
            logger.error("Cache expire failed", error=str(e), key=key)
            return False
    
    async def flush_all(self) -> bool:
        """Flush all keys from cache (use with caution)."""
        try:
            await self.redis.flushall()
            return True
        except Exception as e:
            logger.error("Cache flush_all failed", error=str(e))
            return False
    
    async def get_info(self) -> dict:
        """Get Redis server information."""
        try:
            return await self.redis.info()
        except Exception as e:
            logger.error("Cache get_info failed", error=str(e))
            return {}
    
    async def ping(self) -> bool:
        """Check if Redis server is responsive."""
        try:
            response = await self.redis.ping()
            return response == b'PONG'
        except Exception as e:
            logger.error("Cache ping failed", error=str(e))
            return False
    
    async def close(self):
        """Close Redis connection."""
        try:
            await self.redis.close()
        except Exception as e:
            logger.error("Cache close failed", error=str(e))


# Global cache service instance
cache_service: Optional[CacheService] = None


async def get_cache_service() -> CacheService:
    """Get or create cache service instance."""
    global cache_service
    if cache_service is None:
        cache_service = CacheService()
    return cache_service


async def close_cache_service():
    """Close cache service connection."""
    global cache_service
    if cache_service:
        await cache_service.close()
        cache_service = None
