"""
Application configuration settings.
Uses Pydantic Settings for environment variable management.
"""

from typing import List, Optional, Union
from pydantic import AnyHttpUrl, BaseSettings, validator
import secrets


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Project Information
    PROJECT_NAME: str = "TDS Coder"
    API_V1_STR: str = "/api/v1"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Security
    JWT_SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    BCRYPT_ROUNDS: int = 12
    
    # Session Management
    SESSION_TIMEOUT_MINUTES: int = 60
    PASSWORD_RESET_TOKEN_EXPIRE_MINUTES: int = 15
    EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS: int = 24
    
    # Database
    DATABASE_URL: str = "postgresql://tds_user:tds_password@localhost:5432/tds_coder"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # CORS
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",
        "http://localhost:8000",
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # Stripe Configuration
    STRIPE_SECRET_KEY: str = ""
    STRIPE_PUBLISHABLE_KEY: str = ""
    STRIPE_WEBHOOK_SECRET: str = ""
    STRIPE_PRICE_ID_SOLO: str = ""
    STRIPE_PRICE_ID_TEAM: str = ""
    
    # Email Configuration
    RESEND_API_KEY: str = ""
    FROM_EMAIL: str = "<EMAIL>"
    FROM_NAME: str = "TDS Coder"

    # Stripe Configuration
    STRIPE_PUBLISHABLE_KEY: str = ""
    STRIPE_SECRET_KEY: str = ""
    STRIPE_WEBHOOK_SECRET: str = ""
    STRIPE_PRICE_ID_SOLO: str = ""  # Solo plan price ID
    STRIPE_PRICE_ID_TEAM: str = ""  # Team plan price ID

    # AI Model Configuration
    OPENAI_API_KEY: str = ""
    ANTHROPIC_API_KEY: str = ""
    MISTRAL_API_KEY: str = ""
    DEEPSEEK_API_KEY: str = ""
    GOOGLE_AI_API_KEY: str = ""
    COHERE_API_KEY: str = ""
    META_API_KEY: str = ""  # Together AI or Replicate
    QWEN_API_KEY: str = ""  # DashScope
    HUGGINGFACE_API_KEY: str = ""  # For StarCoder

    DEFAULT_MODEL: str = "gpt-3.5-turbo"
    MAX_TOKENS_DEFAULT: int = 150
    TEMPERATURE_DEFAULT: float = 0.2

    # Model Tier Mapping
    FREE_TIER_MODELS: List[str] = [
        "gpt-3.5-turbo",
        "claude-3-haiku",
        "gemini-1.5-flash",
        "deepseek-coder-v2",
        "mistral-small"
    ]

    SOLO_TIER_MODELS: List[str] = [
        "gpt-3.5-turbo", "gpt-4", "gpt-4o",
        "claude-3-haiku", "claude-3-sonnet",
        "gemini-1.5-flash", "gemini-1.5-pro",
        "deepseek-coder-v2", "deepseek-coder-v3", "deepseek-chat",
        "mistral-small", "codestral",
        "command-r", "codegemma-7b",
        "qwen2.5-coder-7b", "starcoder2-7b"
    ]

    TEAM_TIER_MODELS: List[str] = [
        "gpt-3.5-turbo", "gpt-4", "gpt-4o", "gpt-4-turbo",
        "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "claude-3.5-sonnet",
        "gemini-1.5-flash", "gemini-1.5-pro", "codegemma-7b",
        "deepseek-coder-v2", "deepseek-coder-v3", "deepseek-chat",
        "mistral-small", "mistral-large-2", "codestral",
        "command-r", "command-r-plus",
        "llama-3-70b-instruct", "code-llama-34b",
        "qwen2.5-coder-7b", "qwen2.5-coder-32b",
        "starcoder2-7b", "starcoder2-15b"
    ]

    ENTERPRISE_TIER_MODELS: List[str] = [
        # All models available for enterprise customers
        "gpt-3.5-turbo", "gpt-4", "gpt-4o", "gpt-4-turbo",
        "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "claude-3.5-sonnet",
        "gemini-1.5-flash", "gemini-1.5-pro", "codegemma-7b",
        "deepseek-coder-v2", "deepseek-coder-v3", "deepseek-chat",
        "mistral-small", "mistral-large-2", "codestral",
        "command-r", "command-r-plus",
        "llama-3-70b-instruct", "code-llama-34b",
        "qwen2.5-coder-7b", "qwen2.5-coder-32b",
        "starcoder2-7b", "starcoder2-15b",
        "custom"
    ]

    # Rate Limiting
    RATE_LIMIT_FREE_DAILY: int = 100
    RATE_LIMIT_FREE_BURST: int = 1
    RATE_LIMIT_SOLO_DAILY: int = 10000
    RATE_LIMIT_SOLO_BURST: int = 10
    RATE_LIMIT_TEAM_DAILY: int = 50000
    RATE_LIMIT_TEAM_BURST: int = 50
    
    # Refact AI Configuration (Phase 2)
    REFACT_API_URL: str = "http://localhost:8001"
    REFACT_API_KEY: str = ""
    
    # Monitoring
    SENTRY_DSN: Optional[str] = None
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create global settings instance
settings = Settings()


# Subscription tier configurations
class SubscriptionTiers:
    """Subscription tier configurations and limits."""
    
    FREE = {
        "name": "Free",
        "price": 0,
        "daily_requests": 100,
        "burst_limit": 1,
        "features": ["basic_completion"],
        "seats": 1,
    }
    
    SOLO = {
        "name": "Solo",
        "price": 19,
        "daily_requests": 10000,
        "burst_limit": 10,
        "features": ["basic_completion", "advanced_completion", "chat"],
        "seats": 1,
    }
    
    TEAM = {
        "name": "Team",
        "price": 49,
        "daily_requests": 50000,
        "burst_limit": 50,
        "features": ["basic_completion", "advanced_completion", "chat", "team_dashboard"],
        "seats": 5,
    }
    
    ENTERPRISE = {
        "name": "Enterprise",
        "price": None,  # Custom pricing
        "daily_requests": -1,  # Unlimited
        "burst_limit": 100,
        "features": ["all"],
        "seats": -1,  # Unlimited
    }
    
    @classmethod
    def get_tier_by_name(cls, name: str) -> dict:
        """Get subscription tier configuration by name."""
        tiers = {
            "free": cls.FREE,
            "solo": cls.SOLO,
            "team": cls.TEAM,
            "enterprise": cls.ENTERPRISE,
        }
        return tiers.get(name.lower(), cls.FREE)
    
    @classmethod
    def get_all_tiers(cls) -> List[dict]:
        """Get all subscription tiers."""
        return [cls.FREE, cls.SOLO, cls.TEAM, cls.ENTERPRISE]
