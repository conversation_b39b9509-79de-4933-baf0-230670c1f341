#!/bin/bash

# TDS Coder Development Setup Script
# This script sets up the development environment for TDS Coder

set -e

echo "🚀 Setting up TDS Coder development environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please update it with your configuration."
else
    echo "✅ .env file already exists."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p backend/logs
mkdir -p database/backups
mkdir -p frontend/.next
mkdir -p frontend/node_modules

# Set permissions
echo "🔐 Setting permissions..."
chmod +x scripts/*.sh

# Start PostgreSQL and Redis first
echo "🐘 Starting PostgreSQL and Redis..."
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec postgres pg_isready -U tds_user -d tds_coder; do
    sleep 2
done

echo "✅ PostgreSQL is ready!"

# Install backend dependencies
echo "🐍 Installing backend dependencies..."
cd backend
if [ ! -d "venv" ]; then
    python -m venv venv
fi

# Activate virtual environment
source venv/bin/activate || source venv/Scripts/activate

# Install requirements
pip install -r requirements.txt

# Initialize Alembic if not already done
if [ ! -d "alembic" ]; then
    echo "🔄 Initializing Alembic..."
    alembic init alembic
fi

# Run database migrations
echo "🔄 Running database migrations..."
alembic upgrade head

cd ..

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Start all services
echo "🚀 Starting all services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🏥 Checking service health..."

# Check backend health
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "⚠️  Backend health check failed"
fi

# Check frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is accessible"
else
    echo "⚠️  Frontend health check failed"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Services:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/api/v1/docs"
echo "   PostgreSQL: localhost:5432"
echo "   Redis: localhost:6379"
echo ""
echo "🔧 Useful commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop services: docker-compose down"
echo "   Restart services: docker-compose restart"
echo "   Backend shell: docker-compose exec backend bash"
echo "   Database shell: docker-compose exec postgres psql -U tds_user -d tds_coder"
echo ""
echo "📖 Next steps:"
echo "   1. Update .env file with your API keys"
echo "   2. Configure Stripe webhooks"
echo "   3. Set up SendGrid for emails"
echo "   4. Start coding! 🚀"
