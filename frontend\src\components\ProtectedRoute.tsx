'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireVerified?: boolean
  redirectTo?: string
}

export default function ProtectedRoute({ 
  children, 
  requireVerified = false,
  redirectTo = '/auth/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        // Store the current path for redirect after login
        const currentPath = window.location.pathname + window.location.search
        const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`
        router.push(redirectUrl)
        return
      }

      if (requireVerified && user && !user.is_verified) {
        router.push('/auth/verify-email')
        return
      }
    }
  }, [isAuthenticated, loading, user, requireVerified, router, redirectTo])

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render children if not authenticated or verification required
  if (!isAuthenticated || (requireVerified && user && !user.is_verified)) {
    return null
  }

  return <>{children}</>
}
