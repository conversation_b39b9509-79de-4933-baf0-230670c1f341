"""
Email service for sending transactional emails using Resend.
"""

from typing import Optional, Dict, Any
import resend
from datetime import datetime

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class EmailService:
    """Service for sending transactional emails using Resend."""

    def __init__(self):
        resend.api_key = settings.RESEND_API_KEY
        self.from_email = f"{settings.FROM_NAME} <{settings.FROM_EMAIL}>"
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        plain_content: Optional[str] = None,
    ) -> bool:
        """
        Send email using Resend.

        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            plain_content: Plain text email content (optional)

        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            # Prepare email data
            email_data = {
                "from": self.from_email,
                "to": [to_email],
                "subject": subject,
                "html": html_content,
            }

            if plain_content:
                email_data["text"] = plain_content

            # Send email
            response = resend.Emails.send(email_data)

            if response and response.get("id"):
                logger.info(
                    "Email sent successfully",
                    to_email=to_email,
                    subject=subject,
                    email_id=response.get("id")
                )
                return True
            else:
                logger.error(
                    "Failed to send email",
                    to_email=to_email,
                    response=response,
                )
                return False

        except Exception as e:
            logger.error("Email sending failed", error=str(e), to_email=to_email)
            return False
    
    async def send_verification_email(
        self,
        to_email: str,
        user_name: str,
        verification_token: str,
        base_url: str = "http://localhost:3000",
    ) -> bool:
        """Send email verification email."""
        from app.templates.email_templates import get_verification_email_template

        verification_url = f"{base_url}/auth/verify-email?token={verification_token}"

        template_data = get_verification_email_template(
            user_name=user_name,
            verification_url=verification_url,
            expires_hours=24
        )

        subject = "Verify your TDS Coder account"

        return await self.send_email(
            to_email,
            subject,
            template_data['html'],
            template_data['text']
        )
    
    async def send_password_reset_email(
        self,
        to_email: str,
        user_name: str,
        reset_token: str,
        base_url: str = "http://localhost:3000",
    ) -> bool:
        """Send password reset email."""
        from app.templates.email_templates import get_password_reset_email_template

        reset_url = f"{base_url}/auth/reset-password?token={reset_token}"

        template_data = get_password_reset_email_template(
            user_name=user_name,
            reset_url=reset_url,
            expires_minutes=15
        )

        subject = "Reset your TDS Coder password"

        return await self.send_email(
            to_email,
            subject,
            template_data['html'],
            template_data['text']
        )

    async def send_2fa_enabled_email(
        self,
        to_email: str,
        user_name: str,
    ) -> bool:
        """Send 2FA enabled notification email."""
        from app.templates.email_templates import get_2fa_enabled_email_template

        template_data = get_2fa_enabled_email_template(user_name=user_name)

        subject = "Two-Factor Authentication Enabled - TDS Coder"

        return await self.send_email(
            to_email,
            subject,
            template_data['html'],
            template_data['text']
        )
