"""
Email service for sending transactional emails.
"""

from typing import Optional, Dict, Any
import sendgrid
from sendgrid.helpers.mail import Mail, Email, To, Content

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class EmailService:
    """Service for sending transactional emails using SendGrid."""
    
    def __init__(self):
        self.sg = sendgrid.SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
        self.from_email = Email(settings.FROM_EMAIL, settings.FROM_NAME)
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        plain_content: Optional[str] = None,
    ) -> bool:
        """
        Send email using SendGrid.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            plain_content: Plain text email content
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            to = To(to_email)
            
            # Create mail object
            mail = Mail(
                from_email=self.from_email,
                to_emails=to,
                subject=subject,
                html_content=Content("text/html", html_content),
            )
            
            if plain_content:
                mail.add_content(Content("text/plain", plain_content))
            
            # Send email
            response = self.sg.send(mail)
            
            if response.status_code in [200, 201, 202]:
                logger.info("Email sent successfully", to_email=to_email, subject=subject)
                return True
            else:
                logger.error(
                    "Failed to send email",
                    to_email=to_email,
                    status_code=response.status_code,
                    response_body=response.body,
                )
                return False
                
        except Exception as e:
            logger.error("Email sending failed", error=str(e), to_email=to_email)
            return False
    
    async def send_verification_email(
        self,
        to_email: str,
        verification_token: str,
        base_url: str = "http://localhost:3000",
    ) -> bool:
        """Send email verification email."""
        verification_url = f"{base_url}/verify-email?token={verification_token}"
        
        subject = "Verify your TDS Coder account"
        html_content = f"""
        <html>
        <body>
            <h2>Welcome to TDS Coder!</h2>
            <p>Thank you for signing up. Please verify your email address by clicking the link below:</p>
            <p><a href="{verification_url}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{verification_url}</p>
            <p>This link will expire in 24 hours.</p>
            <p>If you didn't create an account with TDS Coder, please ignore this email.</p>
        </body>
        </html>
        """
        
        plain_content = f"""
        Welcome to TDS Coder!
        
        Thank you for signing up. Please verify your email address by visiting:
        {verification_url}
        
        This link will expire in 24 hours.
        
        If you didn't create an account with TDS Coder, please ignore this email.
        """
        
        return await self.send_email(to_email, subject, html_content, plain_content)
    
    async def send_password_reset_email(
        self,
        to_email: str,
        reset_token: str,
        base_url: str = "http://localhost:3000",
    ) -> bool:
        """Send password reset email."""
        reset_url = f"{base_url}/reset-password?token={reset_token}"
        
        subject = "Reset your TDS Coder password"
        html_content = f"""
        <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>You requested to reset your password for your TDS Coder account.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="{reset_url}" style="background-color: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{reset_url}</p>
            <p>This link will expire in 15 minutes.</p>
            <p>If you didn't request a password reset, please ignore this email.</p>
        </body>
        </html>
        """
        
        plain_content = f"""
        Password Reset Request
        
        You requested to reset your password for your TDS Coder account.
        
        Visit this link to reset your password:
        {reset_url}
        
        This link will expire in 15 minutes.
        
        If you didn't request a password reset, please ignore this email.
        """
        
        return await self.send_email(to_email, subject, html_content, plain_content)
