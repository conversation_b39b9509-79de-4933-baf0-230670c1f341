"""
Team collaboration endpoints for workspace management and shared resources.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from pydantic import BaseModel, Field, EmailStr
import uuid

from app.db.session import get_db
from app.utils.security import get_current_active_user
from app.models.user import User
from app.models.team import Team, TeamRole, CodeSnippet
from app.services.team import TeamService
from app.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)


class TeamCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Team name")
    description: Optional[str] = Field(None, max_length=1000, description="Team description")
    max_members: int = Field(10, ge=2, le=100, description="Maximum number of team members")
    is_public: bool = Field(False, description="Whether team is publicly discoverable")
    enable_usage_pooling: bool = Field(True, description="Enable usage pooling for team")


class TeamUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    max_members: Optional[int] = Field(None, ge=2, le=100)
    is_public: Optional[bool] = None
    enable_usage_pooling: Optional[bool] = None
    require_approval_for_snippets: Optional[bool] = None


class TeamInvite(BaseModel):
    email: EmailStr = Field(..., description="Email address of user to invite")
    role: TeamRole = Field(TeamRole.MEMBER, description="Role to assign to invited user")
    message: Optional[str] = Field(None, max_length=500, description="Optional message to include")


class CodeSnippetCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="Snippet title")
    description: Optional[str] = Field(None, max_length=1000, description="Snippet description")
    code: str = Field(..., min_length=1, description="Code content")
    language: str = Field(..., min_length=1, max_length=50, description="Programming language")
    tags: Optional[List[str]] = Field(None, description="Tags for categorization")
    visibility: str = Field("team", description="Visibility level (team, public, private)")


class TeamResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    slug: str
    max_members: int
    is_public: bool
    enable_usage_pooling: bool
    member_count: int
    owner_id: str
    created_at: str
    
    class Config:
        from_attributes = True


@router.post("/", response_model=TeamResponse)
async def create_team(
    team_data: TeamCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a new team workspace.
    
    Creates a team with the current user as owner. Teams enable collaboration
    through shared code snippets, usage pooling, and member management.
    """
    try:
        team_service = TeamService(db)
        team = await team_service.create_team(
            owner=current_user,
            name=team_data.name,
            description=team_data.description,
            max_members=team_data.max_members
        )
        
        return TeamResponse(
            id=str(team.id),
            name=team.name,
            description=team.description,
            slug=team.slug,
            max_members=team.max_members,
            is_public=team.is_public,
            enable_usage_pooling=team.enable_usage_pooling,
            member_count=1,  # Owner is first member
            owner_id=str(team.owner_id),
            created_at=team.created_at.isoformat()
        )
        
    except Exception as e:
        logger.error("Failed to create team", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create team"
        )


@router.get("/", response_model=List[TeamResponse])
async def list_user_teams(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    List all teams that the current user belongs to.
    
    Returns teams where the user is either the owner or a member.
    """
    try:
        team_service = TeamService(db)
        teams = await team_service.get_user_teams(current_user)
        
        return [
            TeamResponse(
                id=str(team.id),
                name=team.name,
                description=team.description,
                slug=team.slug,
                max_members=team.max_members,
                is_public=team.is_public,
                enable_usage_pooling=team.enable_usage_pooling,
                member_count=len(team.members) if hasattr(team, 'members') else 1,
                owner_id=str(team.owner_id),
                created_at=team.created_at.isoformat()
            )
            for team in teams
        ]
        
    except Exception as e:
        logger.error("Failed to list user teams", 
                    error=str(e), 
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve teams"
        )


@router.get("/{team_id}", response_model=TeamResponse)
async def get_team(
    team_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get details of a specific team.
    
    User must be a member of the team to access its details.
    """
    try:
        team = await db.get(Team, team_id)
        if not team or team.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Team not found"
            )
        
        # Check if user has access to team
        team_service = TeamService(db)
        if not await team_service._is_user_team_member(team, current_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to team"
            )
        
        return TeamResponse(
            id=str(team.id),
            name=team.name,
            description=team.description,
            slug=team.slug,
            max_members=team.max_members,
            is_public=team.is_public,
            enable_usage_pooling=team.enable_usage_pooling,
            member_count=await team_service._get_team_member_count(team),
            owner_id=str(team.owner_id),
            created_at=team.created_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get team", 
                    error=str(e), 
                    team_id=team_id,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve team"
        )


@router.post("/{team_id}/invite")
async def invite_team_member(
    team_id: str,
    invite_data: TeamInvite,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Invite a user to join the team.
    
    Only team owners and admins can send invitations.
    """
    try:
        team = await db.get(Team, team_id)
        if not team or team.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Team not found"
            )
        
        team_service = TeamService(db)
        invitation = await team_service.invite_member(
            team=team,
            inviter=current_user,
            invitee_email=invite_data.email,
            role=invite_data.role,
            message=invite_data.message
        )
        
        return {
            "message": "Invitation sent successfully",
            "invitation_id": str(invitation.id),
            "expires_at": invitation.expires_at.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to invite team member", 
                    error=str(e), 
                    team_id=team_id,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send invitation"
        )


@router.post("/invitations/{token}/accept")
async def accept_team_invitation(
    token: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Accept a team invitation using the invitation token.
    
    The invitation token is typically sent via email.
    """
    try:
        team_service = TeamService(db)
        team = await team_service.accept_invitation(token, current_user)
        
        return {
            "message": "Invitation accepted successfully",
            "team_id": str(team.id),
            "team_name": team.name
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to accept team invitation", 
                    error=str(e), 
                    token=token,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to accept invitation"
        )


@router.post("/{team_id}/snippets")
async def create_code_snippet(
    team_id: str,
    snippet_data: CodeSnippetCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Create a shared code snippet for the team.
    
    Snippets can be shared with team members and used for collaboration.
    """
    try:
        team = await db.get(Team, team_id)
        if not team or team.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Team not found"
            )
        
        team_service = TeamService(db)
        snippet = await team_service.create_code_snippet(
            author=current_user,
            title=snippet_data.title,
            code=snippet_data.code,
            language=snippet_data.language,
            team=team,
            description=snippet_data.description,
            tags=snippet_data.tags,
            visibility=snippet_data.visibility
        )
        
        return {
            "id": str(snippet.id),
            "title": snippet.title,
            "language": snippet.language,
            "visibility": snippet.visibility,
            "requires_approval": snippet.requires_approval,
            "is_approved": snippet.is_approved,
            "created_at": snippet.created_at.isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create code snippet", 
                    error=str(e), 
                    team_id=team_id,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create code snippet"
        )


@router.get("/{team_id}/snippets")
async def list_team_snippets(
    team_id: str,
    language: Optional[str] = Query(None, description="Filter by programming language"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of snippets to return"),
    offset: int = Query(0, ge=0, description="Number of snippets to skip"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    List code snippets for a team.
    
    Returns approved snippets that the user has access to.
    """
    try:
        team = await db.get(Team, team_id)
        if not team or team.is_deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Team not found"
            )
        
        # Parse tags
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
        
        team_service = TeamService(db)
        snippets = await team_service.get_team_snippets(
            team=team,
            user=current_user,
            language=language,
            tags=tag_list,
            limit=limit,
            offset=offset
        )
        
        return [
            {
                "id": str(snippet.id),
                "title": snippet.title,
                "description": snippet.description,
                "language": snippet.language,
                "tags": snippet.tags,
                "visibility": snippet.visibility,
                "usage_count": snippet.usage_count,
                "author_id": str(snippet.author_id),
                "created_at": snippet.created_at.isoformat(),
                "updated_at": snippet.updated_at.isoformat()
            }
            for snippet in snippets
        ]
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to list team snippets", 
                    error=str(e), 
                    team_id=team_id,
                    user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve snippets"
        )
