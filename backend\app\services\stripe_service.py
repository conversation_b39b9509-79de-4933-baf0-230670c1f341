"""
Stripe payment service for subscription and billing management.
"""

from typing import Optional, Dict, Any, List
import stripe
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.config import settings
from app.core.logging import get_logger
from app.models.user import User
from app.models.subscription import Subscription, SubscriptionStatus, SubscriptionTier

logger = get_logger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeService:
    """Service for handling Stripe payment operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_customer(self, user: User) -> str:
        """
        Create a Stripe customer for the user.
        
        Args:
            user: User object
            
        Returns:
            Stripe customer ID
        """
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=user.full_name,
                metadata={
                    "user_id": str(user.id),
                    "environment": settings.ENVIRONMENT,
                }
            )
            
            logger.info("Stripe customer created", 
                       customer_id=customer.id, 
                       user_id=str(user.id))
            
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error("Failed to create Stripe customer", 
                        error=str(e), 
                        user_id=str(user.id))
            raise
    
    async def create_subscription(
        self, 
        user: User, 
        price_id: str, 
        payment_method_id: Optional[str] = None,
        trial_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a new subscription for the user.
        
        Args:
            user: User object
            price_id: Stripe price ID
            payment_method_id: Payment method ID (optional for trials)
            trial_days: Number of trial days (optional)
            
        Returns:
            Subscription creation result
        """
        try:
            # Get or create Stripe customer
            subscription = await self.get_user_subscription(user)
            if not subscription.stripe_customer_id:
                customer_id = await self.create_customer(user)
                subscription.stripe_customer_id = customer_id
                await self.db.commit()
            else:
                customer_id = subscription.stripe_customer_id
            
            # Prepare subscription data
            subscription_data = {
                "customer": customer_id,
                "items": [{"price": price_id}],
                "metadata": {
                    "user_id": str(user.id),
                    "environment": settings.ENVIRONMENT,
                },
                "expand": ["latest_invoice.payment_intent"],
            }
            
            # Add payment method if provided
            if payment_method_id:
                subscription_data["default_payment_method"] = payment_method_id
            
            # Add trial if specified
            if trial_days:
                subscription_data["trial_period_days"] = trial_days
            
            # Create Stripe subscription
            stripe_subscription = stripe.Subscription.create(**subscription_data)
            
            # Update local subscription
            await self.update_subscription_from_stripe(subscription, stripe_subscription)
            
            logger.info("Subscription created successfully", 
                       subscription_id=stripe_subscription.id,
                       user_id=str(user.id))
            
            return {
                "subscription": stripe_subscription,
                "client_secret": stripe_subscription.latest_invoice.payment_intent.client_secret
                if stripe_subscription.latest_invoice.payment_intent else None
            }
            
        except stripe.error.StripeError as e:
            logger.error("Failed to create subscription", 
                        error=str(e), 
                        user_id=str(user.id))
            raise
    
    async def update_subscription(
        self, 
        subscription: Subscription, 
        new_price_id: str,
        prorate: bool = True
    ) -> stripe.Subscription:
        """
        Update an existing subscription to a new plan.
        
        Args:
            subscription: Local subscription object
            new_price_id: New Stripe price ID
            prorate: Whether to prorate the change
            
        Returns:
            Updated Stripe subscription
        """
        try:
            if not subscription.stripe_subscription_id:
                raise ValueError("No Stripe subscription ID found")
            
            # Get current subscription
            stripe_subscription = stripe.Subscription.retrieve(
                subscription.stripe_subscription_id
            )
            
            # Update subscription
            updated_subscription = stripe.Subscription.modify(
                subscription.stripe_subscription_id,
                items=[{
                    "id": stripe_subscription["items"]["data"][0].id,
                    "price": new_price_id,
                }],
                proration_behavior="create_prorations" if prorate else "none",
                metadata={
                    "user_id": str(subscription.user_id),
                    "environment": settings.ENVIRONMENT,
                }
            )
            
            # Update local subscription
            await self.update_subscription_from_stripe(subscription, updated_subscription)
            
            logger.info("Subscription updated successfully", 
                       subscription_id=updated_subscription.id,
                       user_id=str(subscription.user_id))
            
            return updated_subscription
            
        except stripe.error.StripeError as e:
            logger.error("Failed to update subscription", 
                        error=str(e), 
                        subscription_id=subscription.id)
            raise
    
    async def cancel_subscription(
        self, 
        subscription: Subscription, 
        at_period_end: bool = True
    ) -> stripe.Subscription:
        """
        Cancel a subscription.
        
        Args:
            subscription: Local subscription object
            at_period_end: Whether to cancel at period end or immediately
            
        Returns:
            Cancelled Stripe subscription
        """
        try:
            if not subscription.stripe_subscription_id:
                raise ValueError("No Stripe subscription ID found")
            
            if at_period_end:
                # Cancel at period end
                cancelled_subscription = stripe.Subscription.modify(
                    subscription.stripe_subscription_id,
                    cancel_at_period_end=True
                )
            else:
                # Cancel immediately
                cancelled_subscription = stripe.Subscription.delete(
                    subscription.stripe_subscription_id
                )
            
            # Update local subscription
            await self.update_subscription_from_stripe(subscription, cancelled_subscription)
            
            logger.info("Subscription cancelled", 
                       subscription_id=cancelled_subscription.id,
                       user_id=str(subscription.user_id),
                       at_period_end=at_period_end)
            
            return cancelled_subscription
            
        except stripe.error.StripeError as e:
            logger.error("Failed to cancel subscription", 
                        error=str(e), 
                        subscription_id=subscription.id)
            raise
    
    async def add_payment_method(
        self, 
        user: User, 
        payment_method_id: str
    ) -> stripe.PaymentMethod:
        """
        Add a payment method to a customer.
        
        Args:
            user: User object
            payment_method_id: Stripe payment method ID
            
        Returns:
            Attached payment method
        """
        try:
            subscription = await self.get_user_subscription(user)
            if not subscription.stripe_customer_id:
                customer_id = await self.create_customer(user)
                subscription.stripe_customer_id = customer_id
                await self.db.commit()
            else:
                customer_id = subscription.stripe_customer_id
            
            # Attach payment method to customer
            payment_method = stripe.PaymentMethod.attach(
                payment_method_id,
                customer=customer_id
            )
            
            logger.info("Payment method added", 
                       payment_method_id=payment_method_id,
                       user_id=str(user.id))
            
            return payment_method
            
        except stripe.error.StripeError as e:
            logger.error("Failed to add payment method", 
                        error=str(e), 
                        user_id=str(user.id))
            raise
    
    async def get_user_subscription(self, user: User) -> Subscription:
        """
        Get or create user's subscription record.
        
        Args:
            user: User object
            
        Returns:
            Subscription object
        """
        stmt = select(Subscription).where(
            Subscription.user_id == user.id,
            Subscription.is_deleted == False
        )
        result = await self.db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            # Create default free subscription
            subscription = Subscription(
                user_id=user.id,
                tier=SubscriptionTier.FREE,
                status=SubscriptionStatus.ACTIVE,
                daily_request_limit=100,
                burst_limit=1,
                seats_limit=1,
                currency="USD"
            )
            self.db.add(subscription)
            await self.db.commit()
            await self.db.refresh(subscription)
        
        return subscription

    async def update_subscription_from_stripe(
        self,
        subscription: Subscription,
        stripe_subscription: stripe.Subscription
    ) -> None:
        """
        Update local subscription from Stripe subscription data.

        Args:
            subscription: Local subscription object
            stripe_subscription: Stripe subscription object
        """
        try:
            # Map Stripe status to local status
            status_mapping = {
                "active": SubscriptionStatus.ACTIVE,
                "past_due": SubscriptionStatus.PAST_DUE,
                "unpaid": SubscriptionStatus.UNPAID,
                "canceled": SubscriptionStatus.CANCELED,
                "incomplete": SubscriptionStatus.INACTIVE,
                "incomplete_expired": SubscriptionStatus.CANCELED,
                "trialing": SubscriptionStatus.TRIALING,
                "paused": SubscriptionStatus.PAUSED,
            }

            # Update subscription fields
            subscription.stripe_subscription_id = stripe_subscription.id
            subscription.status = status_mapping.get(
                stripe_subscription.status,
                SubscriptionStatus.INACTIVE
            )

            # Update periods
            if stripe_subscription.current_period_start:
                subscription.current_period_start = datetime.fromtimestamp(
                    stripe_subscription.current_period_start
                )

            if stripe_subscription.current_period_end:
                subscription.current_period_end = datetime.fromtimestamp(
                    stripe_subscription.current_period_end
                )

            # Update trial information
            if stripe_subscription.trial_start:
                subscription.trial_start = datetime.fromtimestamp(
                    stripe_subscription.trial_start
                )

            if stripe_subscription.trial_end:
                subscription.trial_end = datetime.fromtimestamp(
                    stripe_subscription.trial_end
                )

            # Update cancellation information
            if stripe_subscription.canceled_at:
                subscription.canceled_at = datetime.fromtimestamp(
                    stripe_subscription.canceled_at
                )

            if stripe_subscription.ended_at:
                subscription.ended_at = datetime.fromtimestamp(
                    stripe_subscription.ended_at
                )

            # Update pricing information
            if stripe_subscription.items and stripe_subscription.items.data:
                item = stripe_subscription.items.data[0]
                if item.price:
                    subscription.stripe_price_id = item.price.id
                    subscription.amount = item.price.unit_amount / 100  # Convert cents to dollars
                    subscription.currency = item.price.currency.upper()

            # Update tier based on price ID
            tier_mapping = {
                settings.STRIPE_PRICE_ID_SOLO: SubscriptionTier.SOLO,
                settings.STRIPE_PRICE_ID_TEAM: SubscriptionTier.TEAM,
            }

            if subscription.stripe_price_id in tier_mapping:
                subscription.tier = tier_mapping[subscription.stripe_price_id]

                # Update limits based on tier
                if subscription.tier == SubscriptionTier.SOLO:
                    subscription.daily_request_limit = 10000
                    subscription.burst_limit = 10
                    subscription.seats_limit = 1
                elif subscription.tier == SubscriptionTier.TEAM:
                    subscription.daily_request_limit = 50000
                    subscription.burst_limit = 50
                    subscription.seats_limit = 5

            await self.db.commit()

        except Exception as e:
            logger.error("Failed to update subscription from Stripe",
                        error=str(e),
                        subscription_id=subscription.id)
            raise

    async def handle_webhook_event(self, event: Dict[str, Any]) -> bool:
        """
        Handle Stripe webhook events.

        Args:
            event: Stripe webhook event data

        Returns:
            True if handled successfully
        """
        try:
            event_type = event["type"]
            data = event["data"]["object"]

            logger.info("Processing Stripe webhook", event_type=event_type, event_id=event["id"])

            if event_type == "customer.subscription.created":
                await self._handle_subscription_created(data)
            elif event_type == "customer.subscription.updated":
                await self._handle_subscription_updated(data)
            elif event_type == "customer.subscription.deleted":
                await self._handle_subscription_deleted(data)
            elif event_type == "invoice.payment_succeeded":
                await self._handle_payment_succeeded(data)
            elif event_type == "invoice.payment_failed":
                await self._handle_payment_failed(data)
            elif event_type == "customer.subscription.trial_will_end":
                await self._handle_trial_will_end(data)
            else:
                logger.info("Unhandled webhook event type", event_type=event_type)
                return True

            logger.info("Webhook processed successfully", event_type=event_type)
            return True

        except Exception as e:
            logger.error("Failed to process webhook",
                        error=str(e),
                        event_type=event.get("type"),
                        event_id=event.get("id"))
            return False

    async def _handle_subscription_created(self, subscription_data: Dict[str, Any]) -> None:
        """Handle subscription created webhook."""
        user_id = subscription_data["metadata"].get("user_id")
        if not user_id:
            logger.warning("No user_id in subscription metadata")
            return

        stmt = select(Subscription).where(
            Subscription.user_id == user_id,
            Subscription.is_deleted == False
        )
        result = await self.db.execute(stmt)
        subscription = result.scalar_one_or_none()

        if subscription:
            stripe_subscription = stripe.Subscription.construct_from(
                subscription_data, stripe.api_key
            )
            await self.update_subscription_from_stripe(subscription, stripe_subscription)

    async def _handle_subscription_updated(self, subscription_data: Dict[str, Any]) -> None:
        """Handle subscription updated webhook."""
        await self._handle_subscription_created(subscription_data)  # Same logic

    async def _handle_subscription_deleted(self, subscription_data: Dict[str, Any]) -> None:
        """Handle subscription deleted webhook."""
        subscription_id = subscription_data["id"]

        stmt = select(Subscription).where(
            Subscription.stripe_subscription_id == subscription_id,
            Subscription.is_deleted == False
        )
        result = await self.db.execute(stmt)
        subscription = result.scalar_one_or_none()

        if subscription:
            subscription.status = SubscriptionStatus.CANCELED
            subscription.ended_at = datetime.utcnow()
            await self.db.commit()

    async def _handle_payment_succeeded(self, invoice_data: Dict[str, Any]) -> None:
        """Handle successful payment webhook."""
        subscription_id = invoice_data.get("subscription")
        if not subscription_id:
            return

        stmt = select(Subscription).where(
            Subscription.stripe_subscription_id == subscription_id,
            Subscription.is_deleted == False
        )
        result = await self.db.execute(stmt)
        subscription = result.scalar_one_or_none()

        if subscription:
            # Reset failed payment count on successful payment
            subscription.failed_payment_count = 0
            subscription.last_failed_payment = None
            subscription.next_retry_date = None
            await self.db.commit()

    async def _handle_payment_failed(self, invoice_data: Dict[str, Any]) -> None:
        """Handle failed payment webhook."""
        subscription_id = invoice_data.get("subscription")
        if not subscription_id:
            return

        stmt = select(Subscription).where(
            Subscription.stripe_subscription_id == subscription_id,
            Subscription.is_deleted == False
        )
        result = await self.db.execute(stmt)
        subscription = result.scalar_one_or_none()

        if subscription:
            # Increment failed payment count
            subscription.failed_payment_count += 1
            subscription.last_failed_payment = datetime.utcnow()

            # Set next retry date (Stripe handles this automatically)
            subscription.next_retry_date = datetime.utcnow() + timedelta(days=3)

            await self.db.commit()

    async def _handle_trial_will_end(self, subscription_data: Dict[str, Any]) -> None:
        """Handle trial ending soon webhook."""
        # This could trigger email notifications to users
        user_id = subscription_data["metadata"].get("user_id")
        if user_id:
            logger.info("Trial ending soon", user_id=user_id)
            # TODO: Send trial ending email notification

    @staticmethod
    def verify_webhook_signature(payload: bytes, signature: str) -> bool:
        """
        Verify Stripe webhook signature.

        Args:
            payload: Raw request payload
            signature: Stripe signature header

        Returns:
            True if signature is valid
        """
        try:
            stripe.Webhook.construct_event(
                payload, signature, settings.STRIPE_WEBHOOK_SECRET
            )
            return True
        except (ValueError, stripe.error.SignatureVerificationError):
            return False
