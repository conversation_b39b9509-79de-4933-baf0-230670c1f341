{"name": "tds-coder", "displayName": "TDS Coder", "description": "AI-powered code completion and assistance for developers", "version": "1.0.0", "publisher": "tdscoder", "icon": "images/icon.png", "repository": {"type": "git", "url": "https://github.com/tdscoder/vscode-extension"}, "bugs": {"url": "https://github.com/tdscoder/vscode-extension/issues"}, "homepage": "https://tdscoder.com", "engines": {"vscode": "^1.74.0"}, "categories": ["Machine Learning", "Programming Languages", "Snippets", "Other"], "keywords": ["ai", "code completion", "autocomplete", "copilot", "machine learning", "productivity"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "tdsCoder.authenticate", "title": "Authenticate with TDS Coder", "category": "TDS Coder"}, {"command": "tdsCoder.logout", "title": "Logout", "category": "TDS Coder"}, {"command": "tdsCoder.openSettings", "title": "Open Settings", "category": "TDS Coder"}, {"command": "tdsCoder.toggleInlineCompletion", "title": "Toggle Inline Completion", "category": "TDS Coder"}, {"command": "tdsCoder.showUsage", "title": "Show Usage Statistics", "category": "TDS Coder"}, {"command": "tdsCoder.clearCache", "title": "<PERSON>ache", "category": "TDS Coder"}], "configuration": {"title": "TDS Coder", "properties": {"tdsCoder.apiKey": {"type": "string", "default": "", "description": "Your TDS Coder API key", "order": 1}, "tdsCoder.apiUrl": {"type": "string", "default": "https://api.tdscoder.com", "description": "TDS Coder API URL", "order": 2}, "tdsCoder.model": {"type": "string", "default": "auto", "enum": ["auto", "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "claude-3-haiku", "claude-3-sonnet", "claude-3-opus"], "description": "AI model to use for completions", "order": 3}, "tdsCoder.enableInlineCompletion": {"type": "boolean", "default": true, "description": "Enable inline code completion", "order": 4}, "tdsCoder.enableChat": {"type": "boolean", "default": true, "description": "Enable chat assistance", "order": 5}, "tdsCoder.maxTokens": {"type": "number", "default": 150, "minimum": 1, "maximum": 4000, "description": "Maximum tokens for completion", "order": 6}, "tdsCoder.temperature": {"type": "number", "default": 0.2, "minimum": 0, "maximum": 2, "description": "Sampling temperature (0 = deterministic, 2 = very creative)", "order": 7}, "tdsCoder.completionDelay": {"type": "number", "default": 300, "minimum": 100, "maximum": 2000, "description": "Delay in milliseconds before triggering completion", "order": 8}, "tdsCoder.enableTelemetry": {"type": "boolean", "default": true, "description": "Enable usage telemetry to improve the service", "order": 9}, "tdsCoder.enableOfflineMode": {"type": "boolean", "default": false, "description": "Enable offline mode with cached completions", "order": 10}, "tdsCoder.cacheSize": {"type": "number", "default": 1000, "minimum": 100, "maximum": 10000, "description": "Maximum number of cached completions", "order": 11}, "tdsCoder.enableLogging": {"type": "boolean", "default": false, "description": "Enable debug logging", "order": 12}}}, "keybindings": [{"command": "tdsCoder.toggleInlineCompletion", "key": "ctrl+alt+t", "mac": "cmd+alt+t", "when": "editorTextFocus"}], "menus": {"commandPalette": [{"command": "tdsCoder.authenticate", "when": "true"}, {"command": "tdsCoder.logout", "when": "true"}, {"command": "tdsCoder.openSettings", "when": "true"}, {"command": "tdsCoder.toggleInlineCompletion", "when": "true"}, {"command": "tdsCoder.showUsage", "when": "true"}, {"command": "tdsCoder.clearCache", "when": "true"}], "editor/context": [{"command": "tdsCoder.toggleInlineCompletion", "group": "tdsCoder", "when": "editorTextFocus"}]}, "views": {"explorer": [{"id": "tdsCoderView", "name": "TDS Coder", "when": "true"}]}, "viewsContainers": {"activitybar": [{"id": "tdsCoder", "title": "TDS Coder", "icon": "$(robot)"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0", "eventsource": "^2.0.2"}}