import * as vscode from 'vscode';

export class StatusBarManager implements vscode.Disposable {
    private statusBarItem: vscode.StatusBarItem;

    constructor() {
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            100
        );
        this.statusBarItem.show();
    }

    updateStatus(isAuthenticated: boolean, additionalInfo?: string): void {
        if (isAuthenticated) {
            this.statusBarItem.text = '$(robot) TDS Coder';
            this.statusBarItem.tooltip = 'TDS Coder is active and ready';
            this.statusBarItem.backgroundColor = undefined;
            this.statusBarItem.command = 'tdsCoder.showUsage';
        } else {
            this.statusBarItem.text = '$(robot) TDS Coder (Not authenticated)';
            this.statusBarItem.tooltip = 'Click to authenticate with TDS Coder';
            this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            this.statusBarItem.command = 'tdsCoder.authenticate';
        }

        if (additionalInfo) {
            this.statusBarItem.tooltip += ` - ${additionalInfo}`;
        }
    }

    showProgress(message: string): void {
        this.statusBarItem.text = `$(loading~spin) ${message}`;
        this.statusBarItem.tooltip = message;
    }

    showError(message: string): void {
        this.statusBarItem.text = '$(error) TDS Coder Error';
        this.statusBarItem.tooltip = message;
        this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
    }

    dispose(): void {
        this.statusBarItem.dispose();
    }
}
