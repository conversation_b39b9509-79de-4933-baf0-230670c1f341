import * as vscode from 'vscode';
import { TDSCoderProvider } from './providers/completionProvider';
import { TDSCoderChatProvider } from './providers/chatProvider';
import { TDSCoderViewProvider } from './providers/viewProvider';
import { AuthenticationManager } from './auth/authManager';
import { ConfigurationManager } from './config/configManager';
import { TelemetryManager } from './telemetry/telemetryManager';
import { CacheManager } from './cache/cacheManager';
import { StatusBarManager } from './ui/statusBarManager';

let completionProvider: TDSCoderProvider;
let chatProvider: TDSCoderChatProvider;
let viewProvider: TDSCoderViewProvider;
let authManager: AuthenticationManager;
let configManager: ConfigurationManager;
let telemetryManager: TelemetryManager;
let cacheManager: CacheManager;
let statusBarManager: StatusBarManager;

export function activate(context: vscode.ExtensionContext) {
    console.log('TDS Coder extension is now active!');

    // Initialize managers
    configManager = new ConfigurationManager();
    authManager = new AuthenticationManager(context, configManager);
    telemetryManager = new TelemetryManager(configManager);
    cacheManager = new CacheManager(context, configManager);
    statusBarManager = new StatusBarManager();

    // Initialize providers
    completionProvider = new TDSCoderProvider(
        authManager,
        configManager,
        telemetryManager,
        cacheManager
    );
    
    chatProvider = new TDSCoderChatProvider(
        authManager,
        configManager,
        telemetryManager
    );
    
    viewProvider = new TDSCoderViewProvider(
        context,
        authManager,
        configManager,
        telemetryManager
    );

    // Register completion provider for all languages
    const completionDisposable = vscode.languages.registerInlineCompletionItemProvider(
        { pattern: '**' },
        completionProvider
    );

    // Register chat provider
    const chatDisposable = vscode.window.registerWebviewViewProvider(
        'tdsCoderView',
        viewProvider
    );

    // Register commands
    const authenticateCommand = vscode.commands.registerCommand(
        'tdsCoder.authenticate',
        async () => {
            await authManager.authenticate();
            statusBarManager.updateStatus(authManager.isAuthenticated());
        }
    );

    const logoutCommand = vscode.commands.registerCommand(
        'tdsCoder.logout',
        async () => {
            await authManager.logout();
            statusBarManager.updateStatus(false);
            vscode.window.showInformationMessage('Logged out from TDS Coder');
        }
    );

    const openSettingsCommand = vscode.commands.registerCommand(
        'tdsCoder.openSettings',
        () => {
            vscode.commands.executeCommand(
                'workbench.action.openSettings',
                'tdsCoder'
            );
        }
    );

    const toggleInlineCompletionCommand = vscode.commands.registerCommand(
        'tdsCoder.toggleInlineCompletion',
        async () => {
            const currentSetting = configManager.getEnableInlineCompletion();
            await configManager.updateSetting('enableInlineCompletion', !currentSetting);
            
            const status = !currentSetting ? 'enabled' : 'disabled';
            vscode.window.showInformationMessage(`TDS Coder inline completion ${status}`);
            statusBarManager.updateStatus(authManager.isAuthenticated());
        }
    );

    const showUsageCommand = vscode.commands.registerCommand(
        'tdsCoder.showUsage',
        async () => {
            const usage = await telemetryManager.getUsageStats();
            const message = `TDS Coder Usage:\nCompletions: ${usage.completions}\nChat messages: ${usage.chatMessages}\nTokens used: ${usage.tokensUsed}`;
            vscode.window.showInformationMessage(message);
        }
    );

    const clearCacheCommand = vscode.commands.registerCommand(
        'tdsCoder.clearCache',
        async () => {
            await cacheManager.clearCache();
            vscode.window.showInformationMessage('TDS Coder cache cleared');
        }
    );

    // Register configuration change listener
    const configChangeDisposable = vscode.workspace.onDidChangeConfiguration(
        (event) => {
            if (event.affectsConfiguration('tdsCoder')) {
                configManager.reloadConfiguration();
                statusBarManager.updateStatus(authManager.isAuthenticated());
            }
        }
    );

    // Register text document change listener for telemetry
    const textChangeDisposable = vscode.workspace.onDidChangeTextDocument(
        (event) => {
            if (configManager.getEnableTelemetry()) {
                telemetryManager.trackTextChange(event);
            }
        }
    );

    // Add all disposables to context
    context.subscriptions.push(
        completionDisposable,
        chatDisposable,
        authenticateCommand,
        logoutCommand,
        openSettingsCommand,
        toggleInlineCompletionCommand,
        showUsageCommand,
        clearCacheCommand,
        configChangeDisposable,
        textChangeDisposable,
        statusBarManager
    );

    // Initialize status bar
    statusBarManager.updateStatus(authManager.isAuthenticated());

    // Check authentication on startup
    if (authManager.isAuthenticated()) {
        vscode.window.showInformationMessage('TDS Coder is ready!');
    } else {
        vscode.window.showInformationMessage(
            'Welcome to TDS Coder! Please authenticate to start using AI completions.',
            'Authenticate'
        ).then(selection => {
            if (selection === 'Authenticate') {
                vscode.commands.executeCommand('tdsCoder.authenticate');
            }
        });
    }

    // Send activation telemetry
    if (configManager.getEnableTelemetry()) {
        telemetryManager.trackEvent('extension_activated', {
            version: context.extension.packageJSON.version,
            vscodeVersion: vscode.version
        });
    }
}

export function deactivate() {
    console.log('TDS Coder extension is now deactivated');
    
    // Clean up resources
    if (telemetryManager) {
        telemetryManager.dispose();
    }
    
    if (cacheManager) {
        cacheManager.dispose();
    }
}
