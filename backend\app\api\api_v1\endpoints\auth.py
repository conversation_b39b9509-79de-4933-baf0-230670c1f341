"""
Authentication endpoints for user registration, login, and token management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any

from app.db.session import get_db
from app.schemas.user import (
    UserCreate,
    UserLogin,
    UserPasswordReset,
    UserPasswordResetConfirm,
    UserEmailVerification,
    TokenResponse,
    RefreshTokenRequest,
    UserResponse,
)
from app.services.auth import AuthService
from app.services.user import UserService
from app.core.logging import get_logger

router = APIRouter()
security = HTTPBearer()
logger = get_logger(__name__)


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Register a new user account.
    
    - Creates a new user with email verification
    - Sends verification email
    - Returns user data (without sensitive information)
    """
    logger.info("User registration attempt", email=user_data.email)
    
    try:
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        # Check if user already exists
        existing_user = await user_service.get_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )
        
        # Create user
        user = await user_service.create(user_data)
        
        # Send verification email
        await auth_service.send_verification_email(
            user.email,
            user.email_verification_token,
            request.client.host if request.client else None
        )
        
        logger.info("User registered successfully", user_id=str(user.id), email=user.email)
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Registration failed", error=str(e), email=user_data.email)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    User login with email and password.
    
    - Validates credentials
    - Checks 2FA if enabled
    - Returns JWT tokens
    """
    logger.info("Login attempt", email=login_data.email)
    
    try:
        auth_service = AuthService(db)
        
        # Authenticate user
        user = await auth_service.authenticate(
            email=login_data.email,
            password=login_data.password,
            totp_code=login_data.totp_code,
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        if not user.is_verified:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Email not verified"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is disabled"
            )
        
        # Generate tokens
        tokens = await auth_service.create_tokens(user)
        
        # Update last login
        await auth_service.update_last_login(
            user,
            request.client.host if request.client else None
        )
        
        logger.info("Login successful", user_id=str(user.id), email=user.email)
        
        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type="bearer",
            expires_in=tokens["expires_in"],
            user=user,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Login failed", error=str(e), email=login_data.email)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Refresh access token using refresh token.
    """
    try:
        auth_service = AuthService(db)
        
        # Validate and refresh tokens
        tokens = await auth_service.refresh_tokens(refresh_data.refresh_token)
        
        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Get user data
        user = await auth_service.get_user_from_token(tokens["access_token"])
        
        return TokenResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type="bearer",
            expires_in=tokens["expires_in"],
            user=user,
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout(
    token: str = Depends(security),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Logout user and invalidate tokens.
    """
    try:
        auth_service = AuthService(db)
        
        # Invalidate tokens
        await auth_service.logout(token.credentials)
        
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        logger.error("Logout failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/verify-email")
async def verify_email(
    verification_data: UserEmailVerification,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Verify user email address using verification token.
    """
    try:
        auth_service = AuthService(db)
        
        # Verify email
        success = await auth_service.verify_email(verification_data.token)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification token"
            )
        
        return {"message": "Email verified successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Email verification failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )


@router.post("/forgot-password")
async def forgot_password(
    reset_data: UserPasswordReset,
    request: Request,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Request password reset email.
    """
    try:
        auth_service = AuthService(db)
        
        # Send password reset email
        await auth_service.send_password_reset_email(
            reset_data.email,
            request.client.host if request.client else None
        )
        
        # Always return success to prevent email enumeration
        return {"message": "Password reset email sent if account exists"}
        
    except Exception as e:
        logger.error("Password reset request failed", error=str(e))
        # Still return success to prevent email enumeration
        return {"message": "Password reset email sent if account exists"}


@router.post("/reset-password")
async def reset_password(
    reset_data: UserPasswordResetConfirm,
    db: AsyncSession = Depends(get_db),
) -> Any:
    """
    Reset password using reset token.
    """
    try:
        auth_service = AuthService(db)
        
        # Reset password
        success = await auth_service.reset_password(
            reset_data.token,
            reset_data.new_password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        return {"message": "Password reset successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Password reset failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )
