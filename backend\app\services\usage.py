"""
Usage tracking service for API requests and analytics.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, text
import json
import uuid

from app.models.usage import Usage
from app.models.user import User
from app.models.api_key import APIKey
from app.core.logging import get_logger

logger = get_logger(__name__)


class UsageService:
    """Service for tracking and analyzing API usage."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def track_request(
        self,
        user: User,
        api_key: Optional[APIKey] = None,
        endpoint: str = "",
        method: str = "GET",
        request_id: Optional[str] = None,
        response_time_ms: Optional[int] = None,
        request_size_bytes: Optional[int] = None,
        response_size_bytes: Optional[int] = None,
        status_code: int = 200,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        referer: Optional[str] = None,
        model_used: Optional[str] = None,
        tokens_input: Optional[int] = None,
        tokens_output: Optional[int] = None,
        completion_type: Optional[str] = None,
        cost_cents: Optional[int] = None,
        credits_used: Optional[float] = None,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None,
        rate_limit_hit: bool = False,
        quota_exceeded: bool = False,
        country_code: Optional[str] = None,
        region: Optional[str] = None,
        city: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Usage:
        """
        Track an API request for usage analytics.
        
        Args:
            user: User making the request
            api_key: API key used (if any)
            endpoint: API endpoint called
            method: HTTP method
            request_id: Unique request identifier
            response_time_ms: Response time in milliseconds
            request_size_bytes: Request payload size
            response_size_bytes: Response payload size
            status_code: HTTP status code
            client_ip: Client IP address
            user_agent: User agent string
            referer: Referer header
            model_used: AI model used for completion
            tokens_input: Input tokens consumed
            tokens_output: Output tokens generated
            completion_type: Type of completion (code, chat, etc.)
            cost_cents: Cost in cents
            credits_used: Credits consumed
            error_code: Error code if request failed
            error_message: Error message if request failed
            rate_limit_hit: Whether rate limit was hit
            quota_exceeded: Whether quota was exceeded
            country_code: Country code from IP geolocation
            region: Region from IP geolocation
            city: City from IP geolocation
            metadata: Additional metadata as JSON
            
        Returns:
            Created Usage object
        """
        try:
            usage = Usage(
                user_id=user.id,
                api_key_id=api_key.id if api_key else None,
                endpoint=endpoint,
                method=method,
                request_id=request_id or str(uuid.uuid4()),
                timestamp=datetime.utcnow(),
                response_time_ms=response_time_ms,
                request_size_bytes=request_size_bytes,
                response_size_bytes=response_size_bytes,
                status_code=status_code,
                client_ip=client_ip,
                user_agent=user_agent,
                referer=referer,
                model_used=model_used,
                tokens_input=tokens_input,
                tokens_output=tokens_output,
                completion_type=completion_type,
                cost_cents=cost_cents,
                credits_used=credits_used,
                error_code=error_code,
                error_message=error_message,
                rate_limit_hit=rate_limit_hit,
                quota_exceeded=quota_exceeded,
                country_code=country_code,
                region=region,
                city=city,
                metadata=json.dumps(metadata) if metadata else None,
            )
            
            self.db.add(usage)
            await self.db.commit()
            await self.db.refresh(usage)
            
            logger.debug("Usage tracked", 
                        usage_id=str(usage.id),
                        user_id=str(user.id),
                        endpoint=endpoint,
                        status_code=status_code)
            
            return usage
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to track usage", error=str(e), user_id=str(user.id))
            raise
    
    async def get_user_usage_stats(
        self, 
        user: User, 
        days: int = 30
    ) -> Dict[str, Any]:
        """Get usage statistics for a user."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Total requests
            total_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.is_deleted == False
                )
            )
            total_result = await self.db.execute(total_stmt)
            total_requests = total_result.scalar() or 0
            
            # Successful requests
            success_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            success_result = await self.db.execute(success_stmt)
            successful_requests = success_result.scalar() or 0
            
            # Error requests
            error_requests = total_requests - successful_requests
            
            # Total tokens
            tokens_stmt = select(
                func.sum(Usage.tokens_input),
                func.sum(Usage.tokens_output)
            ).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            tokens_result = await self.db.execute(tokens_stmt)
            tokens_input, tokens_output = tokens_result.first() or (0, 0)
            
            # Total cost
            cost_stmt = select(func.sum(Usage.cost_cents)).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            cost_result = await self.db.execute(cost_stmt)
            total_cost_cents = cost_result.scalar() or 0
            
            # Average response time
            avg_time_stmt = select(func.avg(Usage.response_time_ms)).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.response_time_ms.isnot(None),
                    Usage.is_deleted == False
                )
            )
            avg_time_result = await self.db.execute(avg_time_stmt)
            avg_response_time = avg_time_result.scalar() or 0
            
            # Most used endpoints
            endpoint_stmt = select(
                Usage.endpoint,
                func.count(Usage.id).label('count')
            ).where(
                and_(
                    Usage.user_id == user.id,
                    Usage.timestamp >= start_date,
                    Usage.is_deleted == False
                )
            ).group_by(Usage.endpoint).order_by(desc('count')).limit(10)
            
            endpoint_result = await self.db.execute(endpoint_stmt)
            top_endpoints = [
                {"endpoint": row.endpoint, "count": row.count}
                for row in endpoint_result
            ]
            
            return {
                "period_days": days,
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "error_requests": error_requests,
                "success_rate": (successful_requests / total_requests * 100) if total_requests > 0 else 0,
                "total_tokens_input": int(tokens_input or 0),
                "total_tokens_output": int(tokens_output or 0),
                "total_tokens": int((tokens_input or 0) + (tokens_output or 0)),
                "total_cost_cents": int(total_cost_cents),
                "total_cost_dollars": round(total_cost_cents / 100, 2),
                "average_response_time_ms": round(float(avg_response_time), 2) if avg_response_time else 0,
                "top_endpoints": top_endpoints,
            }
            
        except Exception as e:
            logger.error("Failed to get usage stats", error=str(e), user_id=str(user.id))
            raise
    
    async def get_daily_usage(
        self, 
        user: User, 
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """Get daily usage breakdown for a user."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Daily usage query
            daily_stmt = text("""
                SELECT 
                    DATE(timestamp) as date,
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN status_code < 400 THEN 1 END) as successful_requests,
                    COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_requests,
                    COALESCE(SUM(tokens_input), 0) as tokens_input,
                    COALESCE(SUM(tokens_output), 0) as tokens_output,
                    COALESCE(SUM(cost_cents), 0) as cost_cents,
                    COALESCE(AVG(response_time_ms), 0) as avg_response_time
                FROM usage 
                WHERE user_id = :user_id 
                    AND timestamp >= :start_date 
                    AND is_deleted = false
                GROUP BY DATE(timestamp)
                ORDER BY DATE(timestamp) DESC
            """)
            
            result = await self.db.execute(
                daily_stmt, 
                {"user_id": user.id, "start_date": start_date}
            )
            
            daily_usage = []
            for row in result:
                daily_usage.append({
                    "date": row.date.isoformat(),
                    "total_requests": row.total_requests,
                    "successful_requests": row.successful_requests,
                    "error_requests": row.error_requests,
                    "success_rate": (row.successful_requests / row.total_requests * 100) if row.total_requests > 0 else 0,
                    "tokens_input": int(row.tokens_input),
                    "tokens_output": int(row.tokens_output),
                    "total_tokens": int(row.tokens_input + row.tokens_output),
                    "cost_cents": int(row.cost_cents),
                    "cost_dollars": round(row.cost_cents / 100, 2),
                    "avg_response_time_ms": round(float(row.avg_response_time), 2),
                })
            
            return daily_usage
            
        except Exception as e:
            logger.error("Failed to get daily usage", error=str(e), user_id=str(user.id))
            raise
    
    async def get_quota_usage(self, user: User, date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get current quota usage for a user."""
        try:
            if not date:
                date = datetime.utcnow().date()
            
            # Get user's subscription for quota limits
            from app.services.api_key import APIKeyService
            api_key_service = APIKeyService(self.db)
            subscription = await api_key_service.get_user_subscription(user)
            
            # Count today's successful requests
            usage_stmt = select(func.count(Usage.id)).where(
                and_(
                    Usage.user_id == user.id,
                    func.date(Usage.timestamp) == date,
                    Usage.status_code < 400,
                    Usage.is_deleted == False
                )
            )
            usage_result = await self.db.execute(usage_stmt)
            current_usage = usage_result.scalar() or 0
            
            daily_limit = subscription.daily_request_limit
            remaining = max(0, daily_limit - current_usage) if daily_limit != -1 else -1
            
            return {
                "date": date.isoformat(),
                "current_usage": current_usage,
                "daily_limit": daily_limit,
                "remaining": remaining,
                "percentage_used": (current_usage / daily_limit * 100) if daily_limit > 0 else 0,
                "quota_exceeded": daily_limit != -1 and current_usage >= daily_limit,
                "subscription_tier": subscription.tier.value,
            }
            
        except Exception as e:
            logger.error("Failed to get quota usage", error=str(e), user_id=str(user.id))
            raise
