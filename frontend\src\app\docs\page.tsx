'use client'

import Link from 'next/link'
import { 
  RocketLaunchIcon,
  KeyIcon,
  CodeBracketIcon,
  CogIcon,
  BookOpenIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline'

const steps = [
  {
    title: 'Create Your Account',
    description: 'Sign up for a free TDS Coder account to get started.',
    icon: RocketLaunchIcon,
    details: [
      'Visit the registration page',
      'Enter your email and create a password',
      'Verify your email address',
      'Complete your profile setup'
    ]
  },
  {
    title: 'Generate API Key',
    description: 'Create an API key to authenticate your requests.',
    icon: KeyIcon,
    details: [
      'Go to your dashboard',
      'Navigate to API Keys section',
      'Click "Create New API Key"',
      'Copy and securely store your key'
    ]
  },
  {
    title: 'Install Extension',
    description: 'Install the TDS Coder extension for your favorite editor.',
    icon: CodeBracketIcon,
    details: [
      'Open your editor\'s extension marketplace',
      'Search for "TDS Coder"',
      'Install the official extension',
      'Restart your editor if required'
    ]
  },
  {
    title: 'Configure Settings',
    description: 'Set up your API key and customize your preferences.',
    icon: CogIcon,
    details: [
      'Open extension settings',
      'Enter your API key',
      'Configure completion preferences',
      'Set up keyboard shortcuts'
    ]
  }
]

const codeExamples = [
  {
    title: 'REST API Example',
    language: 'curl',
    code: `curl -X POST https://api.tdscoder.com/v1/completions \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "prompt": "def fibonacci(n):",
    "language": "python",
    "max_tokens": 100
  }'`
  },
  {
    title: 'Python SDK Example',
    language: 'python',
    code: `import tdscoder

client = tdscoder.Client(api_key="YOUR_API_KEY")

completion = client.completions.create(
    prompt="def fibonacci(n):",
    language="python",
    max_tokens=100
)

print(completion.choices[0].text)`
  },
  {
    title: 'JavaScript SDK Example',
    language: 'javascript',
    code: `import TDSCoder from 'tdscoder';

const client = new TDSCoder({
  apiKey: 'YOUR_API_KEY'
});

const completion = await client.completions.create({
  prompt: 'function fibonacci(n) {',
  language: 'javascript',
  maxTokens: 100
});

console.log(completion.choices[0].text);`
  }
]

const faqs = [
  {
    question: 'How do I get started with TDS Coder?',
    answer: 'Simply sign up for a free account, generate an API key, and install our extension for your favorite editor. You\'ll be coding with AI assistance in minutes!'
  },
  {
    question: 'Which editors are supported?',
    answer: 'We support VS Code, JetBrains IDEs (IntelliJ, PyCharm, WebStorm), Vim/Neovim, Sublime Text, and more. Check our integrations page for the full list.'
  },
  {
    question: 'Is there a free tier?',
    answer: 'Yes! Our free tier includes 100 completions per day, which is perfect for trying out TDS Coder and light usage.'
  },
  {
    question: 'How accurate are the code suggestions?',
    answer: 'Our AI models are trained on millions of code repositories and achieve high accuracy. The suggestions improve as you use the service and provide feedback.'
  },
  {
    question: 'Can I use TDS Coder offline?',
    answer: 'Our Solo and Team plans include offline mode for basic completions. However, the most advanced features require an internet connection.'
  }
]

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Link href="/" className="text-xl font-bold text-gray-900 dark:text-white">
              TDS Coder
            </Link>
            <div className="space-x-4">
              <Link href="/features" className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                Features
              </Link>
              <Link href="/pricing" className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                Pricing
              </Link>
              <Link href="/auth/login" className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                Sign In
              </Link>
              <Link href="/auth/register" className="btn-primary px-4 py-2 rounded-md">
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-20">
            <BookOpenIcon className="mx-auto h-16 w-16 text-indigo-600 mb-6" />
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl">
              Getting Started with TDS Coder
            </h1>
            <p className="mt-4 text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Follow this guide to set up TDS Coder and start coding with AI assistance in just a few minutes.
            </p>
          </div>

          {/* Steps Section */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-12 text-center">
              Quick Setup Guide
            </h2>
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
              {steps.map((step, index) => (
                <div key={step.title} className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-sm border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-md bg-indigo-500 text-white mr-4">
                      <step.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">
                        Step {index + 1}
                      </span>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {step.title}
                      </h3>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    {step.description}
                  </p>
                  
                  <ul className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start text-sm text-gray-500 dark:text-gray-400">
                        <span className="inline-block w-6 h-6 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400 text-xs font-medium flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                          {detailIndex + 1}
                        </span>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Code Examples Section */}
          <div className="mb-20">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-12 text-center">
              API Examples
            </h2>
            <div className="space-y-8">
              {codeExamples.map((example) => (
                <div key={example.title} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {example.title}
                    </h3>
                  </div>
                  <div className="p-6">
                    <pre className="bg-gray-900 text-gray-100 p-4 rounded-md overflow-x-auto text-sm">
                      <code>{example.code}</code>
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mb-20">
            <div className="text-center mb-12">
              <QuestionMarkCircleIcon className="mx-auto h-12 w-12 text-indigo-600 mb-4" />
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                Frequently Asked Questions
              </h2>
            </div>
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
              {faqs.map((faq, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="text-center bg-indigo-600 rounded-lg p-12">
            <h2 className="text-3xl font-bold text-white">
              Ready to start coding with AI?
            </h2>
            <p className="mt-4 text-lg text-indigo-100">
              Create your free account and get your first API key in under 2 minutes.
            </p>
            <div className="mt-8 space-x-4">
              <Link href="/auth/register" className="inline-block bg-white text-indigo-600 px-6 py-3 rounded-md font-medium hover:bg-gray-100">
                Get Started Free
              </Link>
              <Link href="/contact" className="inline-block border border-white text-white px-6 py-3 rounded-md font-medium hover:bg-indigo-700">
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
