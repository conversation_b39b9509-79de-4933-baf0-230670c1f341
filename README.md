# TDS_Coder: Full-Stack SaaS Platform for Refact AI Integration

## Project Overview
TDS_Coder is a comprehensive SaaS platform that integrates the open-source Refact AI coding assistant, serving freelancers, development teams, and enterprises with tiered subscription plans.

## Technology Stack
- **Frontend**: React with Next.js 14+ (App Router)
- **Backend**: FastAPI (Python 3.11+) with async/await
- **Database**: PostgreSQL 15+ with proper indexing
- **Caching**: Redis for sessions and API responses
- **Authentication**: JWT with refresh token rotation
- **Payment**: Stripe integration for subscriptions
- **Containerization**: Docker with docker-compose

## Project Structure
```
TDS_AICoder/
├── frontend/          # Next.js React application
├── backend/           # FastAPI Python application
├── database/          # PostgreSQL schema and migrations
├── docker/            # Docker configurations
├── docs/              # Documentation
├── scripts/           # Utility scripts
└── docker-compose.yml # Development environment
```

## Subscription Tiers
1. **Free Tier**: 100 API requests/day, basic code completion
2. **Solo Plan**: $19/month, 10,000 requests/day, advanced features
3. **Team Plan**: $49/month, 50,000 requests/day, 5 seats, team dashboard
4. **Enterprise**: Custom pricing, unlimited requests, dedicated support

## Getting Started

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)

### Development Setup
1. Clone the repository
2. Copy environment files: `cp .env.example .env`
3. Start services: `docker-compose up -d`
4. Run migrations: `docker-compose exec backend alembic upgrade head`
5. Access the application at http://localhost:3000

## Development Phases
- **Phase 1**: Core Platform Foundation (Weeks 1-4)
- **Phase 2**: Refact AI Integration (Weeks 5-8)
- **Phase 3**: Advanced Dashboard & Admin Panel (Weeks 9-12)
- **Phase 4**: IDE Plugin Integration (Weeks 13-16)
- **Phase 5**: Advanced Features & Deployment (Weeks 17-20)

## Security Features
- HTTPS enforcement with HSTS headers
- JWT authentication with refresh token rotation
- SQL injection prevention
- XSS protection with CSP
- CSRF protection
- Rate limiting
- Input validation and sanitization

## License
MIT License - see LICENSE file for details
