"""
Email template functions for generating HTML and text content.
"""

from typing import Dict, Any
from pathlib import Path
import os

# Get the directory containing this file
TEMPLATE_DIR = Path(__file__).parent


def render_template(template_name: str, context: Dict[str, Any]) -> str:
    """
    Render an email template with the given context.
    
    Args:
        template_name: Name of the template file
        context: Template context variables
        
    Returns:
        Rendered HTML content
    """
    template_path = TEMPLATE_DIR / template_name
    
    if not template_path.exists():
        raise FileNotFoundError(f"Template {template_name} not found")
    
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # Simple template rendering (replace {{ variable }} with values)
    for key, value in context.items():
        placeholder = f"{{{{ {key} }}}}"
        template_content = template_content.replace(placeholder, str(value))
    
    return template_content


def get_verification_email_template(
    user_name: str,
    verification_url: str,
    expires_hours: int = 24
) -> Dict[str, str]:
    """
    Generate email verification template.
    
    Args:
        user_name: User's name
        verification_url: Email verification URL
        expires_hours: Token expiration in hours
        
    Returns:
        Dictionary with 'html' and 'text' content
    """
    context = {
        'title': 'Verify Your Email - TDS Coder',
        'heading': 'Welcome to TDS Coder!',
        'user_name': user_name,
        'verification_url': verification_url,
        'expires_hours': expires_hours,
        'unsubscribe_url': 'https://tdscoder.com/unsubscribe',
        'content': f"""
        <p>Hi {user_name},</p>
        
        <p>Thank you for signing up for TDS Coder! To get started with AI-powered code completion, 
        please verify your email address by clicking the button below:</p>
        
        <p style="text-align: center;">
            <a href="{verification_url}" class="button">Verify Email Address</a>
        </p>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><span class="code">{verification_url}</span></p>
        
        <div class="security-notice">
            <p><strong>Security Notice:</strong> This verification link will expire in {expires_hours} hours. 
            If you didn't create an account with TDS Coder, please ignore this email.</p>
        </div>
        
        <p>Once verified, you'll be able to:</p>
        <ul>
            <li>Generate API keys for your applications</li>
            <li>Access AI-powered code completion</li>
            <li>Track your usage and analytics</li>
            <li>Upgrade to premium plans for more features</li>
        </ul>
        
        <p>Welcome to the future of coding!</p>
        """
    }
    
    html_content = render_template('email_base.html', context)
    
    text_content = f"""
TDS Coder - Verify Your Email

Hi {user_name},

Thank you for signing up for TDS Coder! To get started with AI-powered code completion, 
please verify your email address by visiting the following link:

{verification_url}

This verification link will expire in {expires_hours} hours. If you didn't create an 
account with TDS Coder, please ignore this email.

Once verified, you'll be able to:
- Generate API keys for your applications
- Access AI-powered code completion
- Track your usage and analytics
- Upgrade to premium plans for more features

Welcome to the future of coding!

---
TDS Coder Team
https://tdscoder.com

If you have any questions, contact <NAME_EMAIL>
    """
    
    return {
        'html': html_content,
        'text': text_content.strip()
    }


def get_password_reset_email_template(
    user_name: str,
    reset_url: str,
    expires_minutes: int = 15
) -> Dict[str, str]:
    """
    Generate password reset template.
    
    Args:
        user_name: User's name
        reset_url: Password reset URL
        expires_minutes: Token expiration in minutes
        
    Returns:
        Dictionary with 'html' and 'text' content
    """
    context = {
        'title': 'Reset Your Password - TDS Coder',
        'heading': 'Password Reset Request',
        'user_name': user_name,
        'reset_url': reset_url,
        'expires_minutes': expires_minutes,
        'unsubscribe_url': 'https://tdscoder.com/unsubscribe',
        'content': f"""
        <p>Hi {user_name},</p>
        
        <p>We received a request to reset your password for your TDS Coder account. 
        If you made this request, click the button below to reset your password:</p>
        
        <p style="text-align: center;">
            <a href="{reset_url}" class="button">Reset Password</a>
        </p>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><span class="code">{reset_url}</span></p>
        
        <div class="security-notice">
            <p><strong>Security Notice:</strong> This reset link will expire in {expires_minutes} minutes. 
            If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
        </div>
        
        <p>For your security, this link can only be used once. If you need to reset your password again, 
        please request a new reset link.</p>
        
        <p>If you're having trouble accessing your account, please contact our support team.</p>
        """
    }
    
    html_content = render_template('email_base.html', context)
    
    text_content = f"""
TDS Coder - Password Reset Request

Hi {user_name},

We received a request to reset your password for your TDS Coder account. 
If you made this request, visit the following link to reset your password:

{reset_url}

This reset link will expire in {expires_minutes} minutes. If you didn't request 
a password reset, please ignore this email and your password will remain unchanged.

For your security, this link can only be used once. If you need to reset your 
password again, please request a new reset link.

If you're having trouble accessing your account, please contact our support team.

---
TDS Coder Team
https://tdscoder.com

Support: <EMAIL>
    """
    
    return {
        'html': html_content,
        'text': text_content.strip()
    }


def get_2fa_enabled_email_template(user_name: str) -> Dict[str, str]:
    """
    Generate 2FA enabled notification template.
    
    Args:
        user_name: User's name
        
    Returns:
        Dictionary with 'html' and 'text' content
    """
    context = {
        'title': '2FA Enabled - TDS Coder',
        'heading': 'Two-Factor Authentication Enabled',
        'user_name': user_name,
        'unsubscribe_url': 'https://tdscoder.com/unsubscribe',
        'content': f"""
        <p>Hi {user_name},</p>
        
        <p>Two-factor authentication (2FA) has been successfully enabled on your TDS Coder account. 
        This adds an extra layer of security to protect your account.</p>
        
        <p>From now on, you'll need to provide both your password and a 6-digit code from your 
        authenticator app when signing in.</p>
        
        <div class="security-notice">
            <p><strong>Security Tip:</strong> Keep your backup codes in a safe place. You can use them 
            to access your account if you lose access to your authenticator app.</p>
        </div>
        
        <p>If you didn't enable 2FA on your account, please contact our support team immediately.</p>
        """
    }
    
    html_content = render_template('email_base.html', context)
    
    text_content = f"""
TDS Coder - Two-Factor Authentication Enabled

Hi {user_name},

Two-factor authentication (2FA) has been successfully enabled on your TDS Coder account. 
This adds an extra layer of security to protect your account.

From now on, you'll need to provide both your password and a 6-digit code from your 
authenticator app when signing in.

Security Tip: Keep your backup codes in a safe place. You can use them to access 
your account if you lose access to your authenticator app.

If you didn't enable 2FA on your account, please contact our support team immediately.

---
TDS Coder Team
https://tdscoder.com

Support: <EMAIL>
    """
    
    return {
        'html': html_content,
        'text': text_content.strip()
    }
