"""
Additional AI provider implementations for Google, Cohere, Meta, Qwen, and StarCoder.
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, AsyncGenerator
import httpx

from app.core.logging import get_logger
from app.services.ai_models import ModelProvider

logger = get_logger(__name__)


class GoogleAIProvider:
    """Google AI (Gemini) provider implementation."""
    
    def __init__(self, client: httpx.AsyncClient):
        self.client = client
    
    async def create_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Google AI API."""
        try:
            # Map model names to Google AI format
            model_mapping = {
                "gemini-1.5-pro": "models/gemini-1.5-pro-latest",
                "gemini-1.5-flash": "models/gemini-1.5-flash-latest",
                "codegemma-7b": "models/codegemma-7b-it",
            }
            
            api_model = model_mapping.get(model, model)
            
            payload = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "maxOutputTokens": max_tokens,
                    "temperature": temperature,
                }
            }
            
            endpoint = f"/{api_model}:generateContent"
            if stream:
                endpoint = f"/{api_model}:streamGenerateContent"
            
            response = await self.client.post(endpoint, json=payload)
            response.raise_for_status()
            data = response.json()
            
            if stream:
                return data
            
            content = data.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "")
            
            return {
                "text": content,
                "finish_reason": "stop",
                "provider": ModelProvider.GOOGLE,
            }
            
        except Exception as e:
            logger.error("Google AI completion failed", error=str(e), model=model)
            raise
    
    async def stream_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Google AI API."""
        try:
            model_mapping = {
                "gemini-1.5-pro": "models/gemini-1.5-pro-latest",
                "gemini-1.5-flash": "models/gemini-1.5-flash-latest",
                "codegemma-7b": "models/codegemma-7b-it",
            }
            
            api_model = model_mapping.get(model, model)
            
            payload = {
                "contents": [{"parts": [{"text": prompt}]}],
                "generationConfig": {
                    "maxOutputTokens": max_tokens,
                    "temperature": temperature,
                }
            }
            
            async with self.client.stream("POST", f"/{api_model}:streamGenerateContent", json=payload) as response:
                response.raise_for_status()
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            content = data.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "")
                            if content:
                                yield {
                                    "text": content,
                                    "finish_reason": None,
                                    "provider": ModelProvider.GOOGLE,
                                }
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error("Google AI streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}


class CohereProvider:
    """Cohere provider implementation."""
    
    def __init__(self, client: httpx.AsyncClient):
        self.client = client
    
    async def create_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Cohere API."""
        try:
            payload = {
                "model": model,
                "message": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }
            
            response = await self.client.post("/v1/chat", json=payload)
            response.raise_for_status()
            data = response.json()
            
            if stream:
                return data
            
            return {
                "text": data.get("text", ""),
                "finish_reason": data.get("finish_reason", "stop"),
                "provider": ModelProvider.COHERE,
            }
            
        except Exception as e:
            logger.error("Cohere completion failed", error=str(e), model=model)
            raise
    
    async def stream_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Cohere API."""
        try:
            payload = {
                "model": model,
                "message": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True,
            }
            
            async with self.client.stream("POST", "/v1/chat", json=payload) as response:
                response.raise_for_status()
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if data.get("text"):
                                yield {
                                    "text": data["text"],
                                    "finish_reason": data.get("finish_reason"),
                                    "provider": ModelProvider.COHERE,
                                }
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error("Cohere streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}


class MetaProvider:
    """Meta/Llama provider implementation (via Together AI)."""
    
    def __init__(self, client: httpx.AsyncClient):
        self.client = client
    
    async def create_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Meta/Llama models via Together AI."""
        try:
            # Map model names to Together AI format
            model_mapping = {
                "llama-3-70b-instruct": "meta-llama/Llama-3-70b-chat-hf",
                "code-llama-34b": "codellama/CodeLlama-34b-Instruct-hf",
            }
            
            api_model = model_mapping.get(model, model)
            
            payload = {
                "model": api_model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }
            
            response = await self.client.post("/chat/completions", json=payload)
            response.raise_for_status()
            data = response.json()
            
            if stream:
                return data
            
            return {
                "text": data["choices"][0]["message"]["content"],
                "finish_reason": data["choices"][0]["finish_reason"],
                "provider": ModelProvider.META,
            }
            
        except Exception as e:
            logger.error("Meta completion failed", error=str(e), model=model)
            raise
    
    async def stream_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream completion using Meta/Llama models."""
        try:
            model_mapping = {
                "llama-3-70b-instruct": "meta-llama/Llama-3-70b-chat-hf",
                "code-llama-34b": "codellama/CodeLlama-34b-Instruct-hf",
            }
            
            api_model = model_mapping.get(model, model)
            
            payload = {
                "model": api_model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True,
            }
            
            async with self.client.stream("POST", "/chat/completions", json=payload) as response:
                response.raise_for_status()
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if data["choices"][0]["delta"].get("content"):
                                yield {
                                    "text": data["choices"][0]["delta"]["content"],
                                    "finish_reason": data["choices"][0]["finish_reason"],
                                    "provider": ModelProvider.META,
                                }
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error("Meta streaming failed", error=str(e), model=model)
            yield {"error": str(e), "type": "stream_error"}


class QwenProvider:
    """Qwen provider implementation (via DashScope)."""
    
    def __init__(self, client: httpx.AsyncClient):
        self.client = client
    
    async def create_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using Qwen models via DashScope."""
        try:
            payload = {
                "model": model,
                "input": {"messages": [{"role": "user", "content": prompt}]},
                "parameters": {
                    "max_tokens": max_tokens,
                    "temperature": temperature,
                    "incremental_output": stream,
                }
            }
            
            response = await self.client.post("/services/aigc/text-generation/generation", json=payload)
            response.raise_for_status()
            data = response.json()
            
            if stream:
                return data
            
            output = data.get("output", {})
            content = output.get("text", "")
            
            return {
                "text": content,
                "finish_reason": output.get("finish_reason", "stop"),
                "provider": ModelProvider.QWEN,
            }
            
        except Exception as e:
            logger.error("Qwen completion failed", error=str(e), model=model)
            raise


class StarCoderProvider:
    """StarCoder provider implementation (via Hugging Face)."""
    
    def __init__(self, client: httpx.AsyncClient):
        self.client = client
    
    async def create_completion(
        self,
        prompt: str,
        model: str,
        max_tokens: int,
        temperature: float,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Create completion using StarCoder models via Hugging Face."""
        try:
            # Map model names to Hugging Face format
            model_mapping = {
                "starcoder2-15b": "bigcode/starcoder2-15b",
                "starcoder2-7b": "bigcode/starcoder2-7b",
            }
            
            api_model = model_mapping.get(model, model)
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_new_tokens": max_tokens,
                    "temperature": temperature,
                    "return_full_text": False,
                }
            }
            
            response = await self.client.post(f"/models/{api_model}", json=payload)
            response.raise_for_status()
            data = response.json()
            
            # Handle both single response and list response formats
            if isinstance(data, list):
                content = data[0].get("generated_text", "")
            else:
                content = data.get("generated_text", "")
            
            return {
                "text": content,
                "finish_reason": "stop",
                "provider": ModelProvider.STARCODER,
            }
            
        except Exception as e:
            logger.error("StarCoder completion failed", error=str(e), model=model)
            raise
