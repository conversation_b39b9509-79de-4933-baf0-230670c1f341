'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'
import { 
  CpuChipIcon,
  SparklesIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'
import { apiHelpers } from '@/lib/api'
import toast from 'react-hot-toast'

interface ModelInfo {
  id: string
  name: string
  provider: string
  description: string
  contextWindow: number
  maxTokens: number
  costPer1kInputTokens: number
  costPer1kOutputTokens: number
  bestFor: string[]
  tier: 'free' | 'solo' | 'team' | 'enterprise'
  isRecommended?: boolean
  supportsStreaming: boolean
  supportsFunctionCalling: boolean
}

const allModels: ModelInfo[] = [
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    description: 'Fast and efficient model for general coding tasks',
    contextWindow: 16385,
    maxTokens: 4096,
    costPer1kInputTokens: 0.0015,
    costPer1kOutputTokens: 0.002,
    bestFor: ['general', 'chat', 'quick_completions'],
    tier: 'free',
    isRecommended: true,
    supportsStreaming: true,
    supportsFunctionCalling: true
  },
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'OpenAI',
    description: 'Balanced performance and cost efficiency',
    contextWindow: 128000,
    maxTokens: 4096,
    costPer1kInputTokens: 0.005,
    costPer1kOutputTokens: 0.015,
    bestFor: ['balanced_performance', 'cost_efficiency', 'general_coding'],
    tier: 'solo',
    supportsStreaming: true,
    supportsFunctionCalling: true
  },
  {
    id: 'claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for coding and reasoning tasks',
    contextWindow: 200000,
    maxTokens: 8192,
    costPer1kInputTokens: 0.003,
    costPer1kOutputTokens: 0.015,
    bestFor: ['coding', 'reasoning', 'analysis'],
    tier: 'team',
    isRecommended: true,
    supportsStreaming: true,
    supportsFunctionCalling: true
  },
  {
    id: 'codestral',
    name: 'Codestral',
    provider: 'Mistral AI',
    description: 'Specialized model for code completion',
    contextWindow: 32768,
    maxTokens: 32768,
    costPer1kInputTokens: 0.001,
    costPer1kOutputTokens: 0.003,
    bestFor: ['code_completion', 'code_generation', 'programming_tasks'],
    tier: 'solo',
    isRecommended: true,
    supportsStreaming: true,
    supportsFunctionCalling: true
  },
  {
    id: 'deepseek-coder-v3',
    name: 'DeepSeek Coder V3',
    provider: 'DeepSeek',
    description: 'Latest coding-focused model with excellent performance',
    contextWindow: 64000,
    maxTokens: 8192,
    costPer1kInputTokens: 0.00014,
    costPer1kOutputTokens: 0.00028,
    bestFor: ['code_completion', 'debugging', 'code_optimization'],
    tier: 'solo',
    isRecommended: true,
    supportsStreaming: true,
    supportsFunctionCalling: true
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    provider: 'Google',
    description: 'Ultra-fast model with massive context window',
    contextWindow: 1000000,
    maxTokens: 8192,
    costPer1kInputTokens: 0.000075,
    costPer1kOutputTokens: 0.0003,
    bestFor: ['fast_completions', 'cost_efficiency', 'high_throughput'],
    tier: 'free',
    supportsStreaming: true,
    supportsFunctionCalling: true
  },
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    provider: 'Google',
    description: 'Advanced model with 2M token context window',
    contextWindow: 2000000,
    maxTokens: 8192,
    costPer1kInputTokens: 0.00125,
    costPer1kOutputTokens: 0.005,
    bestFor: ['large_context', 'document_analysis', 'complex_reasoning'],
    tier: 'team',
    supportsStreaming: true,
    supportsFunctionCalling: true
  }
]

export default function ModelsPage() {
  const { user } = useAuth()
  const [availableModels, setAvailableModels] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [selectedTier, setSelectedTier] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('recommended')

  useEffect(() => {
    fetchAvailableModels()
  }, [])

  const fetchAvailableModels = async () => {
    try {
      setLoading(true)
      const response = await apiHelpers.completions.getModels()
      setAvailableModels(response.models.map((m: any) => m.id))
    } catch (error) {
      console.error('Failed to fetch available models:', error)
      toast.error('Failed to load available models')

      // Fallback to simulated data
      const userTier = user?.subscription?.tier || 'free'
      const tierModels = {
        free: ['gpt-3.5-turbo', 'claude-3-haiku', 'gemini-1.5-flash', 'deepseek-coder-v2', 'mistral-small'],
        solo: ['gpt-3.5-turbo', 'gpt-4o', 'claude-3-sonnet', 'codestral', 'deepseek-coder-v3', 'gemini-1.5-flash'],
        team: ['gpt-4-turbo', 'claude-3.5-sonnet', 'mistral-large-2', 'gemini-1.5-pro', 'command-r-plus'],
        enterprise: allModels.map(m => m.id)
      }
      setAvailableModels(tierModels[userTier as keyof typeof tierModels] || tierModels.free)
    } finally {
      setLoading(false)
    }
  }

  const providers = [...new Set(allModels.map(m => m.provider))]
  const tiers = ['free', 'solo', 'team', 'enterprise']

  const filteredModels = allModels
    .filter(model => {
      if (selectedProvider !== 'all' && model.provider !== selectedProvider) return false
      if (selectedTier !== 'all' && model.tier !== selectedTier) return false
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'recommended':
          if (a.isRecommended && !b.isRecommended) return -1
          if (!a.isRecommended && b.isRecommended) return 1
          return 0
        case 'cost':
          return a.costPer1kInputTokens - b.costPer1kInputTokens
        case 'context':
          return b.contextWindow - a.contextWindow
        case 'name':
          return a.name.localeCompare(b.name)
        default:
          return 0
      }
    })

  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'free': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'solo': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'team': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'enterprise': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
  }

  const formatContextWindow = (tokens: number) => {
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(0)}K`
    return tokens.toString()
  }

  const isModelAvailable = (modelId: string) => availableModels.includes(modelId)

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          AI Models
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Explore and compare available AI models for code completion
        </p>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-wrap gap-4">
        <div className="flex items-center space-x-2">
          <FunnelIcon className="h-5 w-5 text-gray-400" />
          <select
            value={selectedProvider}
            onChange={(e) => setSelectedProvider(e.target.value)}
            className="form-input text-sm"
          >
            <option value="all">All Providers</option>
            {providers.map(provider => (
              <option key={provider} value={provider}>{provider}</option>
            ))}
          </select>
        </div>

        <select
          value={selectedTier}
          onChange={(e) => setSelectedTier(e.target.value)}
          className="form-input text-sm"
        >
          <option value="all">All Tiers</option>
          {tiers.map(tier => (
            <option key={tier} value={tier}>{tier.charAt(0).toUpperCase() + tier.slice(1)}</option>
          ))}
        </select>

        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value)}
          className="form-input text-sm"
        >
          <option value="recommended">Recommended</option>
          <option value="cost">Cost (Low to High)</option>
          <option value="context">Context Window (High to Low)</option>
          <option value="name">Name (A-Z)</option>
        </select>
      </div>

      {/* Models Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredModels.map((model) => {
          const available = isModelAvailable(model.id)
          
          return (
            <div
              key={model.id}
              className={`card relative ${!available ? 'opacity-60' : ''}`}
            >
              <div className="card-content">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {model.name}
                      </h3>
                      {model.isRecommended && (
                        <SparklesIcon className="h-5 w-5 text-yellow-500" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {model.provider}
                    </p>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierBadgeColor(model.tier)}`}>
                      {model.tier}
                    </span>
                    {available ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircleIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {model.description}
                </p>

                {/* Specs */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      Context Window
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formatContextWindow(model.contextWindow)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 flex items-center">
                      <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                      Cost per 1K tokens
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      ${model.costPer1kInputTokens.toFixed(4)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400 flex items-center">
                      <CpuChipIcon className="h-4 w-4 mr-1" />
                      Max Tokens
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {model.maxTokens.toLocaleString()}
                    </span>
                  </div>
                </div>

                {/* Features */}
                <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-4">
                  <div className="flex items-center">
                    {model.supportsStreaming ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <XCircleIcon className="h-3 w-3 text-gray-400 mr-1" />
                    )}
                    Streaming
                  </div>
                  <div className="flex items-center">
                    {model.supportsFunctionCalling ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-500 mr-1" />
                    ) : (
                      <XCircleIcon className="h-3 w-3 text-gray-400 mr-1" />
                    )}
                    Functions
                  </div>
                </div>

                {/* Best For Tags */}
                <div className="flex flex-wrap gap-1">
                  {model.bestFor.slice(0, 3).map((use) => (
                    <span
                      key={use}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200"
                    >
                      {use.replace('_', ' ')}
                    </span>
                  ))}
                  {model.bestFor.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                      +{model.bestFor.length - 3} more
                    </span>
                  )}
                </div>

                {/* Availability Status */}
                {!available && (
                  <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Not available in your current plan.{' '}
                      <a href="/dashboard/billing" className="text-indigo-600 hover:text-indigo-500">
                        Upgrade to access
                      </a>
                    </p>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {filteredModels.length === 0 && (
        <div className="text-center py-12">
          <CpuChipIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No models found
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Try adjusting your filters to see more models.
          </p>
        </div>
      )}
    </div>
  )
}
